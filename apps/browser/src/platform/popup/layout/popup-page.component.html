<ng-content select="[slot=header]"></ng-content>
<main class="tw-flex-1 tw-overflow-hidden tw-flex tw-flex-col tw-relative tw-bg-background-alt">
  <ng-content select="[slot=full-width-notice]"></ng-content>
  <div
    #nonScrollable
    class="tw-transition-colors tw-duration-200 tw-border-0 tw-border-b tw-border-solid tw-p-3 bit-compact:tw-p-2"
    [ngClass]="{
      'tw-invisible !tw-p-0': loading || nonScrollable.childElementCount === 0,
      'tw-border-secondary-300': scrolled(),
      'tw-border-transparent': !scrolled(),
    }"
  >
    <ng-content select="[slot=above-scroll-area]"></ng-content>
  </div>
  <div
    class="tw-max-w-screen-sm tw-mx-auto tw-overflow-y-auto tw-flex tw-flex-col tw-size-full tw-styled-scrollbar"
    data-testid="popup-layout-scroll-region"
    (scroll)="handleScroll($event)"
    [ngClass]="{ 'tw-invisible': loading }"
  >
    <div
      class="tw-max-w-screen-sm tw-mx-auto tw-flex-1 tw-flex tw-flex-col tw-w-full"
      [ngClass]="{ 'tw-p-3 bit-compact:tw-p-2': !disablePadding }"
    >
      <ng-content></ng-content>
    </div>
  </div>
  <span
    class="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center tw-text-main"
    [ngClass]="{ 'tw-invisible': !loading }"
  >
    <i class="bwi bwi-spinner bwi-lg bwi-spin" [attr.aria-label]="loadingText"></i>
  </span>
</main>
<ng-content select="[slot=footer]"></ng-content>
