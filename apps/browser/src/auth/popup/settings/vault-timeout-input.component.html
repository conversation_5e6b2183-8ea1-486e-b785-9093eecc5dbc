<div [formGroup]="form">
  <div class="box-content-row last display-block" appBoxRow>
    <label for="vaultTimeout">{{ "vaultTimeout" | i18n }}</label>
    <select
      id="vaultTimeout"
      name="VaultTimeout"
      formControlName="vaultTimeout"
      class="form-control"
    >
      <option *ngFor="let o of vaultTimeoutOptions" [ngValue]="o.value">{{ o.name }}</option>
    </select>
  </div>
  <div class="box-content-row last" *ngIf="showCustom">
    <div formGroupName="custom" class="row">
      <div class="col">
        <div class="display-block" appBoxRow>
          <label for="customVaultTimeout">{{ "hours" | i18n }}</label>
          <input
            id="hours"
            class="form-control"
            type="number"
            min="0"
            name="hours"
            formControlName="hours"
          />
        </div>
      </div>
      <div class="col">
        <div class="display-block" appBoxRow>
          <label for="customVaultTimeout">{{ "minutes" | i18n }}</label>
          <input
            id="minutes"
            class="form-control"
            type="number"
            min="0"
            max="59"
            name="minutes"
            formControlName="minutes"
          />
        </div>
      </div>
    </div>
  </div>
</div>
