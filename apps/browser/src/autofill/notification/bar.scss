﻿@import "../shared/styles/variables";

body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-size: 14px;
  line-height: 16px;
  font-family: $font-family-sans-serif;

  @include themify($themes) {
    color: themed("textColor");
    background-color: themed("backgroundColor");
  }
}

img {
  margin: 0;
  padding: 0;
  border: 0;
}

button,
select {
  font-size: $font-size-base;
  font-family: $font-family-sans-serif;
}

.outer-wrapper {
  display: block;
  position: relative;
  padding: 8px;
  min-height: 42px;
  border: 1px solid transparent;
  border-bottom: 2px solid transparent;
  border-radius: 4px;
  box-sizing: border-box;

  @include themify($themes) {
    border-color: themed("borderColor");
    border-bottom-color: themed("primaryColor");
  }

  &.success-event {
    @include themify($themes) {
      border-bottom-color: themed("successColor");
    }
  }

  &.error-event {
    @include themify($themes) {
      border-bottom-color: themed("errorColor");
    }
  }
}

.inner-wrapper {
  display: grid;
  grid-template-columns: auto max-content;
}

.outer-wrapper > *,
.inner-wrapper > * {
  align-self: center;
}

#logo {
  width: 24px;
  height: 24px;
  display: block;
}

.logo-wrapper {
  position: absolute;
  top: 8px;
  left: 10px;
  overflow: hidden;
}

#close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  margin-right: 10px;
  padding: 0;

  &:hover {
    @include themify($themes) {
      border-color: rgba(themed("textColor"), 0.2);
      background-color: rgba(themed("textColor"), 0.2);
    }
  }
}

#close {
  display: block;
  width: 16px;
  height: 16px;

  > path {
    @include themify($themes) {
      fill: themed("textColor");
    }
  }
}

.notification-close {
  position: absolute;
  top: 6px;
  right: 6px;
}

#content .inner-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;

  .notification-body {
    width: 100%;
    padding: 4px 38px 24px 42px;
    font-weight: 400;
  }

  .notification-actions {
    display: flex;
    width: 100%;
    align-items: stretch;
    justify-content: flex-end;

    #never-save {
      margin-right: auto;
      padding: 0;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 0.5px;
    }

    #select-folder {
      width: 125px;
      margin-right: 6px;
      font-size: 12px;
      appearance: none;
      background-repeat: no-repeat;
      background-position: center right 4px;
      background-size: 16px;

      @include themify($themes) {
        color: themed("mutedTextColor");
        border-color: themed("mutedTextColor");
      }

      &:not([disabled]) {
        display: block;
      }
    }

    .primary,
    .secondary {
      font-size: 12px;
    }

    .secondary {
      margin-right: 6px;
      border-width: 1px;
    }

    .primary {
      margin-right: 2px;
    }

    &.success-message,
    &.error-message {
      padding: 4px 36px 6px 42px;
    }
  }
}

button {
  padding: 4px 8px;
  border-radius: $border-radius;
  border: 1px solid transparent;
  cursor: pointer;
}

button.primary:not(.neutral) {
  @include themify($themes) {
    background-color: themed("primaryColor");
    color: themed("textContrast");
    border-color: themed("primaryColor");
  }

  &:hover {
    @include themify($themes) {
      background-color: darken(themed("primaryColor"), 1.5%);
      color: darken(themed("textContrast"), 6%);
    }
  }
}

button.secondary:not(.neutral) {
  @include themify($themes) {
    background-color: themed("backgroundColor");
    color: themed("mutedTextColor");
    border-color: themed("mutedTextColor");
  }

  &:hover {
    @include themify($themes) {
      background-color: themed("backgroundOffsetColor");
      color: darken(themed("mutedTextColor"), 6%);
    }
  }
}

button.link,
button.neutral {
  @include themify($themes) {
    background-color: transparent;
    color: themed("primaryColor");
  }

  &:hover {
    text-decoration: underline;

    @include themify($themes) {
      color: darken(themed("primaryColor"), 6%);
    }
  }
}

select {
  padding: 4px 6px;
  border: 1px solid #000000;
  border-radius: $border-radius;

  @include themify($themes) {
    color: themed("textColor");
    background-color: themed("inputBackgroundColor");
    border-color: themed("inputBorderColor");
  }
}

.success-message {
  display: flex;
  align-items: center;
  justify-content: center;

  @include themify($themes) {
    color: themed("successColor");
  }

  svg {
    margin-right: 8px;

    path {
      @include themify($themes) {
        fill: themed("successColor");
      }
    }
  }
}

.error-message {
  @include themify($themes) {
    color: themed("errorColor");
  }
}

.success-event,
.error-event {
  .notification-body {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  #select-folder {
    display: none;
  }
}

@media print {
  body {
    display: none;
  }
}

.theme_light {
  #content .inner-wrapper {
    #select-folder {
      background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHhtbG5zOnhsaW5rPSdodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rJyB3aWR0aD0nMTYnIGhlaWdodD0nMTYnIGZpbGw9J25vbmUnPjxwYXRoIHN0cm9rZT0nIzIxMjUyOScgZD0nbTUgNiAzIDMgMy0zJy8+PC9zdmc+");
    }
  }
}

.theme_dark {
  #content .inner-wrapper {
    #select-folder {
      background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHdpZHRoPScxNicgaGVpZ2h0PScxNicgZmlsbD0nbm9uZSc+PHBhdGggc3Ryb2tlPScjZmZmZmZmJyBkPSdtNSA2IDMgMyAzLTMnLz48L3N2Zz4=");
    }
  }
}
