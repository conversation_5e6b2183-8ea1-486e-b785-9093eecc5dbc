<bit-item>
  <button
    (click)="selectCipher(cipher)"
    appA11yTitle="{{ title }} - {{ cipher.name }}"
    bit-item-content
    tabindex="0"
    type="button"
  >
    <app-vault-icon slot="start" [cipher]="cipher"></app-vault-icon>
    <span data-testid="item-name">
      {{ cipher.name }}
      <i
        *ngIf="cipher.organizationId"
        [appA11yTitle]="'shared' | i18n"
        class="bwi bwi-collection-shared text-muted"
      ></i>
    </span>
    <ng-container slot="secondary">
      <div *ngIf="getSubName(cipher)">{{ getSubName(cipher) }}</div>
      <div *ngIf="cipher.subTitle">{{ cipher.subTitle }}</div>
    </ng-container>
  </button>
</bit-item>
