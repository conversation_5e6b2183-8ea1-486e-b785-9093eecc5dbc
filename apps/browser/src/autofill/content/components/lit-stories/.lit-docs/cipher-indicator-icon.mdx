import { Meta, <PERSON>s, Primary } from "@storybook/addon-docs";

import * as stories from "./cipher-indicator-icon.lit-stories";

<Meta title="Components/Ciphers/Cipher Indicator Icon" of={stories} />

## Cipher Info Indicator Icons

The `CipherInfoIndicatorIcons` component displays a set of icons indicating specific attributes
related to cipher information. It supports business and family organization indicators, styled
dynamically based on the provided theme.

<Primary />
<Controls />

## Props

| **Prop**           | **Type**  | **Required** | **Description**                                                         |
| ------------------ | --------- | ------------ | ----------------------------------------------------------------------- |
| `showBusinessIcon` | `boolean` | No           | Displays the business organization icon when set to `true`.             |
| `showFamilyIcon`   | `boolean` | No           | Displays the family organization icon when set to `true`.               |
| `theme`            | `Theme`   | Yes          | Defines the theme used to style the icons. Must match the `Theme` enum. |

## Installation and Setup

1. Ensure the necessary dependencies are installed:

   - `lit`: Renders the component.
   - `@emotion/css`: Used for styling.

2. Pass the required props when using the component:
   - `showBusinessIcon`: A boolean that, when `true`, displays the business icon.
   - `showFamilyIcon`: A boolean that, when `true`, displays the family icon.
   - `theme`: Specifies the theme for styling the icons.

## Accessibility (WCAG) Compliance

The `CipherInfoIndicatorIcons` component ensures accessibility and usability through its design:

### Screen Reader Compatibility

- Icons are rendered as `<svg>` elements, and parent components should provide appropriate labeling
  or descriptions to convey their meaning to screen readers.

### Visual Feedback

- Icons are styled dynamically based on the `theme` to ensure visual clarity and contrast in all
  supported themes.
- The size of the icons is fixed at `12px` in height to maintain a consistent visual appearance.

## Usage Example

Here's an example of how to integrate the `CipherInfoIndicatorIcons` component:

```ts
import { CipherInfoIndicatorIcons } from "./cipher-info-indicator-icons";
import { ThemeTypes } from "@bitwarden/common/platform/enums/theme-type.enum";

<CipherInfoIndicatorIcons
  showBusinessIcon={true}
  showFamilyIcon={false}
  theme={ThemeTypes.Dark}
/>;
```

This example displays the business organization icon, styled for the dark theme, and omits the
family organization icon.

### Styling Details

- The component includes the following styles:

- Icons: Rendered as SVGs with a height of 12px and a width that adjusts to maintain their aspect
  ratio.
- Color: Icons are dynamically styled based on the theme, using muted text colors for a subtle
  appearance.

### Notes

- If neither showBusinessIcon nor showFamilyIcon is set to true, the component renders nothing. This
  behavior should be handled by the parent component.
