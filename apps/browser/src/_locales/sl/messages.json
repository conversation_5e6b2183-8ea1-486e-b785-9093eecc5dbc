{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "Bitwarden Password Manager", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "At home, at work, or on the go, <PERSON><PERSON><PERSON> easily secures all your passwords, passkeys, and sensitive information", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "Prijavite se ali ustvarite nov račun za dostop do svojega varnega trezorja."}, "inviteAccepted": {"message": "Invitation accepted"}, "createAccount": {"message": "Ustvari račun"}, "newToBitwarden": {"message": "New to Bitwarden?"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "Welcome back"}, "setAStrongPassword": {"message": "Set a strong password"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Finish creating your account by setting a password"}, "enterpriseSingleSignOn": {"message": "Enkratna podjetniška prijava."}, "cancel": {"message": "Prekliči"}, "close": {"message": "<PERSON><PERSON><PERSON>"}, "submit": {"message": "Pošlji"}, "emailAddress": {"message": "Elektronski naslov"}, "masterPass": {"message": "Glavno geslo"}, "masterPassDesc": {"message": "Glavno geslo je geslo, ki ga uporabljate za dostop do svojega trezorja. <PERSON><PERSON> pomembno je, da ga ne pozabite. Če pozabite glavno geslo, ga ne bo mogoče obnoviti."}, "masterPassHintDesc": {"message": "Če pozabite glavno geslo, boste prejeli ta namig, da bi se gesla laže spomnili."}, "masterPassHintText": {"message": "If you forget your password, the password hint can be sent to your email. $CURRENT$/$MAXIMUM$ character maximum.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Ponovno vnesite glavno geslo"}, "masterPassHint": {"message": "Namig za glavno g<PERSON>lo (neobvezno)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Join organization"}, "joinOrganizationName": {"message": "Join $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Finish joining this organization by setting a master password."}, "tab": {"message": "Zavihek"}, "vault": {"message": "<PERSON><PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON>"}, "allVaults": {"message": "<PERSON><PERSON> t<PERSON>"}, "tools": {"message": "Orodja"}, "settings": {"message": "Nastavitve"}, "currentTab": {"message": "Trenutni zavihek"}, "copyPassword": {"message": "<PERSON><PERSON><PERSON>"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "<PERSON><PERSON><PERSON>"}, "copyUri": {"message": "<PERSON><PERSON><PERSON>"}, "copyUsername": {"message": "<PERSON><PERSON><PERSON>e"}, "copyNumber": {"message": "<PERSON><PERSON><PERSON>"}, "copySecurityCode": {"message": "<PERSON><PERSON><PERSON> kodo"}, "copyName": {"message": "Copy name"}, "copyCompany": {"message": "Copy company"}, "copySSN": {"message": "Copy Social Security number"}, "copyPassportNumber": {"message": "Copy passport number"}, "copyLicenseNumber": {"message": "Copy license number"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "Copy notes"}, "copy": {"message": "Copy", "description": "Copy to clipboard"}, "fill": {"message": "Fill", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Samodejno izpolnjevanje"}, "autoFillLogin": {"message": "Samodejno izpolni prijavo"}, "autoFillCard": {"message": "Samodejno izpolni kartico"}, "autoFillIdentity": {"message": "Samodejno izpolni identiteto"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON><PERSON><PERSON> (kopirano)"}, "copyElementIdentifier": {"message": "<PERSON><PERSON><PERSON> naziv polja po meri"}, "noMatchingLogins": {"message": "Ni ustreznih prijav."}, "noCards": {"message": "Ni kartic"}, "noIdentities": {"message": "Ni identitet"}, "addLoginMenu": {"message": "Dodaj prijavo"}, "addCardMenu": {"message": "<PERSON>daj kartico"}, "addIdentityMenu": {"message": "Dodaj identiteto"}, "unlockVaultMenu": {"message": "Odkleni svoj trezor"}, "loginToVaultMenu": {"message": "Prijavi se v svoj trezor"}, "autoFillInfo": {"message": "Za samodejno izpolnjevanje v trenutnem zavihku ni na voljo nobena prijava."}, "addLogin": {"message": "Dodaj prijavo"}, "addItem": {"message": "Dodaj element"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "Pridobi namig za glavno geslo"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendVerificationCode": {"message": "Pošlji kodo za preverjanje po e-pošti"}, "sendCode": {"message": "Pošlji kodo"}, "codeSent": {"message": "<PERSON><PERSON> p<PERSON>"}, "verificationCode": {"message": "Koda za preverjanje"}, "confirmIdentity": {"message": "Za nadaljevanje potrdite svojo istovetnost."}, "changeMasterPassword": {"message": "Spremeni glavno geslo"}, "continueToWebApp": {"message": "Continue to web app?"}, "continueToWebAppDesc": {"message": "Explore more features of your Bitwarden account on the web app."}, "continueToHelpCenter": {"message": "Continue to Help Center?"}, "continueToHelpCenterDesc": {"message": "Learn more about how to use Bitwarden on the Help Center."}, "continueToBrowserExtensionStore": {"message": "Continue to browser extension store?"}, "continueToBrowserExtensionStoreDesc": {"message": "Help others find out if <PERSON><PERSON><PERSON> is right for them. Visit your browser's extension store and leave a rating now."}, "changeMasterPasswordOnWebConfirmation": {"message": "You can change your master password on the Bitwarden web app."}, "fingerprintPhrase": {"message": "Identifikacijsko geslo", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Identifikacijsko geslo vašega računa", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Prijava v dveh korakih"}, "logOut": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "aboutBitwarden": {"message": "About Bitwarden"}, "about": {"message": "O programu"}, "moreFromBitwarden": {"message": "More from Bitwarden"}, "continueToBitwardenDotCom": {"message": "Continue to bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden for Business"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden Authenticator allows you to store authenticator keys and generate TOTP codes for 2-step verification flows. Learn more on the bitwarden.com website"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Securely store, manage, and share developer secrets with Bitwarden Secrets Manager. Learn more on the bitwarden.com website."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Create smooth and secure login experiences free from traditional passwords with Passwordless.dev. Learn more on the bitwarden.com website."}, "freeBitwardenFamilies": {"message": "Free Bitwarden Families"}, "freeBitwardenFamiliesPageDesc": {"message": "You are eligible for Free Bitwarden Families. Redeem this offer today in the web app."}, "version": {"message": "Različica"}, "save": {"message": "<PERSON><PERSON><PERSON>"}, "move": {"message": "Premakni"}, "addFolder": {"message": "<PERSON><PERSON><PERSON>o"}, "name": {"message": "<PERSON><PERSON>"}, "editFolder": {"message": "<PERSON><PERSON><PERSON> mapo"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "New folder"}, "folderName": {"message": "Folder name"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "Izbriši mapo"}, "folders": {"message": "Mape"}, "noFolders": {"message": "Nobene takšne mape ni."}, "helpFeedback": {"message": "Pomoč in povratne informacije"}, "helpCenter": {"message": "Bitwardnov center za pomoč"}, "communityForums": {"message": "Prebrskajte Bitwardnove skupnostne forume"}, "contactSupport": {"message": "Kontaktirajte podporo uporabnikom"}, "sync": {"message": "Sinhronizacija"}, "syncVaultNow": {"message": "Sinhronizi<PERSON> t<PERSON> z<PERSON>j"}, "lastSync": {"message": "Zadnja sinhronizacija:"}, "passGen": {"message": "Generator gesel"}, "generator": {"message": "Generator", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Avtomatično generiraj mo<PERSON>, edinstvena gesla za vaše prijave."}, "bitWebVaultApp": {"message": "Bitwarden web app"}, "importItems": {"message": "Uvozi elemente"}, "select": {"message": "Izberi"}, "generatePassword": {"message": "<PERSON><PERSON><PERSON>"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "Ponovno ustvari geslo"}, "options": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "length": {"message": "Dolžina"}, "include": {"message": "Include", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Include numbers", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Include special characters", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "<PERSON><PERSON><PERSON><PERSON> besed"}, "wordSeparator": {"message": "<PERSON><PERSON><PERSON> besed"}, "capitalize": {"message": "Velika začetnica", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "Vključi števko"}, "minNumbers": {"message": "Minimalno števk"}, "minSpecial": {"message": "Minimalno posebnih znakov"}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "Išči v trezorju"}, "edit": {"message": "<PERSON><PERSON><PERSON>"}, "view": {"message": "Pogled"}, "noItemsInList": {"message": "Tukaj ni nič<PERSON>ar."}, "itemInformation": {"message": "Informacije o elementu"}, "username": {"message": "Uporabniško ime"}, "password": {"message": "<PERSON><PERSON><PERSON>"}, "totp": {"message": "Authenticator secret"}, "passphrase": {"message": "Večbesedno geslo"}, "favorite": {"message": "Priljubljeni"}, "unfavorite": {"message": "Unfavorite"}, "itemAddedToFavorites": {"message": "Item added to favorites"}, "itemRemovedFromFavorites": {"message": "<PERSON><PERSON> removed from favorites"}, "notes": {"message": "<PERSON><PERSON><PERSON>"}, "privateNote": {"message": "Private note"}, "note": {"message": "Opomba"}, "editItem": {"message": "Uredi element"}, "folder": {"message": "Mapa"}, "deleteItem": {"message": "Izbiši element"}, "viewItem": {"message": "<PERSON><PERSON> elementa"}, "launch": {"message": "Zaženi"}, "launchWebsite": {"message": "Launch website"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Spletna stran"}, "toggleVisibility": {"message": "Preklopi vidnost"}, "manage": {"message": "Upravljanje"}, "other": {"message": "Drugo"}, "unlockMethods": {"message": "Unlock options"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Da spremenite časovne omejitve trezorja, nastavite metodo o<PERSON>."}, "unlockMethodNeeded": {"message": "Set up an unlock method in Settings"}, "sessionTimeoutHeader": {"message": "Session timeout"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "Other options"}, "rateExtension": {"message": "Ocenite to razširitev"}, "browserNotSupportClipboard": {"message": "Vaš brskalnik ne podpira enostavnega kopiranja na odložišče. Prosimo, kopirajte ročno."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "<PERSON><PERSON>š trezor je zaklenjen. Za nadaljevanje potrdite svojo identiteto."}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "loggedInAsOn": {"message": "Prijavljeni ste kot $EMAIL$ na $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Napačno glavno geslo"}, "vaultTimeout": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, ko preteče toliko časa:"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "Zakleni zdaj"}, "lockAll": {"message": "Lock all"}, "immediately": {"message": "Takoj"}, "tenSeconds": {"message": "10 sekund"}, "twentySeconds": {"message": "20 sekund"}, "thirtySeconds": {"message": "30 sekund"}, "oneMinute": {"message": "1 minuta"}, "twoMinutes": {"message": "2 minuti"}, "fiveMinutes": {"message": "5 minut"}, "fifteenMinutes": {"message": "15 minut"}, "thirtyMinutes": {"message": "30 minut"}, "oneHour": {"message": "1 ura"}, "fourHours": {"message": "4 ure"}, "onLocked": {"message": "<PERSON><PERSON> z<PERSON><PERSON><PERSON> sistema"}, "onRestart": {"message": "<PERSON><PERSON> ponovnem zagonu brskalnika"}, "never": {"message": "Nik<PERSON>"}, "security": {"message": "Varnost"}, "confirmMasterPassword": {"message": "Confirm master password"}, "masterPassword": {"message": "Master password"}, "masterPassImportant": {"message": "Your master password cannot be recovered if you forget it!"}, "masterPassHintLabel": {"message": "Master password hint"}, "errorOccurred": {"message": "<PERSON><PERSON><PERSON><PERSON> je do napake"}, "emailRequired": {"message": "Epoštni naslov je obvezen."}, "invalidEmail": {"message": "Neveljaven epoštni naslov."}, "masterPasswordRequired": {"message": "Glavno geslo je obvezno."}, "confirmMasterPasswordRequired": {"message": "Ponoven vnos glavnega gesla je obvezen."}, "masterPasswordMinlength": {"message": "Glavno geslo mora vsebovati vsaj $VALUE$ znakov.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "<PERSON><PERSON><PERSON><PERSON> glavnega gesla se ne ujema."}, "newAccountCreated": {"message": "<PERSON><PERSON>š račun je ustvarjen. Lahko se prijavite."}, "newAccountCreated2": {"message": "Your new account has been created!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "youSuccessfullyLoggedIn": {"message": "You successfully logged in"}, "youMayCloseThisWindow": {"message": "You may close this window"}, "masterPassSent": {"message": "Poslali smo vam e-poštno spročilo z namigom za vaše glavno geslo."}, "verificationCodeRequired": {"message": "Koda za preverjanje je obvezna."}, "webauthnCancelOrTimeout": {"message": "The authentication was cancelled or took too long. Please try again."}, "invalidVerificationCode": {"message": "Neveljavna koda za preverjanje"}, "valueCopied": {"message": "$VALUE$ kopirana", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Izbrane prijave na tej strani ni mogoče samodejno izpolniti. Namesto tega podatke kopirajte in prilepite."}, "totpCaptureError": {"message": "Unable to scan QR code from the current webpage"}, "totpCaptureSuccess": {"message": "Authenticator key added"}, "totpCapture": {"message": "Scan authenticator QR code from current webpage"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Copy Authenticator key (TOTP)"}, "loggedOut": {"message": "Odjavljen"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "<PERSON><PERSON><PERSON> seja je potekla."}, "logIn": {"message": "Log in"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, da se <PERSON><PERSON> odjaviti?"}, "yes": {"message": "Da"}, "no": {"message": "Ne"}, "location": {"message": "Location"}, "unexpectedError": {"message": "Prišlo je do nepričakovane napake."}, "nameRequired": {"message": "Ime je obvezno."}, "addedFolder": {"message": "Mapa dodana"}, "twoStepLoginConfirmation": {"message": "Avtentikacija v dveh korakih dodatno varuje vaš račun, saj zahteva, da vsakokratno prijavo potrdite z drugo napravo, kot je varnostni ključ, aplikacija za preverjanje pristnosti, SMS, telefonski klic ali e-pošta. Avtentikacijo v dveh korakih lahko omogočite v spletnem trezorju bitwarden.com. Ali želite spletno stran obiskati sedaj?"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "Mapa shranjena"}, "deleteFolderConfirmation": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, da želite izbrisati to mapo?"}, "deletedFolder": {"message": "Mapa izbrisana"}, "gettingStartedTutorial": {"message": "Vodič za začetnike"}, "gettingStartedTutorialVideo": {"message": "<PERSON><PERSON> vodič za začtenike vam pokaže, kako najbolje izkoristiti Bitwardnovo razširitev za brskalnik."}, "syncingComplete": {"message": "Sinhronizacija končana"}, "syncingFailed": {"message": "Sinhronizacija ni uspela"}, "passwordCopied": {"message": "G<PERSON>lo je bilo kopirano"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "Nov URI"}, "addDomain": {"message": "Add domain", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "Element dodan"}, "editedItem": {"message": "<PERSON><PERSON> s<PERSON>n"}, "deleteItemConfirmation": {"message": "<PERSON>, da <PERSON><PERSON><PERSON> to izbrisati?"}, "deletedItem": {"message": "Element poslan v smeti"}, "overwritePassword": {"message": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>lo"}, "overwritePasswordConfirmation": {"message": "<PERSON>, da ž<PERSON>te prepisati trenutno geslo?"}, "overwriteUsername": {"message": "Prepiši uporabniško ime"}, "overwriteUsernameConfirmation": {"message": "<PERSON><PERSON> pre<PERSON><PERSON><PERSON>, da želite prepisati trenutno uporabniško ime?"}, "searchFolder": {"message": "Preišči mapo"}, "searchCollection": {"message": "Preišči zbirko"}, "searchType": {"message": "<PERSON>šči med tipi"}, "noneFolder": {"message": "<PERSON>rez mape", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "Predlagaj dodajanje prijave"}, "vaultSaveOptionsTitle": {"message": "Save to vault options"}, "addLoginNotificationDesc": {"message": "Predlagaj dodajanje novega elementa, če v trezorju ni ustreznega."}, "addLoginNotificationDescAlt": {"message": "Če predmeta ni v trezorju, ga je potrebno dodati. Velja za vse prijavljene račune."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "Prikaži kartice na strani Zavihek"}, "showCardsCurrentTabDesc": {"message": "Na strani Zavihek prikaži kartice za lažje samodejno izpoljnjevanje."}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "Prikaži identitete na strani Zavihek"}, "showIdentitiesCurrentTabDesc": {"message": "Na strani Zavihek prikaži elemente identitete za lažje samodejno izpolnjevanje."}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "Počisti odložišče", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Samodejno izbriši kopirane vrednosti z odložišča.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "<PERSON>j si <PERSON> zapomni to ges<PERSON>?"}, "notificationAddSave": {"message": "Da, shrani zdaj"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "loginSaveConfirmation": {"message": "$ITEMNAME$ saved to Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is saved."}, "loginUpdatedConfirmation": {"message": "$ITEMNAME$ updated in Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is updated."}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Predlagaj posodobitev obstoječe prijave"}, "changedPasswordNotificationDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>, ali naj <PERSON>ward<PERSON> posodobi geslo pri<PERSON>, kadar zazna spremembo gesla na spletni strani"}, "changedPasswordNotificationDescAlt": {"message": "Ask to update a login's password when a change is detected on a website. Applies to all logged in accounts."}, "enableUsePasskeys": {"message": "Ask to save and use passkeys"}, "usePasskeysDesc": {"message": "Ask to save new passkeys or log in with passkeys stored in your vault. Applies to all logged in accounts."}, "notificationChangeDesc": {"message": "<PERSON><PERSON><PERSON>, da Bitwarden shrani spremembo tega gesla?"}, "notificationChangeSave": {"message": "Da, posodobi zdaj"}, "notificationUnlockDesc": {"message": "Unlock your Bitwarden vault to complete the autofill request."}, "notificationUnlock": {"message": "Unlock"}, "additionalOptions": {"message": "Additional options"}, "enableContextMenuItem": {"message": "Prikaži možnosti kontekstnega menuja"}, "contextMenuItemDesc": {"message": "Z desnim klikom se vam prikažejo možnosti generiranja gesel in shranjenih prijav za spletno stran, na kateri ste."}, "contextMenuItemDescAlt": {"message": "Use a secondary click to access password generation and matching logins for the website. Applies to all logged in accounts."}, "defaultUriMatchDetection": {"message": "Privzet način preverjanja ujemanja URI-ja", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Izberite privzeti način preverjanja ujemanja URI-ja pri samodejnem izpolnjevanju in drugih dejanjih."}, "theme": {"message": "<PERSON><PERSON>"}, "themeDesc": {"message": "Spremeni temo aplikacije."}, "themeDescAlt": {"message": "Change the application's color theme. Applies to all logged in accounts."}, "dark": {"message": "Temno", "description": "Dark color"}, "light": {"message": "<PERSON><PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "Izvoz trezorja"}, "fileFormat": {"message": "Format datoteke"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "File password"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "warning": {"message": "OPOZORILO", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Potrdite izvoz trezorja"}, "exportWarningDesc": {"message": "Ta datoteka z izvoženimi podatki vsebuje podatke iz vašega trezorja v nešifrirani obliki. Ne shranjujte in ne pošiljajte je po nezavarovanih kanalih, kot je elektronska pošta. Po uporabi jo takoj izbrišite."}, "encExportKeyWarningDesc": {"message": "Ta izvoz šifrira vaše podatke z uporabo ključa za šifriranje. Če boste kdaj zamenjali ključ za šifriranje, boste morali podatke izvoziti ponovno, saj pričujočega izvoza ne boste mogli več dešifrirati."}, "encExportAccountWarningDesc": {"message": "Ključ za šifriranje je edinstven za vsak Bitwarden račun, zato ni mogoče da se uvozi šifrirana datoteka v drugi račun."}, "exportMasterPassword": {"message": "Za izvoz podatkov iz trezorja vnesite svoje glavno geslo."}, "shared": {"message": "V skupni rabi"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "Premakni v organizacijo"}, "movedItemToOrg": {"message": "Element $ITEMNAME$ premaknjen v $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Izberite organizacijo, v katero želite premakniti ta element. S tem boste prenesli lastništvo elementa na organizacijo in ne boste več njegov neposredni lastnik."}, "learnMore": {"message": "Več o tem"}, "authenticatorKeyTotp": {"message": "Ključ avtentikatorja (TOTP)"}, "verificationCodeTotp": {"message": "Verifikacijska koda (TOTP)"}, "copyVerificationCode": {"message": "<PERSON><PERSON><PERSON> veri<PERSON> kodo"}, "attachments": {"message": "<PERSON><PERSON>onke"}, "deleteAttachment": {"message": "Izbriši priponke"}, "deleteAttachmentConfirmation": {"message": "<PERSON><PERSON> <PERSON>p<PERSON>, da želite izbrisati to priponko?"}, "deletedAttachment": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>onka"}, "newAttachment": {"message": "Dodaj novo priponko"}, "noAttachments": {"message": "<PERSON> priponk."}, "attachmentSaved": {"message": "Priponka je bila shranjena."}, "file": {"message": "Datoteka"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "Izberite datoteko."}, "maxFileSize": {"message": "Največja velikost datoteke je 500 MB."}, "featureUnavailable": {"message": "Funkcija ni na voljo."}, "encryptionKeyMigrationRequired": {"message": "Encryption key migration required. Please login through the web vault to update your encryption key."}, "premiumMembership": {"message": "Premium članstvo"}, "premiumManage": {"message": "Upravljan<PERSON>"}, "premiumManageAlert": {"message": "S svojim članstvom lahko upravljate na spletnem trezorju bitwarden.com. Želite obiskati to spletno stran zdaj?"}, "premiumRefresh": {"message": "Osveži status članstva"}, "premiumNotCurrentMember": {"message": "Trenutno niste premium član."}, "premiumSignUpAndGet": {"message": "Če postanete premium član, dobite:"}, "ppremiumSignUpStorage": {"message": "1 GB šifriranega prostora za shrambo podatkov."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "Proprietary two-step login options such as YubiKey and Duo."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON><PERSON>, zdravje računa in poročila o kraji podatkov, ki vam pomagajo ohraniti varnost vašega trezorja."}, "ppremiumSignUpTotp": {"message": "Generator TOTP verifikacijskih kod (2FA) za prijave v vašem trezorju."}, "ppremiumSignUpSupport": {"message": "Prioritetna podpora strankam."}, "ppremiumSignUpFuture": {"message": "Vse bodoče premium ugodnosti. Kmalu še več!"}, "premiumPurchase": {"message": "Kupite premium članstvo"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "Ste premium član!"}, "premiumCurrentMemberThanks": {"message": "<PERSON><PERSON><PERSON>, ker podpirate Bitwarden."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "Vse za samo $PRICE$ /leto!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "Osveževanje zaključeno"}, "enableAutoTotpCopy": {"message": "Samodejno kopiraj TOTP"}, "disableAutoTotpCopyDesc": {"message": "Če za prijavo uporabljate avtentikacijski ključ, se verifikacijska koda TOTP samodejno kopira v odložišče, kadar uporabite samodejno izpolnjevanje."}, "enableAutoBiometricsPrompt": {"message": "<PERSON><PERSON> z<PERSON>u zahtevaj biometrično preverjanje"}, "premiumRequired": {"message": "Potrebno je premium članstvo"}, "premiumRequiredDesc": {"message": "Premium članstvo je potrebno za uporabo te funkcije."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "Potrditveno sporočilo poslano na $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "Priključi svoj varnostni ključ v USB priključek. Če ima tipko, se jo dotaknite."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "Authenticate WebAuthn"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "P<PERSON>java ni na voljo"}, "noTwoStepProviders": {"message": "Ta račun ima omogočemo prijavo v dveh korakih, ampak, nobena izmed konfiguriranih prijav v dveh korakih ni podprta v teb spletnem brskalniku."}, "noTwoStepProviders2": {"message": "Uporabite enega izmed podprtih spletnih brskalnikov (npr. Chrome) in/ali dodajte ponudnika, ki je bolje podprt na različnih brskalnikih (npr. aplikacija za avtentikacijo)."}, "twoStepOptions": {"message": "Možnosti dvostopenjske prijave"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "Ste izgubili dostop do vseh ponudnikov dvostopenjske prijave? Uporabite svojo kodo za obnovitev in tako onemogočite dvostopenjsko prijavo v svoj račun."}, "recoveryCodeTitle": {"message": "Koda za obnovitev"}, "authenticatorAppTitle": {"message": "Aplikacija za avtentikacijo"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP Security Key"}, "yubiKeyDesc": {"message": "Za dostop do svojega računa uporabite YubiKey. Podprti so YubiKey 4, 4 Nano, 4C in naprave NEO."}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Verify with Duo Security for your organization using the Duo Mobile app, SMS, phone call, or U2F security key.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Use any WebAuthn compatible security key to access your account."}, "emailTitle": {"message": "E-pošta"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "selfHostedEnvironment": {"message": "Self-hosted environment"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "Okolje po meri"}, "baseUrl": {"message": "URL naslov strežnika"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "URL naslov API strežnika"}, "webVaultUrl": {"message": "Web vault server URL"}, "identityUrl": {"message": "Identity server URL"}, "notificationsUrl": {"message": "Notifications server URL"}, "iconsUrl": {"message": "Icons server URL"}, "environmentSaved": {"message": "Environment URLs saved"}, "showAutoFillMenuOnFormFields": {"message": "Show autofill menu on form fields", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Turn off your browser's built in password manager settings to avoid conflicts."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Edit browser settings."}, "autofillOverlayVisibilityOff": {"message": "Off", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "When autofill icon is selected", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Samodejno izpolni, ko se stran naloži"}, "enableAutoFillOnPageLoadDesc": {"message": "Če Bitwarden na strani zazna prijavni obrazec, ga samodejno izpolni takoj, ko se stran naloži."}, "experimentalFeature": {"message": "<PERSON><PERSON><PERSON><PERSON> strani, ki jim ne zaupate ali v katere so vdrli, lahko zlorabijo samodejno izpolnjevanje ob naložitvi strani."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "Preberite več o samodejnem izpolnjevanju"}, "defaultAutoFillOnPageLoad": {"message": "Privzeta nastavitev samodejnega izpolnjevanja za prijavne elemente"}, "defaultAutoFillOnPageLoadDesc": {"message": "Samodejno izpolnjevanje ob naložitvi strani lahko izklopite za posamčne prijave, ko jih urejate."}, "itemAutoFillOnPageLoad": {"message": "Samodejno izpolni ob naložitvi strani (če je omogočeno v Možnostih)"}, "autoFillOnPageLoadUseDefault": {"message": "Uporabi privzete nastavitve"}, "autoFillOnPageLoadYes": {"message": "Samodejno izpolni ob naložitvi strani"}, "autoFillOnPageLoadNo": {"message": "Ne izpolnjuj samodejno ob naložitvi strani"}, "commandOpenPopup": {"message": "Open vault popup"}, "commandOpenSidebar": {"message": "Open vault in sidebar"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "Ustvari novo naključno geslo in ga kopiraj v odložišče"}, "commandLockVaultDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> trez<PERSON>"}, "customFields": {"message": "Polja po meri"}, "copyValue": {"message": "<PERSON><PERSON><PERSON>"}, "value": {"message": "Vrednost"}, "newCustomField": {"message": "Novo polje po meri"}, "dragToSort": {"message": "Sortirajte z vlečenjem"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "<PERSON><PERSON><PERSON>"}, "cfTypeHidden": {"message": "Skrito"}, "cfTypeBoolean": {"message": "Logična vrednost"}, "cfTypeCheckbox": {"message": "Checkbox"}, "cfTypeLinked": {"message": "Povezano polje", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Povezana vrednost", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Če kliknete izven tega pojavnega okna, da bi preverili po<PERSON>, se to pojavno okno zaprlo. Želite odpreti to pojavno okno v novem oknu, da se ne bo zaprlo?"}, "popupU2fCloseMessage": {"message": "Ta spletni brskalnik ne more obdelati U2F zahteve v tem pojavnem oknu. Želite odpreti to pojavno okno v novem oknu, tako, da se lahko prijavite z U2F?"}, "enableFavicon": {"message": "Show website icons"}, "faviconDesc": {"message": "Show a recognizable image next to each login."}, "faviconDescAlt": {"message": "Show a recognizable image next to each login. Applies to all logged in accounts."}, "enableBadgeCounter": {"message": "Show badge counter"}, "badgeCounterDesc": {"message": "Indicate how many logins you have for the current web page."}, "cardholderName": {"message": "<PERSON><PERSON> imetnika kartice"}, "number": {"message": "Številka"}, "brand": {"message": "Znamka"}, "expirationMonth": {"message": "Mesec poteka"}, "expirationYear": {"message": "<PERSON>"}, "expiration": {"message": "Veljavna do"}, "january": {"message": "<PERSON><PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON>"}, "march": {"message": "<PERSON><PERSON>"}, "april": {"message": "April"}, "may": {"message": "Maj"}, "june": {"message": "<PERSON><PERSON><PERSON>"}, "july": {"message": "<PERSON><PERSON><PERSON>"}, "august": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "september": {"message": "September"}, "october": {"message": "Oktober"}, "november": {"message": "November"}, "december": {"message": "December"}, "securityCode": {"message": "Varnostna koda"}, "ex": {"message": "npr."}, "title": {"message": "<PERSON><PERSON>"}, "mr": {"message": "G."}, "mrs": {"message": "Ga."}, "ms": {"message": "Gdč."}, "dr": {"message": "Dr."}, "mx": {"message": "Gx"}, "firstName": {"message": "Ime"}, "middleName": {"message": "Srednje ime"}, "lastName": {"message": "Priimek"}, "fullName": {"message": "Polno ime"}, "identityName": {"message": "Ime identitete"}, "company": {"message": "Podjetje"}, "ssn": {"message": "EMŠO"}, "passportNumber": {"message": "Številka potnega lista"}, "licenseNumber": {"message": "Številka vozniškega dovoljenja"}, "email": {"message": "E-pošta"}, "phone": {"message": "Telefon"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "address1": {"message": "Naslov 1"}, "address2": {"message": "Naslov 2"}, "address3": {"message": "Naslov 3"}, "cityTown": {"message": "Mesto / Naselje"}, "stateProvince": {"message": "Država / Regija"}, "zipPostalCode": {"message": "Poštna številka"}, "country": {"message": "Država"}, "type": {"message": "Vrsta"}, "typeLogin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeLogins": {"message": "Prijave"}, "typeSecureNote": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeCard": {"message": "Kartica"}, "typeIdentity": {"message": "Identiteta"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "New $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Edit $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "View $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "Zgodovina gesel"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "<PERSON><PERSON><PERSON>"}, "collections": {"message": "Zbirke"}, "nCollections": {"message": "$COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "Priljubljeno"}, "popOutNewWindow": {"message": "Odpri v svojem oknu"}, "refresh": {"message": "Osveži"}, "cards": {"message": "Plačilne kartice"}, "identities": {"message": "Identitete"}, "logins": {"message": "Prijave"}, "secureNotes": {"message": "Zavarovani zapiski"}, "sshKeys": {"message": "SSH Keys"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "<PERSON><PERSON><PERSON>, ali je bilo g<PERSON>lo izpostavljeno"}, "passwordExposed": {"message": "To geslo je bilo že $VALUE$-krat razkrito med raznimi ukradenimi podatki. Morali bi ga zamenjati.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Tega gesla ni najti med znanimi ukradenimi podatki. Na<PERSON><PERSON><PERSON><PERSON> je varno, da ga uporabljate."}, "baseDomain": {"message": "<PERSON><PERSON>na domena", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Base domain (recommended)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "<PERSON><PERSON> domene", "description": "Domain name. Ex. website.com"}, "host": {"message": "Gostitelj", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Dobesedno ujemanje"}, "startsWith": {"message": "Ujemanje začetka"}, "regEx": {"message": "Regularni izraz", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Preverjanje ujemanja", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Privzeto preverjanje ujemanja", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Prikaži/skrij mo<PERSON>"}, "toggleCurrentUris": {"message": "<PERSON><PERSON><PERSON><PERSON>/skrij <PERSON>-je odprtih z<PERSON>kov", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "Trenutni URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Organizacija", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "<PERSON><PERSON><PERSON>"}, "allItems": {"message": "Vsi elementi"}, "noPasswordsInList": {"message": "<PERSON> tak<PERSON>h gesel."}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "Odstrani"}, "default": {"message": "Privzeto"}, "dateUpdated": {"message": "Posodobljeno", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "Ustvarjeno", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "<PERSON><PERSON><PERSON>.", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "<PERSON><PERSON> preprič<PERSON>, da vam možnost Nikoli ustreza? Pri nastavitvi Nikoli se šifrirni ključ vašega trezorja shrani v vaši napravi. Ob uporabi te možnosti morate skrbeti za ustrezno varnost svoje naprave."}, "noOrganizationsList": {"message": "Niste član nobene organizacije. Organizacije vam omogočajo varno skupno rabo elementov z drugimi uporabniki."}, "noCollectionsInList": {"message": "Ni zbirk za prikaz."}, "ownership": {"message": "Lastništvo"}, "whoOwnsThisItem": {"message": "Kdo je lastnik tega elementa?"}, "strong": {"message": "Močno", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "Dobro", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "Š<PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "Šibko glavno geslo"}, "weakMasterPasswordDesc": {"message": "<PERSON><PERSON><PERSON> geslo, ki ste ga i<PERSON>, je <PERSON><PERSON><PERSON>. Za primerno zaščito svojega Bitwarden računa morate uporabiti močno glavno geslo. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, da želite uporabiti izbrano glavno geslo?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Odkleni s PIN-kodo"}, "setYourPinTitle": {"message": "Set PIN"}, "setYourPinButton": {"message": "Set PIN"}, "setYourPinCode": {"message": "Za odklep Bitwardna si nastavite PIN-kodo. PIN-koda bo ponastavljena, če se boste popolnoma odjavili iz aplikacije."}, "setYourPinCode1": {"message": "Your PIN will be used to unlock Bitwarden instead of your master password. Your PIN will reset if you ever fully log out of Bitwarden."}, "pinRequired": {"message": "Potrebna je PIN-koda."}, "invalidPin": {"message": "Nepravilna PIN-koda."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Too many invalid PIN entry attempts. Logging out."}, "unlockWithBiometrics": {"message": "Prijava z biometriko"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "awaitDesktop": {"message": "Čakam na potrditev z namizja"}, "awaitDesktopDesc": {"message": "Please confirm using biometrics in the Bitwarden desktop application to set up biometrics for browser."}, "lockWithMasterPassOnRestart": {"message": "Zakleni z glavnim geslom ob ponovnem zagonu brskalnika"}, "lockWithMasterPassOnRestart1": {"message": "Require master password on browser restart"}, "selectOneCollection": {"message": "<PERSON>z<PERSON>ti morate vsaj eno zbirko."}, "cloneItem": {"message": "Podvoji element"}, "clone": {"message": "Podvoji"}, "passwordGenerator": {"message": "Password generator"}, "usernameGenerator": {"message": "Username generator"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "Use this password"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "<PERSON><PERSON><PERSON> ob poteku roka"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "newCustomizationOptionsCalloutTitle": {"message": "New customization options"}, "newCustomizationOptionsCalloutContent": {"message": "Customize your vault experience with quick copy actions, compact mode, and more!"}, "newCustomizationOptionsCalloutLink": {"message": "View all Appearance settings"}, "lock": {"message": "Zaklepanje", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "<PERSON><PERSON>", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "Preišči koš"}, "permanentlyDeleteItem": {"message": "Trajno izbriši element"}, "permanentlyDeleteItemConfirmation": {"message": "<PERSON><PERSON>p<PERSON>, da želite ta element trajno izbrisati?"}, "permanentlyDeletedItem": {"message": "Element trajno izbrisan"}, "restoreItem": {"message": "Obnovi element"}, "restoredItem": {"message": "Element obnovljen"}, "alreadyHaveAccount": {"message": "Already have an account?"}, "vaultTimeoutLogOutConfirmation": {"message": "Logging out will remove all access to your vault and requires online authentication after the timeout period. Are you sure you want to use this setting?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> de<PERSON> ob poteku roka"}, "autoFillAndSave": {"message": "Samodejno izpolni in shrani"}, "fillAndSave": {"message": "Fill and save"}, "autoFillSuccessAndSavedUri": {"message": "Element je bil samodejno izpolnjen in shranjen"}, "autoFillSuccess": {"message": "Element je bil samodejno izpolnjen"}, "insecurePageWarning": {"message": "Warning: This is an unsecured HTTP page, and any information you submit can potentially be seen and changed by others. This Login was originally saved on a secure (HTTPS) page."}, "insecurePageWarningFillPrompt": {"message": "Do you still wish to fill this login?"}, "autofillIframeWarning": {"message": "The form is hosted by a different domain than the URI of your saved login. <PERSON>ose OK to autofill anyway, or Cancel to stop."}, "autofillIframeWarningTip": {"message": "To prevent this warning in the future, save this URI, $HOSTNAME$, to your Bitwarden login item for this site.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "Nastavi glavno geslo"}, "currentMasterPass": {"message": "Trenutno glavno geslo"}, "newMasterPass": {"message": "Novo glavno geslo"}, "confirmNewMasterPass": {"message": "Ponovitev glavnega gesla"}, "masterPasswordPolicyInEffect": {"message": "One or more organization policies require your master password to meet the following requirements:"}, "policyInEffectMinComplexity": {"message": "Minimum complexity score of $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Minimum length of $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Contain one or more uppercase characters"}, "policyInEffectLowercase": {"message": "Contain one or more lowercase characters"}, "policyInEffectNumbers": {"message": "Contain one or more numbers"}, "policyInEffectSpecial": {"message": "Contain one or more of the following special characters $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Vaše novo glavno geslo ne ustreza zahtevam."}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Unsubscribe"}, "atAnyTime": {"message": "at any time."}, "byContinuingYouAgreeToThe": {"message": "By continuing, you agree to the"}, "and": {"message": "and"}, "acceptPolicies": {"message": "Strinjam se z naslednjim:"}, "acceptPoliciesRequired": {"message": "Niste sprejeli Pogojev uporabe in Pravilnika o zasebnosti."}, "termsOfService": {"message": "Pogoji <PERSON>"}, "privacyPolicy": {"message": "Pravilnik o zasebnosti"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "<PERSON>ig za geslo ne sme biti enak geslu."}, "ok": {"message": "V redu"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "Desktop sync verification"}, "desktopIntegrationVerificationText": {"message": "<PERSON>si<PERSON>, preverite, da namizna aplikacija prikazuje naslednje identifikacijsko geslo: "}, "desktopIntegrationDisabledTitle": {"message": "Browser integration is not set up"}, "desktopIntegrationDisabledDesc": {"message": "Browser integration is not set up in the Bitwarden desktop application. Please set it up in the settings within the desktop application."}, "startDesktopTitle": {"message": "Start the Bitwarden desktop application"}, "startDesktopDesc": {"message": "The Bitwarden desktop application needs to be started before unlock with biometrics can be used."}, "errorEnableBiometricTitle": {"message": "Unable to set up biometrics"}, "errorEnableBiometricDesc": {"message": "Action was canceled by the desktop application"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Desktop application invalidated the secure communication channel. Please retry this operation"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Desktop communication interrupted"}, "nativeMessagingWrongUserDesc": {"message": "The desktop application is logged into a different account. Please ensure both applications are logged into the same account."}, "nativeMessagingWrongUserTitle": {"message": "Account missmatch"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "Biometrics not set up"}, "biometricsNotEnabledDesc": {"message": "Browser biometrics requires desktop biometric to be set up in the settings first."}, "biometricsNotSupportedTitle": {"message": "Biometrics not supported"}, "biometricsNotSupportedDesc": {"message": "Browser biometrics is not supported on this device."}, "biometricsNotUnlockedTitle": {"message": "User locked or logged out"}, "biometricsNotUnlockedDesc": {"message": "Please unlock this user in the desktop application and try again."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "Biometrics failed"}, "biometricsFailedDesc": {"message": "Biometrics cannot be completed, consider using a master password or logging out. If this persists, please contact Bitwarden support."}, "nativeMessaginPermissionErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nativeMessaginPermissionErrorDesc": {"message": "Brez dovoljenja za komunikacijo z Bitwardnovo namizno aplikacijo ni mogoče uporabljati bimetričnega preverjanja v razširitvi brskalnika. Prosimo, poskusite ponovno."}, "nativeMessaginPermissionSidebarTitle": {"message": "Napaka pri zahtevku za dovoljenje"}, "nativeMessaginPermissionSidebarDesc": {"message": "This action cannot be done in the sidebar, please retry the action in the popup or popout."}, "personalOwnershipSubmitError": {"message": "Politika podjetja določa, da ne morete shranjevati elementov v svoj osebni trezor. Spremenite lastništvo na organizacijo in izberite izmed zbirk na voljo."}, "personalOwnershipPolicyInEffect": {"message": "An organization policy is affecting your ownership options."}, "personalOwnershipPolicyInEffectImports": {"message": "An organization policy has blocked importing items into your individual vault."}, "domainsTitle": {"message": "Domains", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "Izključene domene"}, "excludedDomainsDesc": {"message": "Za te domene Bitwarden ne bo predlagal shranjevanja prijavnih podatkov. Sprememba nastavitev stopi v veljavo šele, ko osvežite stran."}, "excludedDomainsDescAlt": {"message": "Bitwarden will not ask to save login details for these domains for all logged in accounts. You must refresh the page for changes to take effect."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ ni veljavna domena", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Pošiljke", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "<PERSON><PERSON><PERSON>"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "Datoteka"}, "allSends": {"message": "Vse pošiljke", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "Poteklo"}, "passwordProtected": {"message": "Password protected"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "<PERSON><PERSON><PERSON> povezavo pošiljke", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "Odstrani geslo"}, "delete": {"message": "Izbriši"}, "removedPassword": {"message": "Geslo odstranjeno"}, "deletedSend": {"message": "Pošiljka izbrisana", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Povezava pošiljke", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "Onemogočeno"}, "removePasswordConfirmation": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, da želite odstraniti geslo?"}, "deleteSend": {"message": "Izbriši pošiljko", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "<PERSON><PERSON> prepri<PERSON>, da želite izbrisati to pošiljko?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "<PERSON>redi <PERSON>o", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "<PERSON>tum <PERSON>"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "<PERSON><PERSON>eka"}, "oneDay": {"message": "1 dan"}, "days": {"message": "$DAYS$ dni", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "Po meri"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "Nova pošiljka", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "Novo geslo"}, "sendDisabled": {"message": "Pošiljka odstranjena", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Pravila podjetja določajo, da lahko izbrišete le obstoječo pošiljko.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Pošiljka ustvar<PERSON>na", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "<PERSON>š<PERSON>jk<PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "In order to choose a file, open the extension in the sidebar (if possible) or pop out to a new window by clicking this banner."}, "sendFirefoxFileWarning": {"message": "In order to choose a file using Firefox, open the extension in the sidebar or pop out to a new window by clicking this banner."}, "sendSafariFileWarning": {"message": "In order to choose a file using Safari, pop out to a new window by clicking this banner."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "Preden pričnete"}, "expirationDateIsInvalid": {"message": "Datum poteka ni veljaven."}, "deletionDateIsInvalid": {"message": "Datum izbrisa ni veljaven."}, "expirationDateAndTimeRequired": {"message": "Datum in čas poteka sta obvezna."}, "deletionDateAndTimeRequired": {"message": "Datum in čas izbrisa sta obvezna."}, "dateParsingError": {"message": "Pri shranjevanju datumov poteka in izbrisa je prišlo do napake."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "Ponovno zahtevaj glavno geslo"}, "passwordConfirmation": {"message": "<PERSON><PERSON><PERSON><PERSON> glavnega gesla"}, "passwordConfirmationDesc": {"message": "To dejanje je zaščiteno. Za nadaljevanje vpišite svoje glavno geslo, da potrdite svojo istovetnost."}, "emailVerificationRequired": {"message": "Potrebna je potrditev e-naslova"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerificationRequiredDesc": {"message": "Za uporabo te funkcionalnosti morate potrditi svoj e-naslov. To lahko storite v spletnem trezorju."}, "updatedMasterPassword": {"message": "Posodobi glavno geslo"}, "updateMasterPassword": {"message": "Spremeni glavno geslo"}, "updateMasterPasswordWarning": {"message": "Your master password was recently changed by an administrator in your organization. In order to access the vault, you must update it now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "updateWeakMasterPasswordWarning": {"message": "Your master password does not meet one or more of your organization policies. In order to access the vault, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "Automatic enrollment"}, "resetPasswordAutoEnrollInviteWarning": {"message": "This organization has an enterprise policy that will automatically enroll you in password reset. Enrollment will allow organization administrators to change your master password."}, "selectFolder": {"message": "Izberi mapo..."}, "noFoldersFound": {"message": "No folders found", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Verification required", "description": "Default title for the user verification dialog."}, "hours": {"message": "<PERSON><PERSON>"}, "minutes": {"message": "Minut"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "Your organization policies have set your maximum allowed vault timeout to $HOURS$ hour(s) and $MINUTES$ minute(s).", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Your organization policies are affecting your vault timeout. Maximum allowed vault timeout is $HOURS$ hour(s) and $MINUTES$ minute(s). Your vault timeout action is set to $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Your organization policies have set your vault timeout action to $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Your vault timeout exceeds the restrictions set by your organization."}, "vaultExportDisabled": {"message": "Vault export unavailable"}, "personalVaultExportPolicyInEffect": {"message": "One or more organization policies prevents you from exporting your individual vault."}, "copyCustomFieldNameInvalidElement": {"message": "Unable to identify a valid form element. Try inspecting the HTML instead."}, "copyCustomFieldNameNotUnique": {"message": "No unique identifier found."}, "convertOrganizationEncryptionDesc": {"message": "$ORGANIZATION$ is using SSO with a self-hosted key server. A master password is no longer required to log in for members of this organization.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "leaveOrganization": {"message": "Leave organization"}, "removeMasterPassword": {"message": "Remove master password"}, "removedMasterPassword": {"message": "Master password removed"}, "leaveOrganizationConfirmation": {"message": "Are you sure you want to leave this organization?"}, "leftOrganization": {"message": "You have left the organization."}, "toggleCharacterCount": {"message": "Prikaži/skrij št. znakov"}, "sessionTimeout": {"message": "Your session has timed out. Please go back and try logging in again."}, "exportingPersonalVaultTitle": {"message": "Exporting individual vault"}, "exportingIndividualVaultDescription": {"message": "Only the individual vault items associated with $EMAIL$ will be exported. Organization vault items will not be included. Only vault item information will be exported and will not include associated attachments.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Napaka"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Ustvari uporabniško ime"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Plus addressed email", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Use your email provider's sub-addressing capabilities."}, "catchallEmail": {"message": "Catch-all email"}, "catchallEmailDesc": {"message": "Use your domain's configured catch-all inbox."}, "random": {"message": "Naključno"}, "randomWord": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>a"}, "websiteName": {"message": "<PERSON>me spletne strani"}, "service": {"message": "Service"}, "forwardedEmail": {"message": "Forwarded email alias"}, "forwardedEmailDesc": {"message": "Generate an email alias with an external forwarding service."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generated by <PERSON><PERSON><PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Invalid $SERVICENAME$ domain.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Invalid $SERVICENAME$ url.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Unknown $SERVICENAME$ error occurred.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Hostname", "description": "Part of a URL."}, "apiAccessToken": {"message": "API Access Token"}, "apiKey": {"message": "API Key"}, "ssoKeyConnectorError": {"message": "Key connector error: make sure key connector is available and working correctly."}, "premiumSubcriptionRequired": {"message": "Potrebno je premium članstvo"}, "organizationIsDisabled": {"message": "Organization suspended."}, "disabledOrganizationFilterError": {"message": "Do elementov v suspendiranih organizacijah ne morete dostopati. Za pomoč se obrnite na lastnika svoje organizacije."}, "loggingInTo": {"message": "Logging in to $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Verzija strežnika"}, "selfHostedServer": {"message": "self-hosted"}, "thirdParty": {"message": "Third-party"}, "thirdPartyServerMessage": {"message": "Connected to third-party server implementation, $SERVERNAME$. Please verify bugs using the official server, or report them to the third-party server.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "last seen on: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Log in with master password"}, "newAroundHere": {"message": "New around here?"}, "rememberEmail": {"message": "Zapomni si e-pošto"}, "loginWithDevice": {"message": "Log in with device"}, "fingerprintPhraseHeader": {"message": "Identifikacijsko geslo"}, "fingerprintMatchInfo": {"message": "<PERSON>si<PERSON>, preverite, da je vaš trezor od<PERSON> in da se identifikacijski gesli na tej in drugi napravi ujemata."}, "resendNotification": {"message": "Resend notification"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "A notification has been sent to your device."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "<PERSON><PERSON> initiated"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "Exposed Master Password"}, "exposedMasterPasswordDesc": {"message": "Password found in a data breach. Use a unique password to protect your account. Are you sure you want to use an exposed password?"}, "weakAndExposedMasterPassword": {"message": "Weak and Exposed Master Password"}, "weakAndBreachedMasterPasswordDesc": {"message": "Weak password identified and found in a data breach. Use a strong and unique password to protect your account. Are you sure you want to use this password?"}, "checkForBreaches": {"message": "Preverite, ali je bilo geslo izpostavljeno v krajah podatkov"}, "important": {"message": "Pomembno:"}, "masterPasswordHint": {"message": "Če pozabite glavno geslo, ga ne bo mogoče povrniti!"}, "characterMinimum": {"message": "$LENGTH$ character minimum", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "V pravilih vaše organizacije je vklopljeno samodejno izpolnjevanje."}, "howToAutofill": {"message": "<PERSON><PERSON> samodejno izpolnjevanje"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "Razumem"}, "autofillSettings": {"message": "Nastavitve"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Manage shortcuts"}, "autofillShortcut": {"message": "Bližnjica za samodejno izpolnjevanje"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Privzeta bližnjica za samodejno izpolnjevanje: $COMMAND$", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Odpre se v novem oknu"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Device approval required. Select an approval option below:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "Remember this device"}, "uncheckIfPublicDevice": {"message": "Uncheck if using a public device"}, "approveFromYourOtherDevice": {"message": "Approve from your other device"}, "requestAdminApproval": {"message": "Request admin approval"}, "ssoIdentifierRequired": {"message": "Organization SSO identifier is required."}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Follow the link in the email sent to"}, "andContinueCreatingYourAccount": {"message": "and continue creating your account."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "Access denied. You do not have permission to view this page."}, "general": {"message": "General"}, "display": {"message": "Display"}, "accountSuccessfullyCreated": {"message": "Account successfully created!"}, "adminApprovalRequested": {"message": "Admin approval requested"}, "adminApprovalRequestSentToAdmins": {"message": "Your request has been sent to your admin."}, "troubleLoggingIn": {"message": "Trouble logging in?"}, "loginApproved": {"message": "Login approved"}, "userEmailMissing": {"message": "User email missing"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "<PERSON><PERSON> trusted"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsNoItemsTitle": {"message": "No active Sends", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsNoItemsMessage": {"message": "Use Send to securely share encrypted information with anyone.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Input is required."}, "required": {"message": "required"}, "search": {"message": "Search"}, "inputMinLength": {"message": "Input must be at least $COUNT$ characters long.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Input must not exceed $COUNT$ characters in length.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "The following characters are not allowed: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Input value must be at least $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Input value must not exceed $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 or more emails are invalid"}, "inputTrimValidator": {"message": "Input must not contain only whitespace.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Input is not an email address."}, "fieldsNeedAttention": {"message": "$COUNT$ field(s) above need your attention.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Select --"}, "multiSelectPlaceholder": {"message": "-- Type to filter --"}, "multiSelectLoading": {"message": "Retrieving options..."}, "multiSelectNotFound": {"message": "No items found"}, "multiSelectClearAll": {"message": "Clear all"}, "plusNMore": {"message": "+ $QUANTITY$ more", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Submenu"}, "toggleCollapse": {"message": "Toggle collapse", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "Alias domain"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Items with master password re-prompt cannot be autofilled on page load. Autofill on page load turned off.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Autofill on page load set to use default setting.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Turn off master password re-prompt to edit this field", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Skip to content"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden autofill menu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Unlock your account to view matching logins", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Unlock account", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Fill credentials for", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Partial username", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "No items to show", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "New item", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Add new vault item", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "New identity", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Turn on"}, "ignore": {"message": "Ignore"}, "importData": {"message": "Import data", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Import error"}, "importErrorDesc": {"message": "There was a problem with the data you tried to import. Please resolve the errors listed below in your source file and try again."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Resolve the errors below and try again."}, "description": {"message": "Description"}, "importSuccess": {"message": "Data successfully imported"}, "importSuccessNumberOfItems": {"message": "A total of $AMOUNT$ items were imported.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Try again"}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "Set PIN"}, "verifyWithBiometrics": {"message": "Verify with biometrics"}, "awaitingConfirmation": {"message": "Awaiting confirmation"}, "couldNotCompleteBiometrics": {"message": "Could not complete biometrics."}, "needADifferentMethod": {"message": "Need a different method?"}, "useMasterPassword": {"message": "Use master password"}, "usePin": {"message": "Use PIN"}, "useBiometrics": {"message": "Use biometrics"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Resend code"}, "total": {"message": "Total"}, "importWarning": {"message": "You are importing data to $ORGANIZATION$. Your data may be shared with members of this organization. Do you want to proceed?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Duo two-step login is required for your account."}, "popoutExtension": {"message": "Popout extension"}, "launchDuo": {"message": "Launch Duo"}, "importFormatError": {"message": "Data is not formatted correctly. Please check your import file and try again."}, "importNothingError": {"message": "Nothing was imported."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Learn about your import options"}, "selectImportFolder": {"message": "Select a folder"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "confirmFilePassword": {"message": "Confirm file password"}, "exportSuccess": {"message": "Vault data exported"}, "typePasskey": {"message": "Passkey"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "Passkey will not be copied"}, "passkeyNotCopiedAlert": {"message": "The passkey will not be copied to the cloned item. Do you want to continue cloning this item?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verification required by the initiating site. This feature is not yet implemented for accounts without master password."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "A passkey already exists for this application."}, "noPasskeysFoundForThisApplication": {"message": "No passkeys found for this application."}, "noMatchingPasskeyLogin": {"message": "You do not have a matching login for this site."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "Confirm"}, "savePasskey": {"message": "Save passkey"}, "savePasskeyNewLogin": {"message": "Save passkey as new login"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "<PERSON><PERSON> Item"}, "overwritePasskey": {"message": "Overwrite passkey?"}, "overwritePasskeyAlert": {"message": "This item already contains a passkey. Are you sure you want to overwrite the current passkey?"}, "featureNotSupported": {"message": "Feature not yet supported"}, "yourPasskeyIsLocked": {"message": "Authentication required to use passkey. Verify your identity to continue."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "Incorrect username or password"}, "incorrectPassword": {"message": "Incorrect password"}, "incorrectCode": {"message": "Incorrect code"}, "incorrectPin": {"message": "Incorrect PIN"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "LastPass Email"}, "importingYourAccount": {"message": "Importing your account..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "LastPass master password"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Import directly from LastPass"}, "importFromCSV": {"message": "Import from CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Collection"}, "lastPassYubikeyDesc": {"message": "Insert the YubiKey associated with your LastPass account into your computer's USB port, then touch its button."}, "switchAccount": {"message": "Switch account"}, "switchAccounts": {"message": "Switch accounts"}, "switchToAccount": {"message": "Switch to account"}, "activeAccount": {"message": "Active account"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Available accounts"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "server"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Just once"}, "alwaysForThisSite": {"message": "Always for this site"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ added to excluded domains.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Make <PERSON><PERSON><PERSON> your default password manager?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ignoring this option may cause conflicts between Bitwarden autofill suggestions and your browser's.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Make Bitwarden your default password manager", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Unable to set <PERSON><PERSON><PERSON> as the default password manager", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "You must grant browser privacy permissions to Bitwarden to set it as the default password manager.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Make default", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Credentials saved successfully!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Credentials updated successfully!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Error saving credentials. Check console for details.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Success"}, "removePasskey": {"message": "Remove passkey"}, "passkeyRemoved": {"message": "Passkey removed"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "accountSecurity": {"message": "Account security"}, "notifications": {"message": "Notifications"}, "appearance": {"message": "Appearance"}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "New"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "Owner"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Additional information"}, "itemHistory": {"message": "Item history"}, "lastEdited": {"message": "Last edited"}, "ownerYou": {"message": "Owner: You"}, "linked": {"message": "Linked"}, "copySuccessful": {"message": "Copy Successful"}, "upload": {"message": "Upload"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "deleteAttachmentName": {"message": "Delete attachment $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Download $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "Filters"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "downloadAttachment": {"message": "Download - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Login credentials"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "Add account"}, "loading": {"message": "Loading"}, "data": {"message": "Data"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Add field"}, "add": {"message": "Add"}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Edit field"}, "editFieldLabel": {"message": "Edit $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Delete $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ added", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Item Location"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "Compact mode"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBody": {"message": "Autofill items for the current page\nFavorite items for easy access\nSearch your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}}