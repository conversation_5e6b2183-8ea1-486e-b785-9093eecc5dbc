{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "Bitwarden Password Manager", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "At home, at work, or on the go, <PERSON><PERSON><PERSON> easily secures all your passwords, passkeys, and sensitive information", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "Maglog-in o gumawa ng bagong account para ma-access ang iyong ligtas na kahadeyero."}, "inviteAccepted": {"message": "Invitation accepted"}, "createAccount": {"message": "Gumawa ng Account"}, "newToBitwarden": {"message": "New to Bitwarden?"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "Welcome back"}, "setAStrongPassword": {"message": "Set a strong password"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Finish creating your account by setting a password"}, "enterpriseSingleSignOn": {"message": "Enterprise Single Sign-On sa Filipino ay Isang Sign-On na Enterprise"}, "cancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "close": {"message": "Isara"}, "submit": {"message": "Isumite"}, "emailAddress": {"message": "Email Address"}, "masterPass": {"message": "Master Password"}, "masterPassDesc": {"message": "Ang master password ay ang password na gagamitin mo upang ma-access ang iyong kahadeyero. Napakaimportante na hindi mo makalimutan ang iyong master password. Walang paraan upang ma-recover ang password kapag nakalimutan mo ito."}, "masterPassHintDesc": {"message": "May isang pahiwatig para sa master password na makakatulong na maalala mo ang iyong password kapag nakalimutan mo ito."}, "masterPassHintText": {"message": "If you forget your password, the password hint can be sent to your email. $CURRENT$/$MAXIMUM$ character maximum.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Muling i-type ang Master Password"}, "masterPassHint": {"message": "<PERSON><PERSON><PERSON><PERSON> sa Master Password (opsyonal)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Join organization"}, "joinOrganizationName": {"message": "Join $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Finish joining this organization by setting a master password."}, "tab": {"message": "Tab"}, "vault": {"message": "<PERSON><PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON>"}, "allVaults": {"message": "Lahat ng Vault"}, "tools": {"message": "Mga Kagamitan"}, "settings": {"message": "Mga Preperensya"}, "currentTab": {"message": "Pangkasalukuyang Tab"}, "copyPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> ang Password"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>"}, "copyUri": {"message": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>"}, "copyUsername": {"message": "Ko<PERSON>ahin ang pangalan ng gumagamit"}, "copyNumber": {"message": "Pamamagitan ng Kopya ng Bilang"}, "copySecurityCode": {"message": "Kopyahin ang code ng seguridad"}, "copyName": {"message": "Copy name"}, "copyCompany": {"message": "Copy company"}, "copySSN": {"message": "Copy Social Security number"}, "copyPassportNumber": {"message": "Copy passport number"}, "copyLicenseNumber": {"message": "Copy license number"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "Copy notes"}, "copy": {"message": "Copy", "description": "Copy to clipboard"}, "fill": {"message": "Fill", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Auto-fill sa Filipino ay Awtomatikong Pagpuno"}, "autoFillLogin": {"message": "Auto-fill na pag-login"}, "autoFillCard": {"message": "Auto-fill card"}, "autoFillIdentity": {"message": "Awtomatikong punan ang pagkakakilanlan"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "Maglagay ng Password"}, "copyElementIdentifier": {"message": "<PERSON><PERSON> ang Pangalan ng Pasadyang Field"}, "noMatchingLogins": {"message": "Walang tumutugmang mga login"}, "noCards": {"message": "Walang card"}, "noIdentities": {"message": "Walang pagkaka<PERSON>lanlan"}, "addLoginMenu": {"message": "Magdagdag ng login"}, "addCardMenu": {"message": "Magdagdag ng card"}, "addIdentityMenu": {"message": "Magdagdag ng pagkakakilanlan"}, "unlockVaultMenu": {"message": "<PERSON><PERSON><PERSON> ang iyong <PERSON>"}, "loginToVaultMenu": {"message": "Ag-log in sa iyong bangko"}, "autoFillInfo": {"message": "Walang mga login na magagamit para i-auto-fill para sa kasalukuyang tab ng browser."}, "addLogin": {"message": "Magdagdag ng Login"}, "addItem": {"message": "Magdagdag ng Item"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "<PERSON><PERSON><PERSON> ang <PERSON> Password na Hint"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sendVerificationCode": {"message": "Magpadala ng isang verification code sa iyong email"}, "sendCode": {"message": "Magpadala ng Code"}, "codeSent": {"message": "Ipinadala ang Code"}, "verificationCode": {"message": "VerificationCode sa Filipino ay Pagsasagot sa Tanong"}, "confirmIdentity": {"message": "Ku<PERSON><PERSON><PERSON><PERSON> ang iyong identididad upang magpatuloy."}, "changeMasterPassword": {"message": "<PERSON><PERSON><PERSON> ang Master Password"}, "continueToWebApp": {"message": "Continue to web app?"}, "continueToWebAppDesc": {"message": "Explore more features of your Bitwarden account on the web app."}, "continueToHelpCenter": {"message": "Continue to Help Center?"}, "continueToHelpCenterDesc": {"message": "Learn more about how to use Bitwarden on the Help Center."}, "continueToBrowserExtensionStore": {"message": "Continue to browser extension store?"}, "continueToBrowserExtensionStoreDesc": {"message": "Help others find out if <PERSON><PERSON><PERSON> is right for them. Visit your browser's extension store and leave a rating now."}, "changeMasterPasswordOnWebConfirmation": {"message": "You can change your master password on the Bitwarden web app."}, "fingerprintPhrase": {"message": "Hulmabig ng Hilik ng Dako", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Ang fingerprint pala ng iyong account", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Dalawahang-hak<PERSON> na <PERSON>g-login"}, "logOut": {"message": "Mag-Log Out"}, "aboutBitwarden": {"message": "About Bitwarden"}, "about": {"message": "Tungkol"}, "moreFromBitwarden": {"message": "More from Bitwarden"}, "continueToBitwardenDotCom": {"message": "Continue to bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden for Business"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden Authenticator allows you to store authenticator keys and generate TOTP codes for 2-step verification flows. Learn more on the bitwarden.com website"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Securely store, manage, and share developer secrets with Bitwarden Secrets Manager. Learn more on the bitwarden.com website."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Create smooth and secure login experiences free from traditional passwords with Passwordless.dev. Learn more on the bitwarden.com website."}, "freeBitwardenFamilies": {"message": "Free Bitwarden Families"}, "freeBitwardenFamiliesPageDesc": {"message": "You are eligible for Free Bitwarden Families. Redeem this offer today in the web app."}, "version": {"message": "<PERSON><PERSON><PERSON>"}, "save": {"message": "I-save"}, "move": {"message": "Lumipat"}, "addFolder": {"message": "Magdagdag ng folder"}, "name": {"message": "<PERSON><PERSON><PERSON>"}, "editFolder": {"message": "I-edit ang folder"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "New folder"}, "folderName": {"message": "Folder name"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "<PERSON><PERSON><PERSON> ang folder"}, "folders": {"message": "Mga Folder"}, "noFolders": {"message": "Walang mga folder na listahan."}, "helpFeedback": {"message": "<PERSON><PERSON> at Mga Feedback"}, "helpCenter": {"message": "Bitwarden <PERSON> sentro"}, "communityForums": {"message": "I-eksplorang Bitwarden komunidad na mga forum"}, "contactSupport": {"message": "Kontakin ang Bitwarden suporta"}, "sync": {"message": "<PERSON><PERSON><PERSON>"}, "syncVaultNow": {"message": "Isingit ang <PERSON> ngayon"}, "lastSync": {"message": "<PERSON><PERSON>:"}, "passGen": {"message": "Tagapaglikha ng Password"}, "generator": {"message": "Magmamana", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Automatiko na gumawa ng mga malakas at natatanging mga password para sa iyong mga logins."}, "bitWebVaultApp": {"message": "Bitwarden web app"}, "importItems": {"message": "Isingit ang <PERSON> ngayon"}, "select": {"message": "<PERSON><PERSON><PERSON>"}, "generatePassword": {"message": "Magtatag ng Password"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "Muling I-generate ang Password"}, "options": {"message": "<PERSON>ga <PERSON>"}, "length": {"message": "<PERSON><PERSON><PERSON>"}, "include": {"message": "Include", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Include numbers", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Include special characters", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "Ang bilang ng mga salita\n\nNumero ng mga salita"}, "wordSeparator": {"message": "Separador ng salita"}, "capitalize": {"message": "Pagkapital", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "Isama ang numero"}, "minNumbers": {"message": "Pinakamababang mga numero"}, "minSpecial": {"message": "Inakamababang espesyal"}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "<PERSON><PERSON><PERSON> ang vault"}, "edit": {"message": "I-edit"}, "view": {"message": "Tan<PERSON>"}, "noItemsInList": {"message": "Walang mga bagay na maipapakita."}, "itemInformation": {"message": "Impormasyon ng item"}, "username": {"message": "Ang pangalan ng tagagamit"}, "password": {"message": "Ang Password"}, "totp": {"message": "Authenticator secret"}, "passphrase": {"message": "Pasa salita"}, "favorite": {"message": "<PERSON>"}, "unfavorite": {"message": "Unfavorite"}, "itemAddedToFavorites": {"message": "Item added to favorites"}, "itemRemovedFromFavorites": {"message": "<PERSON><PERSON> removed from favorites"}, "notes": {"message": "<PERSON><PERSON> nota"}, "privateNote": {"message": "Private note"}, "note": {"message": "<PERSON>"}, "editItem": {"message": "<PERSON><PERSON><PERSON> ang <PERSON>"}, "folder": {"message": "Folder/Direktoryo"}, "deleteItem": {"message": "<PERSON><PERSON><PERSON> ang <PERSON>"}, "viewItem": {"message": "<PERSON><PERSON><PERSON> ang <PERSON>"}, "launch": {"message": "Paglulunsad"}, "launchWebsite": {"message": "Launch website"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Website"}, "toggleVisibility": {"message": "I-toggle ang ka<PERSON>hang makita"}, "manage": {"message": "<PERSON><PERSON><PERSON>"}, "other": {"message": "Iba pa"}, "unlockMethods": {"message": "Unlock options"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Mag-set up ng paraan ng pag-unlock upang baguhin ang iyong pagkilos sa pag-timeout ng vault."}, "unlockMethodNeeded": {"message": "Set up an unlock method in Settings"}, "sessionTimeoutHeader": {"message": "Session timeout"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "Other options"}, "rateExtension": {"message": "I-rate ang extension"}, "browserNotSupportClipboard": {"message": "Hindi suportado ng iyong web browser ang madaling pag-copy ng clipboard. <PERSON><PERSON><PERSON> ito manually sa halip."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "Naka-lock ang iyong vault. <PERSON><PERSON><PERSON> ang iyong pagkakakilanlan upang magpatuloy."}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "<PERSON><PERSON><PERSON>"}, "loggedInAsOn": {"message": "Nakalog-in bilang $EMAIL$ sa $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Hindi wasto ang master password"}, "vaultTimeout": {"message": "Vault timeout"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "Mag<PERSON>kanda<PERSON>"}, "lockAll": {"message": "Lock all"}, "immediately": {"message": "Kaagad"}, "tenSeconds": {"message": "10 segundo"}, "twentySeconds": {"message": "<PERSON><PERSON><PERSON><PERSON>'t segundo"}, "thirtySeconds": {"message": "Tatlumpung segundo"}, "oneMinute": {"message": "1 minuto"}, "twoMinutes": {"message": "2 mga minuto"}, "fiveMinutes": {"message": "5 mga minuto"}, "fifteenMinutes": {"message": "15 mga minuto"}, "thirtyMinutes": {"message": "30 minuto"}, "oneHour": {"message": "1 oras"}, "fourHours": {"message": "4 oras"}, "onLocked": {"message": "Sa pag-lock ng sistema"}, "onRestart": {"message": "Sa pag-restart ng browser"}, "never": {"message": "Hindi kailanman"}, "security": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmMasterPassword": {"message": "Confirm master password"}, "masterPassword": {"message": "Master password"}, "masterPassImportant": {"message": "Your master password cannot be recovered if you forget it!"}, "masterPassHintLabel": {"message": "Master password hint"}, "errorOccurred": {"message": "<PERSON><PERSON><PERSON><PERSON> ng error"}, "emailRequired": {"message": "<PERSON><PERSON>kailangan ang email address."}, "invalidEmail": {"message": "Di-wasto na email address."}, "masterPasswordRequired": {"message": "<PERSON>ng address ng email."}, "confirmMasterPasswordRequired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ang ulitin ang master password."}, "masterPasswordMinlength": {"message": "Ang master password ay dapat na hindi bababa sa $VALUE$ na mga character.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Hindi tumutugma ang kumpir<PERSON>yon ng master password."}, "newAccountCreated": {"message": "Na<PERSON><PERSON> na ang iyong bagong account! Maaari ka nang mag-log in."}, "newAccountCreated2": {"message": "Your new account has been created!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "youSuccessfullyLoggedIn": {"message": "You successfully logged in"}, "youMayCloseThisWindow": {"message": "You may close this window"}, "masterPassSent": {"message": "<PERSON>nadala na namin sa iyo ang email na may hint ng master password mo."}, "verificationCodeRequired": {"message": "Kinakailangan ang verification code."}, "webauthnCancelOrTimeout": {"message": "The authentication was cancelled or took too long. Please try again."}, "invalidVerificationCode": {"message": "Maling verification code"}, "valueCopied": {"message": "Kinopya ang $VALUE$", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Hindi makapag-auto-fill ng napiling item sa pahinang ito. <PERSON><PERSON><PERSON> at i-paste ang impormasyon sa halip."}, "totpCaptureError": {"message": "Unable to scan QR code from the current webpage"}, "totpCaptureSuccess": {"message": "Authenticator key added"}, "totpCapture": {"message": "Scan authenticator QR code from current webpage"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Copy Authenticator key (TOTP)"}, "loggedOut": {"message": "Umalis na"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "Nag-expire na ang iyong session sa login."}, "logIn": {"message": "Log in"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "<PERSON><PERSON><PERSON> ka bang gusto mong mag-log out?"}, "yes": {"message": "Oo"}, "no": {"message": "Hindi"}, "location": {"message": "Location"}, "unexpectedError": {"message": "<PERSON><PERSON> ang isang hindi in<PERSON><PERSON><PERSON> error."}, "nameRequired": {"message": "<PERSON><PERSON><PERSON> ay kin<PERSON>n."}, "addedFolder": {"message": "Idinagdag na folder"}, "twoStepLoginConfirmation": {"message": "Ang two-step login ay nagpapagaan sa iyong account sa pamamagitan ng pag-verify sa iyong login sa isa pang device tulad ng security key, authenticator app, SMS, tawag sa telepono o email. Ang two-step login ay maaaring magawa sa bitwarden.com web vault. Gusto mo bang bisitahin ang website ngayon?"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "Nai-save na folder"}, "deleteFolderConfirmation": {"message": "<PERSON><PERSON><PERSON> ka bang gusto mong tanggalin ang folder na ito?"}, "deletedFolder": {"message": "Tinanggal na folder"}, "gettingStartedTutorial": {"message": "Tutoran sa Pag-uump<PERSON>"}, "gettingStartedTutorialVideo": {"message": "Panoorin ang aming tutoran sa pag-uumpisa upang matuto kung paano makakakuha ng pinakamataas na kapakinabangan mula sa extension ng browser."}, "syncingComplete": {"message": "Ang pag-sync ay nak<PERSON>leto"}, "syncingFailed": {"message": "Ang pag-sync ay nabigo"}, "passwordCopied": {"message": "Ang password ay nacopy"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "Bagong URI"}, "addDomain": {"message": "Add domain", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "Ang item ay idinagdag"}, "editedItem": {"message": "Ang item ay nai-save"}, "deleteItemConfirmation": {"message": "Gusto mo bang talagang ipadala sa basura?"}, "deletedItem": {"message": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>em sa basurahan"}, "overwritePassword": {"message": "<PERSON><PERSON>an ang password"}, "overwritePasswordConfirmation": {"message": "<PERSON><PERSON><PERSON> ka bang gusto mong palitan ang kasalukuyang password?"}, "overwriteUsername": {"message": "Palitan ang username"}, "overwriteUsernameConfirmation": {"message": "<PERSON><PERSON><PERSON> ka bang gusto mong palitan ang kasalukuyang username?"}, "searchFolder": {"message": "Maghanap ng folder"}, "searchCollection": {"message": "Maghanap ng koleksyon"}, "searchType": {"message": "Maghanap ng uri"}, "noneFolder": {"message": "Walang folder", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "Tanungin na magdagdag ng login"}, "vaultSaveOptionsTitle": {"message": "Save to vault options"}, "addLoginNotificationDesc": {"message": "Tanungin na magdagdag ng isang item kung wala itong nakita sa iyong vault."}, "addLoginNotificationDescAlt": {"message": "Hilingin na magdagdag ng isang item kung ang isa ay hindi mahanap sa iyong vault. Nalalapat sa lahat ng naka-log in na account."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "Ipakita ang mga card sa Tab page"}, "showCardsCurrentTabDesc": {"message": "Itala ang mga item ng card sa Tab page para sa madaling auto-fill."}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "Ipakita ang mga pagkatao sa Tab page"}, "showIdentitiesCurrentTabDesc": {"message": "Itala ang mga item ng pagkatao sa Tab page para sa madaling auto-fill."}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "Linisin ang clipboard", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Awtomatikong linisin ang mga kopya mula sa iy.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Dapat bang tandaan ng Bitwarden ang password na ito para sa iyo?"}, "notificationAddSave": {"message": "I-save"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "loginSaveConfirmation": {"message": "$ITEMNAME$ saved to Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is saved."}, "loginUpdatedConfirmation": {"message": "$ITEMNAME$ updated in Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is updated."}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Tanungin ang update ng umiiral na login"}, "changedPasswordNotificationDesc": {"message": "<PERSON><PERSON><PERSON> ang update ng password ng isang login kapag napansin ang pagbabago sa websi."}, "changedPasswordNotificationDescAlt": {"message": "Hilingin na i-update ang password ng login kapag may nakitang pagbabago sa isang website. Nalalapat sa lahat ng naka-log in na account.\nI-unlock ang iyong Bitwarden vault para makumpleto ang kahilingan sa auto-fill."}, "enableUsePasskeys": {"message": "Ask to save and use passkeys"}, "usePasskeysDesc": {"message": "Ask to save new passkeys or log in with passkeys stored in your vault. Applies to all logged in accounts."}, "notificationChangeDesc": {"message": "Nais mo bang i-update ang password na ito sa Bitwarden?"}, "notificationChangeSave": {"message": "I-update"}, "notificationUnlockDesc": {"message": "I-unlock ang iyong <PERSON> vault para maku<PERSON>to ang kahilingan sa auto-fill."}, "notificationUnlock": {"message": "I-unlock"}, "additionalOptions": {"message": "Additional options"}, "enableContextMenuItem": {"message": "Ipakita ang mga opsyon ng menu ng konteksto"}, "contextMenuItemDesc": {"message": "G<PERSON><PERSON> ang pangalawang pag-click upang ma-access ang password generation at matching logins para sa website. "}, "contextMenuItemDescAlt": {"message": "Gumamit ng pangalawang pag-click upang ma-access ang pagbuo ng password at pagtutugma ng mga login para sa website. Nalalapat sa lahat ng naka-log in na account."}, "defaultUriMatchDetection": {"message": "Default na pagtukoy ng tugma ng URI", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Pumili ng default na paraan ng paghanda ng URI match detection para sa mga login kapag ginagawa ang mga aksyon tulad ng auto-fill."}, "theme": {"message": "Tagapagmana"}, "themeDesc": {"message": "Baguhin ang tema ng kulay ng application."}, "themeDescAlt": {"message": "Baguhin ang tema ng kulay ng application. Nalalapat sa lahat ng naka-log in na account."}, "dark": {"message": "<PERSON><PERSON><PERSON>", "description": "Dark color"}, "light": {"message": "<PERSON><PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "I-export vault"}, "fileFormat": {"message": "Format ng file"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "File password"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "warning": {"message": "BABALA", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Tanggapin ang pag-export ng vault"}, "exportWarningDesc": {"message": "Ang export na ito ay naglalaman ng iyong data sa vault sa isang hindi naka-encrypt na format. Hindi mo dapat i-store o ipadala ang naka-export na file sa pamamagitan ng mga hindi secure na channel (gaya ng email). Tanggalin agad ito pagkatapos mong gamitin."}, "encExportKeyWarningDesc": {"message": "Ang export na ito ay naka-encrypt ng iyong data gamit ang encryption key ng iyong account. Kung kailanman mo i-rotate ang encryption key ng iyong account, dapat mong i-export muli dahil hindi mo na mababawasan ang export file na ito."}, "encExportAccountWarningDesc": {"message": "Ang encryption keys ng account ay isa-isa lamang sa bawat user account ng Bitwarden, kaya hindi mo ma-import ang naka-encrypt na export sa ibang account."}, "exportMasterPassword": {"message": "Ipasok ang iyong master password para i-export ang iyong data sa vault."}, "shared": {"message": "Iniimbak"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "Lumipat sa organisasyon"}, "movedItemToOrg": {"message": "$ITEMNAME$ lumipat sa $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Pumili ng isang organisasyon kung saan mo nais na ipalipat ang item na ito. Ang paglipat sa isang organisasyon ay nagpapalipat ng pagmamay-ari ng item sa organisasyon na iyon. Hindi ka na magiging direktang may-ari ng item na ito kapag naipadala na ito."}, "learnMore": {"message": "<PERSON><PERSON> nang higit pa"}, "authenticatorKeyTotp": {"message": "Susi ng Authenticator (TOTP)"}, "verificationCodeTotp": {"message": "Code ng Pag-verify (TOTP)"}, "copyVerificationCode": {"message": "Paghahawak ng kodigo ng pagpapatunay"}, "attachments": {"message": "Mga Attachment"}, "deleteAttachment": {"message": "Tanggalin ang attachment"}, "deleteAttachmentConfirmation": {"message": "<PERSON><PERSON><PERSON> ka bang gusto mong tanggalin ang attachment na ito?"}, "deletedAttachment": {"message": "Attachment na natanggal"}, "newAttachment": {"message": "Magdagdag ng bagong attachment"}, "noAttachments": {"message": "Walang mga attachment."}, "attachmentSaved": {"message": "Attachment na nai-save"}, "file": {"message": "Mag-file"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "Pumili ng File"}, "maxFileSize": {"message": "Maximum na laki ng file ay 500 MB."}, "featureUnavailable": {"message": "Hindi magagamit ang tampok"}, "encryptionKeyMigrationRequired": {"message": "Kinakailangan ang paglilipat ng encryption key. Mangyaring mag-login sa pamamagitan ng web vault upang i-update ang iyong encryption key."}, "premiumMembership": {"message": "Pagiging miyembro ng premium"}, "premiumManage": {"message": "<PERSON><PERSON><PERSON><PERSON> ang pagiging miyembro"}, "premiumManageAlert": {"message": "<PERSON><PERSON><PERSON> mong i-manage ang iyong membership sa bitwarden.com web vault. <PERSON>to mo bang bisitahin ang website ngayon?"}, "premiumRefresh": {"message": "I-refresh ang membership"}, "premiumNotCurrentMember": {"message": "Hindi ka ngayon ay isang Premium member."}, "premiumSignUpAndGet": {"message": "Mag-sign up para sa isang Premium membership at makakuha ng:"}, "ppremiumSignUpStorage": {"message": "1 GB encrypted storage para sa mga file attachment."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "Pagmamay-ari na dalawang hakbang na opsyon sa pag-log in gaya ng YubiKey at Duo."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>, kalusugan ng account, at mga ulat sa data breach upang panatilihing ligtas ang iyong vault."}, "ppremiumSignUpTotp": {"message": "TOTP Verification Code (2FA) Generator para sa mga login sa iyong vault."}, "ppremiumSignUpSupport": {"message": "Priority suporta sa customer."}, "ppremiumSignUpFuture": {"message": "Lahat ng mga susunod na Premium na tampok. <PERSON><PERSON> pang darating!"}, "premiumPurchase": {"message": "Bilhin ang Premium"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "Ikaw ay isang Premium na miyembro!"}, "premiumCurrentMemberThanks": {"message": "Salamat sa pagsuporta sa Bitwarden."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "Lahat para lamang sa $PRICE$ /taon!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "I-refresh ang lahat"}, "enableAutoTotpCopy": {"message": "<PERSON><PERSON><PERSON> ang TOTP nang automatiko"}, "disableAutoTotpCopyDesc": {"message": "Kapag may authenticator key ang login, kopyahin ang TOTP verification code sa iyong clip-board kapag auto-fill mo ang login."}, "enableAutoBiometricsPrompt": {"message": "Mangyaring humingi ng mga biometrika sa paglunsad"}, "premiumRequired": {"message": "Premium na kinakailangan"}, "premiumRequiredDesc": {"message": "Ang Premium na membership ay kinakailangan upang gamitin ang tampok na ito."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "Veripikasyon na email na ipinadala sa $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "I-insert ang iyong security key sa USB port ng iyong computer. <PERSON> mayroon itong buton, tindigin ito."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "I-authenticate ang <PERSON>"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "Hindi magagamit ang pag-login"}, "noTwoStepProviders": {"message": "<PERSON><PERSON><PERSON> naka-set up na two-step login ang account na ito, ngunit walang sinusuportahang two-step provider ng web browser na ito."}, "noTwoStepProviders2": {"message": "Mangyaring gamitin ang isang suportadong web browser (tulad ng Chrome) at/o magdagdag ng karagdagang mga provider na mas suportado sa ibat ibang web browsers (tulad ng isang authenticator app)."}, "twoStepOptions": {"message": "<PERSON>ga pagpipilian para sa two-step login"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "Nawalan ka ng access sa lahat ng iyong mga two-factor provider? Gamitin ang iyong recovery code para i-off ang lahat ng two-factor providers mula sa iyong account."}, "recoveryCodeTitle": {"message": "Recovery code"}, "authenticatorAppTitle": {"message": "App ng Authenticator"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP Security Key"}, "yubiKeyDesc": {"message": "Gamitin ang YubiKey upang ma-access ang iyong account. Gumagana sa mga YubiKey 4, 4 Nano, 4C, at NEO devices."}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Patunayan sa Duo Security para sa iyong samahan gamit ang Duo Mobile app, SMS, tawag sa telepono, o key ng seguridad ng U2F.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Gamitin ang anumang WebAuthn compatible security key upang ma-access ang iyong account."}, "emailTitle": {"message": "Mag-email"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "selfHostedEnvironment": {"message": "Kapaligirang self-hosted"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "Kapaligirang Custom"}, "baseUrl": {"message": "URL ng Server"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API Server URL"}, "webVaultUrl": {"message": "Web vault server URL - URL ng Server ng Web vault"}, "identityUrl": {"message": "Identity server URL - URL ng Server ng Identity"}, "notificationsUrl": {"message": "URL ng server ng mga abiso"}, "iconsUrl": {"message": "Icons server URL - URL ng Server ng Mga Icon"}, "environmentSaved": {"message": "Nai-save ang mga URL ng Kapaligiran"}, "showAutoFillMenuOnFormFields": {"message": "Show autofill menu on form fields", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Turn off your browser's built in password manager settings to avoid conflicts."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Edit browser settings."}, "autofillOverlayVisibilityOff": {"message": "<PERSON><PERSON><PERSON>", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "When autofill icon is selected", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Awtomatikong punan sa pagkarga ng pahina"}, "enableAutoFillOnPageLoadDesc": {"message": "Kung natukoy ang isang form sa pag login, awtomatikong punan kapag naglo load ang web page."}, "experimentalFeature": {"message": "Ang mga nakompromiso o hindi pinagkakatiwalaang mga website ay maaaring samantalahin ang awtomatikong pagpuno sa pag load ng pahina."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "Matuto nang higit pa tungkol sa auto fill"}, "defaultAutoFillOnPageLoad": {"message": "Default na setting ng autofill para sa mga item sa pag login"}, "defaultAutoFillOnPageLoadDesc": {"message": "<PERSON><PERSON><PERSON> mong i-off ang auto-fill sa pag-load ng pahina para sa indibidwal na login items mula sa view ng Edit ng item."}, "itemAutoFillOnPageLoad": {"message": "Auto-fill sa pag-load ng pahina (kung naka-set up sa Options)"}, "autoFillOnPageLoadUseDefault": {"message": "Gamitin ang default na setting"}, "autoFillOnPageLoadYes": {"message": "Awtomatikong punan sa pagkarga ng pahina"}, "autoFillOnPageLoadNo": {"message": "Huwag awtomatikong punan sa page load"}, "commandOpenPopup": {"message": "<PERSON><PERSON><PERSON> ang popup ng vault"}, "commandOpenSidebar": {"message": "<PERSON><PERSON><PERSON> ang vault sa sidebar"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "<PERSON><PERSON><PERSON> at k<PERSON><PERSON>n ang bagong random na password sa clipboard"}, "commandLockVaultDesc": {"message": "I-lock ang vault"}, "customFields": {"message": "Pasadyang mga patlang"}, "copyValue": {"message": "<PERSON><PERSON><PERSON><PERSON> ang halaga"}, "value": {"message": "<PERSON><PERSON>"}, "newCustomField": {"message": "Bagong custom field"}, "dragToSort": {"message": "I-drag upang i-sort"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "Teksto"}, "cfTypeHidden": {"message": "Nakatago"}, "cfTypeBoolean": {"message": "Boolean"}, "cfTypeCheckbox": {"message": "Checkbox"}, "cfTypeLinked": {"message": "<PERSON><PERSON><PERSON>", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Nilikha ng halaga", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Kapag ikaw ay mag-click sa labas ng popup window para tingnan ang iyong email para sa iyong code ng pagpapatunay, ang popup na ito ay magsasara. Nais mo bang buksan ang popup na ito sa isang bagong window upang hindi ito sarado?"}, "popupU2fCloseMessage": {"message": "Ang browser na ito ay hindi maaaring prosesuhin ang mga hiling ng U2F sa popup window na ito. Nais mo bang buksan ang popup na ito sa isang bagong window upang ma-log in gamit ang U2F?"}, "enableFavicon": {"message": "Ipakita ang mga icon ng website"}, "faviconDesc": {"message": "Ipakita ang isang kilalang larawan sa tabi ng bawat login."}, "faviconDescAlt": {"message": "Show a recognizable image next to each login. Applies to all logged in accounts."}, "enableBadgeCounter": {"message": "Ipakita ang badge counter"}, "badgeCounterDesc": {"message": "Ipahiwatig kung gaano karaming mga login ang mayroon ka para sa kasalukuyang pahina ng web."}, "cardholderName": {"message": "Pangalan ng cardholder"}, "number": {"message": "Numero"}, "brand": {"message": "Brand"}, "expirationMonth": {"message": "Buwan ng Pagpaso"}, "expirationYear": {"message": "Taon ng Pag-expire"}, "expiration": {"message": "Pag-expire"}, "january": {"message": "<PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "march": {"message": "<PERSON><PERSON>"}, "april": {"message": "Abril"}, "may": {"message": "Mayo"}, "june": {"message": "Hu<PERSON>o"}, "july": {"message": "<PERSON><PERSON><PERSON>"}, "august": {"message": "Agosto"}, "september": {"message": "Setyembre"}, "october": {"message": "Oktubre"}, "november": {"message": "Nobyembre"}, "december": {"message": "Disyembre"}, "securityCode": {"message": "Kodigo ng Seguridad"}, "ex": {"message": "ex."}, "title": {"message": "Pamagat"}, "mr": {"message": "Ginoo"}, "mrs": {"message": "Gng"}, "ms": {"message": "Ms"}, "dr": {"message": "Dr"}, "mx": {"message": "Mx"}, "firstName": {"message": "<PERSON><PERSON>"}, "middleName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lastName": {"message": "<PERSON><PERSON>an"}, "fullName": {"message": "<PERSON><PERSON><PERSON>"}, "identityName": {"message": "Pangalan ng Identidad"}, "company": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ssn": {"message": "Numero ng Seguridad Sosyal"}, "passportNumber": {"message": "Numero ng Pasaporte"}, "licenseNumber": {"message": "Numero ng Lisensya"}, "email": {"message": "Mag-email"}, "phone": {"message": "Telepono"}, "address": {"message": "Address"}, "address1": {"message": "Address 1"}, "address2": {"message": "Address 2"}, "address3": {"message": "Address 3"}, "cityTown": {"message": "Bayan/Town"}, "stateProvince": {"message": "Estado/Probinsya"}, "zipPostalCode": {"message": "Código ng Zip / Postal"}, "country": {"message": "Bayan"}, "type": {"message": "<PERSON><PERSON>"}, "typeLogin": {"message": "<PERSON><PERSON>"}, "typeLogins": {"message": "<PERSON><PERSON>"}, "typeSecureNote": {"message": "Secure na tala"}, "typeCard": {"message": "Karta"}, "typeIdentity": {"message": "Pagkakakilanlan"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "New $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Edit $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "View $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "Kasaysayan ng Password"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "Bumalik"}, "collections": {"message": "Koleksyon"}, "nCollections": {"message": "$COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "<PERSON>ga <PERSON>"}, "popOutNewWindow": {"message": "Lumabas sa isang bagong window"}, "refresh": {"message": "I-refresh"}, "cards": {"message": "Mga Karta"}, "identities": {"message": "Mga Identidad"}, "logins": {"message": "<PERSON><PERSON>"}, "secureNotes": {"message": "Mga Secure Note"}, "sshKeys": {"message": "SSH Keys"}, "clear": {"message": "<PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "<PERSON>iin kung ang password ay na-expose."}, "passwordExposed": {"message": "Ang password na ito ay na-expose na $VALUE$ beses sa mga data breach. Dapat mong baguhin ito.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Ang password na ito ay hindi natagpuan sa alinman sa mga alam na data breach. <PERSON><PERSON>i itong gamitin nang ligtas."}, "baseDomain": {"message": "Base domain", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Base domain (recommended)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "Pangalan ng domain", "description": "Domain name. Ex. website.com"}, "host": {"message": "Host", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Eksak<PERSON>"}, "startsWith": {"message": "<PERSON><PERSON><PERSON><PERSON> sa"}, "regEx": {"message": "Regular Expression - Regular na Pagpapahayag", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Match Detection - Pagtuklas ng Pares", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Kamalian ng Default", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "<PERSON>ga pagpipilian sa toggle"}, "toggleCurrentUris": {"message": "Toggle ka<PERSON><PERSON>yang URIs", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "Kasalukuyang URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Organisasyon", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "<PERSON><PERSON> uri"}, "allItems": {"message": "Lahat ng mga item"}, "noPasswordsInList": {"message": "Walang mga password na i-list."}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "<PERSON><PERSON>"}, "default": {"message": "Default na"}, "dateUpdated": {"message": "Na-update", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "Na-update ang password", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "Sigu<PERSON> ka bang gusto mong gamitin ang opsyon na \"Never\" Ang pagtatakda ng iyong mga pagpipilian sa lock sa \"Hindi kailanman\" ay nag iimbak ng key ng pag encrypt ng iyong vault sa iyong aparato. Kung gagamitin mo ang pagpipiliang ito dapat mong tiyakin na pinapanatili mong protektado nang maayos ang iyong aparato."}, "noOrganizationsList": {"message": "Hindi ka kabilang sa anumang mga organisasyon. Pinapayagan ka ng mga organisasyon na ligtas na magbahagi ng mga item sa iba pang mga gumagamit."}, "noCollectionsInList": {"message": "Walang mga koleksyon na maipapakita."}, "ownership": {"message": "<PERSON><PERSON><PERSON>a<PERSON>"}, "whoOwnsThisItem": {"message": "Sino ang may-ari ng item na ito?"}, "strong": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "<PERSON><PERSON><PERSON> master password"}, "weakMasterPasswordDesc": {"message": "Ang master password na pinili mo ay mahina. <PERSON><PERSON><PERSON> gamitin mo ang isang ma<PERSON>hang master password (o passphrase) para maayos na protektahan ang iyong account sa Bitwarden. <PERSON><PERSON><PERSON> ka bang nais mong gamitin ang master password na ito?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Unlock sa pamamagitan ng PIN"}, "setYourPinTitle": {"message": "Set PIN"}, "setYourPinButton": {"message": "Set PIN"}, "setYourPinCode": {"message": "Itakda ang iyong PIN code para sa pag-unlock ng Bitwarden. Ang iyong mga setting ng PIN ay ma-reset kung kailanman ay lubusang lumabas ka mula sa application."}, "setYourPinCode1": {"message": "Your PIN will be used to unlock Bitwarden instead of your master password. Your PIN will reset if you ever fully log out of Bitwarden."}, "pinRequired": {"message": "Kinakailangan ang PIN code."}, "invalidPin": {"message": "Hindi wastong PIN code."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Too many invalid PIN entry attempts. Logging out."}, "unlockWithBiometrics": {"message": "I-unlock sa pamamagitan ng biometrics"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "awaitDesktop": {"message": "Naghihintay ng kumpirmasyon mula sa desktop"}, "awaitDesktopDesc": {"message": "Mangyaring kump<PERSON>mahin gamit ang biometrics sa Bitwarden desktop application upang makapagtakda ng biometrics para sa browser."}, "lockWithMasterPassOnRestart": {"message": "I-lock sa master password sa restart ng browser"}, "lockWithMasterPassOnRestart1": {"message": "Require master password on browser restart"}, "selectOneCollection": {"message": "Kailangan mong piliin ang hindi bababa sa isang koleksyon."}, "cloneItem": {"message": "Ko<PERSON><PERSON>n ang item"}, "clone": {"message": "Ko<PERSON><PERSON>n ang item"}, "passwordGenerator": {"message": "Password generator"}, "usernameGenerator": {"message": "Username generator"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "Use this password"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "Aksyon sa Vault timeout"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "newCustomizationOptionsCalloutTitle": {"message": "New customization options"}, "newCustomizationOptionsCalloutContent": {"message": "Customize your vault experience with quick copy actions, compact mode, and more!"}, "newCustomizationOptionsCalloutLink": {"message": "View all Appearance settings"}, "lock": {"message": "I-lock", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "Maghanap ng basurahan"}, "permanentlyDeleteItem": {"message": "Permanenteng tanggalin ang item"}, "permanentlyDeleteItemConfirmation": {"message": "Si<PERSON><PERSON> ka bang gusto mong tuluyang tanggalin ang item na ito?"}, "permanentlyDeletedItem": {"message": "<PERSON><PERSON>"}, "restoreItem": {"message": "Ibalik ang item"}, "restoredItem": {"message": "Item na nai-restore"}, "alreadyHaveAccount": {"message": "Already have an account?"}, "vaultTimeoutLogOutConfirmation": {"message": "Sigu<PERSON> ka bang gusto mong gamitin ang setting na ito? Pagsasara ay magtatanggal ng lahat ng access sa iyong vault at nangangailangan ng online authentication pagkatapos ng timeout period?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Pagkumpirma ng pagkilos ng timeout"}, "autoFillAndSave": {"message": "Auto-fill at i-save"}, "fillAndSave": {"message": "Fill and save"}, "autoFillSuccessAndSavedUri": {"message": "Item na auto-filled at URI na nai-save"}, "autoFillSuccess": {"message": "Item na auto-filled "}, "insecurePageWarning": {"message": "Warning: This is an unsecured HTTP page, and any information you submit can potentially be seen and changed by others. This Login was originally saved on a secure (HTTPS) page."}, "insecurePageWarningFillPrompt": {"message": "Do you still wish to fill this login?"}, "autofillIframeWarning": {"message": "The form is hosted by a different domain than the URI of your saved login. <PERSON>ose OK to autofill anyway, or Cancel to stop."}, "autofillIframeWarningTip": {"message": "To prevent this warning in the future, save this URI, $HOSTNAME$, to your Bitwarden login item for this site.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "<PERSON>akda ang master password"}, "currentMasterPass": {"message": "Current master password"}, "newMasterPass": {"message": "Bagong master password"}, "confirmNewMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ang bagong master password"}, "masterPasswordPolicyInEffect": {"message": "Isang o higit pang mga patakaran ng organisasyon ay nangangailangan ng iyong master password upang matugunan ang sumusunod na kinakailangan:"}, "policyInEffectMinComplexity": {"message": "Minimum complexity score ng $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Minimum na haba ng $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Naglalaman ng isa o higit pang mga uppercase character"}, "policyInEffectLowercase": {"message": "Naglalaman ng isa o higit pang mga lowercase character"}, "policyInEffectNumbers": {"message": "Naglalaman ng isa o higit pang mga numero"}, "policyInEffectSpecial": {"message": "Naglalaman ng isa o higit pang mga sumusunod na espesyal na character $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Hindi matugunan ng iyong bagong pangunahing password ang mga kinakailangan ng patakaran."}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Unsubscribe"}, "atAnyTime": {"message": "at any time."}, "byContinuingYouAgreeToThe": {"message": "By continuing, you agree to the"}, "and": {"message": "and"}, "acceptPolicies": {"message": "Sa pamamagitan ng pag-tsek sa kahon na ito ay sumasang-ayon ka sa sumusunod:"}, "acceptPoliciesRequired": {"message": "Ang mga Tuntunin ng Serbisyo at Patakaran sa Privacy ay hindi naiulat."}, "termsOfService": {"message": "Mga Tuntunin ng Serbisyo"}, "privacyPolicy": {"message": "Patakaran sa Privacy"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "Hindi pwedeng maging pareho ang password hint at password mo."}, "ok": {"message": "Ok"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "Pag verify ng pag sync ng desktop"}, "desktopIntegrationVerificationText": {"message": "Mangyaring i verify na ipinapakita ng desktop application ang fingerprint na ito: "}, "desktopIntegrationDisabledTitle": {"message": "Hindi naka set up ang pagsasama ng browser"}, "desktopIntegrationDisabledDesc": {"message": "Ang browser integration ay hindi naka-set up sa Bitwarden desktop application. Paki-set up ito sa settings sa loob ng desktop application."}, "startDesktopTitle": {"message": "Simulan ang Bitwarden desktop application"}, "startDesktopDesc": {"message": "Kinakailangan na simulan ang Bitwarden desktop application bago magamit ang unlock with biometrics."}, "errorEnableBiometricTitle": {"message": "Hindi makapag-set up ng biometrics"}, "errorEnableBiometricDesc": {"message": "Ang aksyon ay kanselahin ng desktop application"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Hindi pinagtibay ng desktop application ang secure communication channel. Mangyaring subukan muli ang operasyon na ito"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Nakaputol ang desktop communication"}, "nativeMessagingWrongUserDesc": {"message": "Ang desktop application ay naka-log in sa iba pang account. Mangyaring tiyakin na naka-log in ang parehong application sa parehong account."}, "nativeMessagingWrongUserTitle": {"message": "Mismatch sa Account"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "Hindi naka-setup ang biometrics"}, "biometricsNotEnabledDesc": {"message": "Ang browser biometrics ay nangangailangan na i-setup ang desktop biometrics sa mga setting muna."}, "biometricsNotSupportedTitle": {"message": "Hindi sinusuportahan ang biometrics"}, "biometricsNotSupportedDesc": {"message": "Ang browser biometrics ay hindi sinusuportahan sa device na ito."}, "biometricsNotUnlockedTitle": {"message": "User locked or logged out"}, "biometricsNotUnlockedDesc": {"message": "Please unlock this user in the desktop application and try again."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "Biometrics failed"}, "biometricsFailedDesc": {"message": "Biometrics cannot be completed, consider using a master password or logging out. If this persists, please contact Bitwarden support."}, "nativeMessaginPermissionErrorTitle": {"message": "Permiso ay hindi i<PERSON>y"}, "nativeMessaginPermissionErrorDesc": {"message": "Walang pahintulot upang makipag-ugnayan sa Bitwarden Desktop Application kaya hindi maaaring magbigay ng biometrics sa browser extension. Mangyaring subukan muli."}, "nativeMessaginPermissionSidebarTitle": {"message": "Maling paghiling ng pahintulot"}, "nativeMessaginPermissionSidebarDesc": {"message": "Hindi maaring gawin ang aksyon na ito sa sidebar, mangyaring subukan muli sa popup o popout."}, "personalOwnershipSubmitError": {"message": "Dahil sa Enterprise Policy, ikaw ay hindi pinapayagan na mag-save ng mga item sa iyong personal vault. Baguhin ang Ownership option sa isang organisasyon at pumili mula sa mga available na collections."}, "personalOwnershipPolicyInEffect": {"message": "Isang organisasyon policy ang nakakaapekto sa iyong mga pagpipilian sa ownership."}, "personalOwnershipPolicyInEffectImports": {"message": "Hinarang ng isang patakaran ng organisasyon ang pag-import ng mga item sa iyong vault."}, "domainsTitle": {"message": "Domains", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "Inilayo na Domain"}, "excludedDomainsDesc": {"message": "Hindi tatanungin ng Bitwarden na i-save ang mga detalye ng pag-login para sa mga domain na ito. Kailangan mo nang i-refresh ang page para maipatupad ang mga pagbabago."}, "excludedDomainsDescAlt": {"message": "Bitwarden will not ask to save login details for these domains for all logged in accounts. You must refresh the page for changes to take effect."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ ay hindi isang valid domain", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Ipadala", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "Teksto"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "Mag-file"}, "allSends": {"message": "Lahat ng Mga Padala", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "Paso na"}, "passwordProtected": {"message": "Protektado ng Password"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "Kopyahin ang Link ng Padala", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "<PERSON><PERSON> ang Password"}, "delete": {"message": "<PERSON><PERSON>"}, "removedPassword": {"message": "Password binura"}, "deletedSend": {"message": "Ipad<PERSON> ang tinanggal", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Ipadala ang link", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "Ipadala nai-delete"}, "removePasswordConfirmation": {"message": "<PERSON><PERSON><PERSON> ka bang gusto mo na tanggalin ang password?"}, "deleteSend": {"message": "I-delete ang <PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "<PERSON><PERSON><PERSON> ka bang gusto mo na i-delete ang Ipadala na ito?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "I-edit ang <PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "Petsa ng Pagtanggal"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "Petsa ng pag-expire"}, "oneDay": {"message": "1 araw"}, "days": {"message": "$DAYS$ araw", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "Pasadyang"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "Bagong Ipadala", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "Bagong password"}, "sendDisabled": {"message": "Ipadala inalis", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Dahil sa isang enterprise policy, ikaw ay magagawang lamang na tanggalin ang umiiral na Ipadala.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "<PERSON><PERSON><PERSON> na nilikha", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "<PERSON>pad<PERSON> na nai-save", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "Upang pumili ng isang file, buksan ang extension sa sidebar (kung posible) o bumalik sa isang bagong window sa pamamagitan ng pag-click sa banner na ito."}, "sendFirefoxFileWarning": {"message": "Upang pumili ng isang file gamit ang Firefox, buksan ang extension sa sidebar o bumalik sa isang bagong window sa pamamagitan ng pag-click sa banner na ito."}, "sendSafariFileWarning": {"message": "Upang pumili ng isang file gamit ang <PERSON>, bumalik sa isang bagong window sa pamamagitan ng pag-click sa banner na ito."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "Bago ka magsimula"}, "expirationDateIsInvalid": {"message": "Ang ibinigay na petsa ng pagpaso ay hindi wasto."}, "deletionDateIsInvalid": {"message": "Ang petsa ng pagbura ibinigay ay hindi wasto."}, "expirationDateAndTimeRequired": {"message": "Kailangan ng petsa at oras ng pagpaso."}, "deletionDateAndTimeRequired": {"message": "Kailangan ng petsa at oras ng pagbura."}, "dateParsingError": {"message": "Nagkaroon ng error sa pag-save ng iyong mga petsa ng pagbura at pagpaso."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "Muling pag<PERSON><PERSON> sa master password"}, "passwordConfirmation": {"message": "Kumpirmasyon ng master password"}, "passwordConfirmationDesc": {"message": "Ang aksyon na ito ay naka-protekta. <PERSON><PERSON>, pakisagutan muli ang iyong master password upang tiyakin ang iyong pagkaka<PERSON>lan."}, "emailVerificationRequired": {"message": "Kailangan ang pag verify ng email"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerificationRequiredDesc": {"message": "Kailangan mong i-verify ang iyong email upang gamitin ang tampok na ito. Maaari mong i-verify ang iyong email sa web vault."}, "updatedMasterPassword": {"message": "I-update ang master password"}, "updateMasterPassword": {"message": "Update master password - I-update ang master password"}, "updateMasterPasswordWarning": {"message": "Ang iyong master password ay binago kamakailan ng isang administrator sa iyong samahan. Para ma-access ang vault, kailangan mo itong i-update ngayon. Ang proceeding ay mag-log out sa iyong kasalukuyang session, na nangangailangan na mag-log in muli. Ang mga aktibong sesyon sa iba pang mga aparato ay maaaring patuloy na manatiling aktibo hanggang sa isang oras."}, "updateWeakMasterPasswordWarning": {"message": "Your master password does not meet one or more of your organization policies. In order to access the vault, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "Awtomatikong pagpapatala"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Ang organisasyon na ito ay may enterprise policy na automatikong mag-eenroll sa iyo sa password reset. Ang enrollment ay magbibigay ng mga administrator ng organisasyon upang mabago ang iyong master password."}, "selectFolder": {"message": "P<PERSON>li ng folder..."}, "noFoldersFound": {"message": "No folders found", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Verification required", "description": "Default title for the user verification dialog."}, "hours": {"message": "Or<PERSON>"}, "minutes": {"message": "Min<PERSON>"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "Nakakaapekto ang mga patakaran ng iyong organisasyon sa iyong vault timeout. Ang pinakamataas na pinapayagang Vault Timeout ay $HOURS$ oras at $MINUTES$ minuto", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Your organization policies are affecting your vault timeout. Maximum allowed vault timeout is $HOURS$ hour(s) and $MINUTES$ minute(s). Your vault timeout action is set to $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Your organization policies have set your vault timeout action to $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Ang iyong vault timeout ay lumalampas sa mga restriksiyon na itinakda ng iyong organisasyon."}, "vaultExportDisabled": {"message": "Hindi magagamit ang pag-export ng Vault"}, "personalVaultExportPolicyInEffect": {"message": "Ang isa o higit pang mga patakaran ng organisasyon ay nagpipigil sa iyo mula sa pag-export ng iyong indibidwal na vault."}, "copyCustomFieldNameInvalidElement": {"message": "Hindi makapag-identify ng wastong elemento ng form. Subukan na mag-inspect ng HTML."}, "copyCustomFieldNameNotUnique": {"message": "Walang natagpuang natatanging nag-identipikar."}, "convertOrganizationEncryptionDesc": {"message": "$ORGANIZATION$ ay gumagamit ng SSO na may sariling-hosted na key server. Walang kinakailangang master password para mag-log in para sa mga miyembro ng organisasyon na ito.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "leaveOrganization": {"message": "Umalis sa organisasyon"}, "removeMasterPassword": {"message": "<PERSON><PERSON><PERSON> ang master password"}, "removedMasterPassword": {"message": "<PERSON><PERSON><PERSON> ang master password"}, "leaveOrganizationConfirmation": {"message": "<PERSON><PERSON><PERSON> ka bang gusto mong umalis sa organisasyon na ito?"}, "leftOrganization": {"message": "Umalis ka na sa organisasyon."}, "toggleCharacterCount": {"message": "I-toggle ang bilang ng character"}, "sessionTimeout": {"message": "Nawalan na ng bisa ang iyong se<PERSON>on. <PERSON><PERSON><PERSON> bumalik at subukan na mag-log in muli."}, "exportingPersonalVaultTitle": {"message": "Nag-export ng indibidwal na vault"}, "exportingIndividualVaultDescription": {"message": "Only the individual vault items associated with $EMAIL$ will be exported. Organization vault items will not be included. Only vault item information will be exported and will not include associated attachments.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Mali"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Lumikha ng username"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Plus na naka-address na email", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Gamitin ang mga kakayahan ng sub address ng iyong email provider."}, "catchallEmail": {"message": "Catch-all na email"}, "catchallEmailDesc": {"message": "Gamitin ang naka configure na inbox ng catch all ng iyong domain."}, "random": {"message": "Random"}, "randomWord": {"message": "Random na salita"}, "websiteName": {"message": "Pangalan ng website"}, "service": {"message": "<PERSON><PERSON><PERSON>"}, "forwardedEmail": {"message": "<PERSON><PERSON><PERSON> ang alias ng email"}, "forwardedEmailDesc": {"message": "<PERSON><PERSON><PERSON> ng isang email alias na may isang panlabas na serbisyo sa pagpapasa."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generated by <PERSON><PERSON><PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Invalid $SERVICENAME$ domain.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Invalid $SERVICENAME$ url.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Unknown $SERVICENAME$ error occurred.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Pangalan ng Hostname", "description": "Part of a URL."}, "apiAccessToken": {"message": "Token ng Access sa API"}, "apiKey": {"message": "Susi ng API"}, "ssoKeyConnectorError": {"message": "Mali sa koneksiyon ng key: <PERSON><PERSON><PERSON><PERSON><PERSON> na magagamit at gumagana nang maayos ang key connector."}, "premiumSubcriptionRequired": {"message": "Kinakailangan ng subscription ng Premium"}, "organizationIsDisabled": {"message": "Organisasyon ay suspindido."}, "disabledOrganizationFilterError": {"message": "Mga item sa mga naka-suspindong Organisasyon ay hindi ma-access. Mangyaring makipag-ugnayan sa may-ari ng iyong Organisasyon para sa tulong."}, "loggingInTo": {"message": "Nag-lolog in sa $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Bersyon ng server"}, "selfHostedServer": {"message": "self-hosted"}, "thirdParty": {"message": "Ika-tatlong-partido"}, "thirdPartyServerMessage": {"message": "Naka-konekta sa ika-tatlong-partido server implementation, $SERVERNAME$. Paki-verify ang mga bug gamit ang opisyal na server, o iulat sila sa ika-tatlong-partido server.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "huling nakita sa: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Mag-login gamit ang pangunahing password"}, "newAroundHere": {"message": "Mag-login gamit ang pangunahing password?"}, "rememberEmail": {"message": "<PERSON><PERSON><PERSON> ang email"}, "loginWithDevice": {"message": "Mag log in gamit ang device"}, "fingerprintPhraseHeader": {"message": "Hulmabig ng Hilik ng Dako"}, "fingerprintMatchInfo": {"message": "Mangy<PERSON> tiyakin na ang iyong vault ay naka unlock at ang parirala ng Fingerprint ay tumutugma sa kabilang aparato."}, "resendNotification": {"message": "<PERSON>ling ipadala ang abiso"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "Naipadala na ang notification sa iyong device."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "<PERSON><PERSON> initiated"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "Na<PERSON><PERSON><PERSON> na Master Password"}, "exposedMasterPasswordDesc": {"message": "Password na nakita sa data breach. Gamitin ang natatanging password upang makaproteksyon sa iyong account. <PERSON><PERSON><PERSON> ka ba na gusto mong gamitin ang naipakitang password?"}, "weakAndExposedMasterPassword": {"message": "<PERSON><PERSON><PERSON> at Naipakitang Pangunahing Password"}, "weakAndBreachedMasterPasswordDesc": {"message": "Ma<PERSON>ang password na nakilala at nakita sa data breach. <PERSON><PERSON><PERSON> ang malakas at natatanging password upang makaproteksyon sa iyong account. Si<PERSON><PERSON> ka ba na gusto mong gamitin ang password na ito?"}, "checkForBreaches": {"message": "Tingnan ang kilalang breaches ng data para sa password na ito"}, "important": {"message": "Mahalaga:"}, "masterPasswordHint": {"message": "Hindi maibalik ang iyong master password kung maalala mo ito!"}, "characterMinimum": {"message": "$LENGTH$ character kailangan ang minimum", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Ang mga patakaran ng iyong organisasyon ay nagbigay ng pag-automatikong pag-load sa pahina."}, "howToAutofill": {"message": "Paano mag-auto-fill"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "<PERSON><PERSON><PERSON> ko"}, "autofillSettings": {"message": "Mga setting ng auto-fill"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Manage shortcuts"}, "autofillShortcut": {"message": "Keyboard shortcut para sa auto-fill"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Default shortcut ng auto-fill: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Opens in a new window"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Device approval required. Select an approval option below:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "Remember this device"}, "uncheckIfPublicDevice": {"message": "Uncheck if using a public device"}, "approveFromYourOtherDevice": {"message": "Approve from your other device"}, "requestAdminApproval": {"message": "Request admin approval"}, "ssoIdentifierRequired": {"message": "Organization SSO identifier is required."}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Follow the link in the email sent to"}, "andContinueCreatingYourAccount": {"message": "and continue creating your account."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "Access denied. You do not have permission to view this page."}, "general": {"message": "General"}, "display": {"message": "Display"}, "accountSuccessfullyCreated": {"message": "Account successfully created!"}, "adminApprovalRequested": {"message": "Admin approval requested"}, "adminApprovalRequestSentToAdmins": {"message": "Your request has been sent to your admin."}, "troubleLoggingIn": {"message": "Trouble logging in?"}, "loginApproved": {"message": "Login approved"}, "userEmailMissing": {"message": "User email missing"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "<PERSON><PERSON> trusted"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsNoItemsTitle": {"message": "No active Sends", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsNoItemsMessage": {"message": "Use Send to securely share encrypted information with anyone.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Input is required."}, "required": {"message": "required"}, "search": {"message": "Search"}, "inputMinLength": {"message": "Input must be at least $COUNT$ characters long.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Input must not exceed $COUNT$ characters in length.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "The following characters are not allowed: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Input value must be at least $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Input value must not exceed $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 or more emails are invalid"}, "inputTrimValidator": {"message": "Input must not contain only whitespace.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Input is not an email address."}, "fieldsNeedAttention": {"message": "$COUNT$ field(s) above need your attention.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Select --"}, "multiSelectPlaceholder": {"message": "-- Type to filter --"}, "multiSelectLoading": {"message": "Retrieving options..."}, "multiSelectNotFound": {"message": "No items found"}, "multiSelectClearAll": {"message": "Clear all"}, "plusNMore": {"message": "+ $QUANTITY$ more", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Submenu"}, "toggleCollapse": {"message": "Toggle collapse", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "Alias domain"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Items with master password re-prompt cannot be autofilled on page load. Autofill on page load turned off.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Autofill on page load set to use default setting.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Turn off master password re-prompt to edit this field", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Skip to content"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden autofill menu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Unlock your account to view matching logins", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Unlock account", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Fill credentials for", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Partial username", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "No items to show", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "New item", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Add new vault item", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "New identity", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Turn on"}, "ignore": {"message": "Ignore"}, "importData": {"message": "Import data", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Import error"}, "importErrorDesc": {"message": "There was a problem with the data you tried to import. Please resolve the errors listed below in your source file and try again."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Resolve the errors below and try again."}, "description": {"message": "Description"}, "importSuccess": {"message": "Data successfully imported"}, "importSuccessNumberOfItems": {"message": "A total of $AMOUNT$ items were imported.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Try again"}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "Set PIN"}, "verifyWithBiometrics": {"message": "Verify with biometrics"}, "awaitingConfirmation": {"message": "Awaiting confirmation"}, "couldNotCompleteBiometrics": {"message": "Could not complete biometrics."}, "needADifferentMethod": {"message": "Need a different method?"}, "useMasterPassword": {"message": "Use master password"}, "usePin": {"message": "Use PIN"}, "useBiometrics": {"message": "Use biometrics"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Resend code"}, "total": {"message": "Total"}, "importWarning": {"message": "You are importing data to $ORGANIZATION$. Your data may be shared with members of this organization. Do you want to proceed?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Duo two-step login is required for your account."}, "popoutExtension": {"message": "Popout extension"}, "launchDuo": {"message": "Launch Duo"}, "importFormatError": {"message": "Data is not formatted correctly. Please check your import file and try again."}, "importNothingError": {"message": "Nothing was imported."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Learn about your import options"}, "selectImportFolder": {"message": "Select a folder"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "confirmFilePassword": {"message": "Confirm file password"}, "exportSuccess": {"message": "Vault data exported"}, "typePasskey": {"message": "Passkey"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "Passkey will not be copied"}, "passkeyNotCopiedAlert": {"message": "The passkey will not be copied to the cloned item. Do you want to continue cloning this item?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verification required by the initiating site. This feature is not yet implemented for accounts without master password."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "A passkey already exists for this application."}, "noPasskeysFoundForThisApplication": {"message": "No passkeys found for this application."}, "noMatchingPasskeyLogin": {"message": "You do not have a matching login for this site."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "Confirm"}, "savePasskey": {"message": "Save passkey"}, "savePasskeyNewLogin": {"message": "Save passkey as new login"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "<PERSON><PERSON> Item"}, "overwritePasskey": {"message": "Overwrite passkey?"}, "overwritePasskeyAlert": {"message": "This item already contains a passkey. Are you sure you want to overwrite the current passkey?"}, "featureNotSupported": {"message": "Feature not yet supported"}, "yourPasskeyIsLocked": {"message": "Authentication required to use passkey. Verify your identity to continue."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "Incorrect username or password"}, "incorrectPassword": {"message": "Incorrect password"}, "incorrectCode": {"message": "Incorrect code"}, "incorrectPin": {"message": "Incorrect PIN"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "LastPass Email"}, "importingYourAccount": {"message": "Importing your account..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "LastPass master password"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Import directly from LastPass"}, "importFromCSV": {"message": "Import from CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Collection"}, "lastPassYubikeyDesc": {"message": "Insert the YubiKey associated with your LastPass account into your computer's USB port, then touch its button."}, "switchAccount": {"message": "Switch account"}, "switchAccounts": {"message": "Switch accounts"}, "switchToAccount": {"message": "Switch to account"}, "activeAccount": {"message": "Active account"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Available accounts"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "server"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Just once"}, "alwaysForThisSite": {"message": "Always for this site"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ added to excluded domains.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Make <PERSON><PERSON><PERSON> your default password manager?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ignoring this option may cause conflicts between Bitwarden autofill suggestions and your browser's.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Make Bitwarden your default password manager", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Unable to set <PERSON><PERSON><PERSON> as the default password manager", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "You must grant browser privacy permissions to Bitwarden to set it as the default password manager.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Make default", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Credentials saved successfully!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Credentials updated successfully!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Error saving credentials. Check console for details.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Success"}, "removePasskey": {"message": "Remove passkey"}, "passkeyRemoved": {"message": "Passkey removed"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "accountSecurity": {"message": "Account security"}, "notifications": {"message": "Notifications"}, "appearance": {"message": "Appearance"}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "New"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "Owner"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Additional information"}, "itemHistory": {"message": "Item history"}, "lastEdited": {"message": "Last edited"}, "ownerYou": {"message": "Owner: You"}, "linked": {"message": "Linked"}, "copySuccessful": {"message": "Copy Successful"}, "upload": {"message": "Upload"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "deleteAttachmentName": {"message": "Delete attachment $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Download $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "Filters"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "downloadAttachment": {"message": "Download - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Login credentials"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "Add account"}, "loading": {"message": "Loading"}, "data": {"message": "Data"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Add field"}, "add": {"message": "Add"}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Edit field"}, "editFieldLabel": {"message": "Edit $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Delete $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ added", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Item Location"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "Compact mode"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBody": {"message": "Autofill items for the current page\nFavorite items for easy access\nSearch your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}}