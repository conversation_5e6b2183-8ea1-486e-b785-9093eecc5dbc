{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logó"}, "extName": {"message": "Bitwarden Password Manager", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "At home, at work, or on the go, <PERSON><PERSON><PERSON> easily secures all your passwords, passkeys, and sensitive information", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "Bejelentkezés vagy új fiók létrehozása a biztonsági széf eléréséhez."}, "inviteAccepted": {"message": "A meghívás elfogadásra került."}, "createAccount": {"message": "Fiók létrehozása"}, "newToBitwarden": {"message": "Új vagyunk a Bitwardenben?"}, "logInWithPasskey": {"message": "Bejelentkezés hozzáférési kulccsal"}, "useSingleSignOn": {"message": "Egyszeri bejelentkezés használata"}, "welcomeBack": {"message": "Üdvözlet újra"}, "setAStrongPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "finishCreatingYourAccountBySettingAPassword": {"message": "A fiók létrehozásának befejezése jelszó beállít<PERSON>val"}, "enterpriseSingleSignOn": {"message": "Vállalati ö<PERSON>"}, "cancel": {"message": "M<PERSON>gs<PERSON>"}, "close": {"message": "Bezárás"}, "submit": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "emailAddress": {"message": "<PERSON><PERSON>"}, "masterPass": {"message": "Mesterjelszó"}, "masterPassDesc": {"message": "A mesterjelszó a jelszó a széf eléréséhez. Nagyon fontos a mesterjelszó ismerete. Nincs mód a jels<PERSON><PERSON> v<PERSON>zaállítására."}, "masterPassHintDesc": {"message": "A mesterjelszó emlékeztető segíthet emlékezni a jelszóra elfelejtés esetén."}, "masterPassHintText": {"message": "Ha elfelejtettük a j<PERSON><PERSON><PERSON><PERSON>, a j<PERSON><PERSON>óra vonatko<PERSON>ó tippet elküldhetjük a saját email címre. $CURRENT$/$MAXIMUM$ maximum karakter.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "A mesterjelszó ismételt begépelése"}, "masterPassHint": {"message": "Mesterjelszó emlékeztető (nem kötelező)"}, "passwordStrengthScore": {"message": "A jelszó erősségi pontszáma $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Csatlakozás szervezethez"}, "joinOrganizationName": {"message": "Csatlakozás: $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Fejezzük be a szervezethez csatlakozást egy mesterjelszó beállításával."}, "tab": {"message": "<PERSON><PERSON><PERSON>"}, "vault": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "allVaults": {"message": "Összes széf"}, "tools": {"message": "Eszközök"}, "settings": {"message": "Beállítások"}, "currentTab": {"message": "<PERSON><PERSON><PERSON><PERSON> fül"}, "copyPassword": {"message": "Jelszó másolása"}, "copyPassphrase": {"message": "Jelmondat másol<PERSON>"}, "copyNote": {"message": "Je<PERSON><PERSON>t m<PERSON>"}, "copyUri": {"message": "URI másolása"}, "copyUsername": {"message": "Felhasználónév másolása"}, "copyNumber": {"message": "Szá<PERSON> m<PERSON>"}, "copySecurityCode": {"message": "Biztonsági kód m<PERSON>"}, "copyName": {"message": "Név másolása"}, "copyCompany": {"message": "<PERSON><PERSON>g <PERSON>"}, "copySSN": {"message": "Társadalombiztosítási szám másolása"}, "copyPassportNumber": {"message": "Útlevélszám másolása"}, "copyLicenseNumber": {"message": "Licensz szám másolása"}, "copyPrivateKey": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> másolása"}, "copyPublicKey": {"message": "Nyilvános kulcs másolása"}, "copyFingerprint": {"message": "Ujjlen<PERSON><PERSON>"}, "copyCustomField": {"message": "$FIELD$ másolása", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "<PERSON><PERSON><PERSON>"}, "copyNotes": {"message": "Je<PERSON><PERSON>t m<PERSON>"}, "copy": {"message": "Másolás", "description": "Copy to clipboard"}, "fill": {"message": "Kitöltés", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Automatikus kitöltés"}, "autoFillLogin": {"message": "Automatikus kitöltés bejelentkezés"}, "autoFillCard": {"message": "Automatikus kitöltés kártya"}, "autoFillIdentity": {"message": "Automatikus kitöltés személyazonosság"}, "fillVerificationCode": {"message": "<PERSON><PERSON><PERSON><PERSON> kód kitöltése"}, "fillVerificationCodeAria": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON><PERSON><PERSON><PERSON> (másolt)"}, "copyElementIdentifier": {"message": "Egyedi mezőnév másolása"}, "noMatchingLogins": {"message": "Nincsenek egyező bejelentkezések."}, "noCards": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "noIdentities": {"message": "Nincsenek személyazonosságok"}, "addLoginMenu": {"message": "Bejelentkezés hozzáadása"}, "addCardMenu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "addIdentityMenu": {"message": "Személyazonossság hozzáadása"}, "unlockVaultMenu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "loginToVaultMenu": {"message": "Bejelentkezés a saját széfbe"}, "autoFillInfo": {"message": "Nincsenek elérhető bejelentkezések az automatikus kitöltéshez az aktuális böngészőfülnél."}, "addLogin": {"message": "Bejelentkezés hozzáadása"}, "addItem": {"message": "<PERSON><PERSON>"}, "accountEmail": {"message": "Fiók email cím"}, "requestHint": {"message": "<PERSON><PERSON><PERSON>"}, "requestPasswordHint": {"message": "Jelszó emlékeztető kérése"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Adjuk meg fiók email címét és elküldésre kerül a jelszóra vonatkozó tipp."}, "getMasterPasswordHint": {"message": "<PERSON><PERSON><PERSON><PERSON> emlékeztetőt"}, "continue": {"message": "Folytatás"}, "sendVerificationCode": {"message": "<PERSON><PERSON><PERSON><PERSON> kód elküldése a saját email címre"}, "sendCode": {"message": "<PERSON><PERSON><PERSON>"}, "codeSent": {"message": "A kód elküldésre került."}, "verificationCode": {"message": "<PERSON><PERSON><PERSON><PERSON> kód"}, "confirmIdentity": {"message": "A folytatáshoz meg kell erősíteni a személyazonosságot."}, "changeMasterPassword": {"message": "Mesterjelszó módosítása"}, "continueToWebApp": {"message": "Tovább a webes alkalmazáshoz?"}, "continueToWebAppDesc": {"message": "Fedezzük fel a Bitwarden-fiók további funkcióit a webalkalmazásban."}, "continueToHelpCenter": {"message": "<PERSON><PERSON><PERSON><PERSON> a Segítség Központhoz?"}, "continueToHelpCenterDesc": {"message": "További információ a Bitwarden használatáról a Segítség Központban."}, "continueToBrowserExtensionStore": {"message": "Továbblépés a böngésző bővítmények áruházba?"}, "continueToBrowserExtensionStoreDesc": {"message": "Segítsünk másoknak megtudni, hogy a Bitwarden megfelelő-e számukra. Látogassunk el a böngésző bővítmény áruházba és értékeljük most."}, "changeMasterPasswordOnWebConfirmation": {"message": "A mesterjelszó a Bitwarden webalkalmazásban módosítható."}, "fingerprintPhrase": {"message": "Ujjlenyomat <PERSON>", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Fiók ujjlenyomat k<PERSON>", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Kétlépcsős be<PERSON>z<PERSON>"}, "logOut": {"message": "Kijelentkezés"}, "aboutBitwarden": {"message": "Bitwarden névjegy"}, "about": {"message": "Névjegy"}, "moreFromBitwarden": {"message": "További infó a Bitwardenről"}, "continueToBitwardenDotCom": {"message": "Folytatás a bitwarden.com webhelyen?"}, "bitwardenForBusiness": {"message": "Bitwarden for Business"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "A Bitwarden Authenticator lehetővé teszi hitelesítő kulcsok tárolását és TOTP-kódok generálását a kétlépcsős ellenőrzési folyamatokhoz. Tudjunk meg többet a bitwarden.com webhelyen."}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Biztonságos<PERSON>, kezelhetjük és megoszthatjuk a fejlesztői titkos adatokat a Bitwarden Secrets Manager segítségével. Tudjunk meg többet a bitwarden.com webhelyen."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Hozzunk létre zökkenőmentes és biztonságos bejelentkezési élményt hagyományos jelszavak nélkül a Passwordless.dev segítségével. Tudjunk meg többet a bitwarden.com webhelyen."}, "freeBitwardenFamilies": {"message": "Ingyenes Bitwarden Families csomag"}, "freeBitwardenFamiliesPageDesc": {"message": "Jogosult vagyunk az ingyenes Bitwarden Families csomagra. Váltsuk be ezt az ajánlatot még ma az internetes alkalmazásban."}, "version": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "save": {"message": "Men<PERSON>s"}, "move": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addFolder": {"message": "Mappa hozzáadása"}, "name": {"message": "Név"}, "editFolder": {"message": "Mappa szerkesztése"}, "editFolderWithName": {"message": "Mappa szerkesztése: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "<PERSON><PERSON> <PERSON><PERSON>"}, "folderName": {"message": "Mappanév"}, "folderHintText": {"message": "Mappa beágyazása a szülőmappa nevének hozzáadásával, majd egy “/” karakterrel. Példa: Közösségi/Fórumok"}, "noFoldersAdded": {"message": "<PERSON>em lett mappa hozz<PERSON>va."}, "createFoldersToOrganize": {"message": "Hozzunk létre mappákat a széfelemek rendszerezéséhez"}, "deleteFolderPermanently": {"message": "Biztosan véglegesen törlésre kerüljön ez a mappa?"}, "deleteFolder": {"message": "Mappa törlése"}, "folders": {"message": "Mappák"}, "noFolders": {"message": "Nincsenek megjeleníthető mappák."}, "helpFeedback": {"message": "Súgó és visszajelzés"}, "helpCenter": {"message": "Bitwardsn Segítségközpont"}, "communityForums": {"message": "Bitwarden közösségi fórum felfedezése"}, "contactSupport": {"message": "Kapcsolatfelvétel a Bitwarden támogatással"}, "sync": {"message": "Szinkronizálás"}, "syncVaultNow": {"message": "<PERSON><PERSON><PERSON><PERSON>iz<PERSON>lása most"}, "lastSync": {"message": "Utolsó szinkronizálás:"}, "passGen": {"message": "<PERSON><PERSON><PERSON><PERSON> gener<PERSON>"}, "generator": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Automatikusan létre<PERSON>, egyedi j<PERSON> a bejelentkezéseidhez."}, "bitWebVaultApp": {"message": "Bitwarden webes alkalmazás"}, "importItems": {"message": "Elemek <PERSON>"}, "select": {"message": "Kiválaszt"}, "generatePassword": {"message": "Jelszó generálása"}, "generatePassphrase": {"message": "Jelm<PERSON>t gene<PERSON>"}, "passwordGenerated": {"message": "A jelszó generálásra került."}, "passphraseGenerated": {"message": "A jelmondat generálásra került."}, "usernameGenerated": {"message": "A felhasználónév generálásra került."}, "emailGenerated": {"message": "Az email generálásra került."}, "regeneratePassword": {"message": "<PERSON><PERSON><PERSON><PERSON>ragenerálása"}, "options": {"message": "Beállítások"}, "length": {"message": "Hossz"}, "include": {"message": "<PERSON><PERSON><PERSON>", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Nagybetűs karakterek bevon<PERSON>", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Kisbetűs karakter<PERSON> be<PERSON>", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Számok be<PERSON>", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Spec<PERSON><PERSON><PERSON>", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "Szavak száma"}, "wordSeparator": {"message": "<PERSON><PERSON><PERSON>"}, "capitalize": {"message": "<PERSON><PERSON>", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "Szám is"}, "minNumbers": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "minSpecial": {"message": "<PERSON><PERSON><PERSON><PERSON> speci<PERSON>"}, "avoidAmbiguous": {"message": "Félreérthető karakterek mellőzése", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "A vállalati házirend követelményei kerülnek alkalmazásra a generátor be<PERSON>ll<PERSON>ásair<PERSON>.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "Keresés a széfben"}, "edit": {"message": "Szerkesztés"}, "view": {"message": "Nézet"}, "noItemsInList": {"message": "Nincsenek megjeleníthető tételek."}, "itemInformation": {"message": "<PERSON>em <PERSON>"}, "username": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "password": {"message": "Je<PERSON><PERSON><PERSON>"}, "totp": {"message": "Hitelesítő titkos kulcs"}, "passphrase": {"message": "Kulcskifejezés"}, "favorite": {"message": "Kedvenc"}, "unfavorite": {"message": "<PERSON><PERSON>"}, "itemAddedToFavorites": {"message": "<PERSON>z elem bekerült a kedvencekhez."}, "itemRemovedFromFavorites": {"message": "Az elem kikerült a kedvencekből."}, "notes": {"message": "Jegyzetek"}, "privateNote": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "note": {"message": "Jegyzet"}, "editItem": {"message": "<PERSON><PERSON>"}, "folder": {"message": "Mappa"}, "deleteItem": {"message": "<PERSON><PERSON>"}, "viewItem": {"message": "<PERSON><PERSON>"}, "launch": {"message": "Indítás"}, "launchWebsite": {"message": "<PERSON><PERSON><PERSON>"}, "launchWebsiteName": {"message": "$ITEMNAME$ webhely elindít<PERSON>a", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Weboldal"}, "toggleVisibility": {"message": "Láthatóság váltása"}, "manage": {"message": "Kezelés"}, "other": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unlockMethods": {"message": "Feloldási opciók"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Állítsunk be egy feloldási módot a széf időkifutási műveletének módosításához."}, "unlockMethodNeeded": {"message": "Feloldási mód beállítása a Beállításokban"}, "sessionTimeoutHeader": {"message": "Munkamenet időkifutás"}, "vaultTimeoutHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "otherOptions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "rateExtension": {"message": "Bővítmény értékelése"}, "browserNotSupportClipboard": {"message": "A webböngésződ nem támogat könnyű vágólap másolást. M<PERSON>old man<PERSON><PERSON> inkább."}, "verifyYourIdentity": {"message": "Személyazonosság ellenőrzése"}, "weDontRecognizeThisDevice": {"message": "Nem is<PERSON> fel ez az eszköz. Írjuk be az email címünkre küldött kódot a személyazonosság igazolásához."}, "continueLoggingIn": {"message": "A bejelentkezés folytatása"}, "yourVaultIsLocked": {"message": "A széf zárolásra került. A folytatáshoz meg kell adni a mesterjelszót."}, "yourVaultIsLockedV2": {"message": "A széf <PERSON> van."}, "yourAccountIsLocked": {"message": "A fiók zárolva van."}, "or": {"message": "vagy"}, "unlock": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "loggedInAsOn": {"message": "Bejelentkezve mint $EMAIL$ $HOSTNAME$ webhelyen.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "<PERSON><PERSON><PERSON>"}, "vaultTimeout": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "vaultTimeout1": {"message": "Időkifutás"}, "lockNow": {"message": "<PERSON><PERSON><PERSON><PERSON> most"}, "lockAll": {"message": "Összes zárolása"}, "immediately": {"message": "Azonnal"}, "tenSeconds": {"message": "10 másodperc"}, "twentySeconds": {"message": "20 másodperc"}, "thirtySeconds": {"message": "30 másodperc"}, "oneMinute": {"message": "1 perc"}, "twoMinutes": {"message": "2 perc"}, "fiveMinutes": {"message": "5 perc"}, "fifteenMinutes": {"message": "15 perc"}, "thirtyMinutes": {"message": "30 perc"}, "oneHour": {"message": "1 óra"}, "fourHours": {"message": "4 óra"}, "onLocked": {"message": "Rendszerzároláskor"}, "onRestart": {"message": "Böngésző újraindításkor"}, "never": {"message": "<PERSON><PERSON>"}, "security": {"message": "Biztonság"}, "confirmMasterPassword": {"message": "Mesterjelszó megerősítése"}, "masterPassword": {"message": "Mesterjelszó"}, "masterPassImportant": {"message": "A mesterjelszó nem állítható he<PERSON>, ha elfelejtik!"}, "masterPassHintLabel": {"message": "Mesterjelszó emlékeztető"}, "errorOccurred": {"message": "<PERSON><PERSON>."}, "emailRequired": {"message": "E-mail cím megadása kötelező."}, "invalidEmail": {"message": "Érvénytelen email cím."}, "masterPasswordRequired": {"message": "A mesterjelszó megadása kötelező."}, "confirmMasterPasswordRequired": {"message": "A mesterjelszó ismételt megadása kötelező."}, "masterPasswordMinlength": {"message": "A mesterjelszónak legalább $VALUE$ karakter hosszúnak kell lennie.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "A megadott két j<PERSON>zó nem egyezik meg."}, "newAccountCreated": {"message": "Felhasználódat létrehoztuk. Most már be t<PERSON>z j<PERSON>nt<PERSON>zni."}, "newAccountCreated2": {"message": "Az új fiók létrrejött."}, "youHaveBeenLoggedIn": {"message": "Megtörtént a bejelentkezés!"}, "youSuccessfullyLoggedIn": {"message": "A bejelentkezés sikeres volt."}, "youMayCloseThisWindow": {"message": "Most már bez<PERSON><PERSON><PERSON><PERSON> ez az ablak."}, "masterPassSent": {"message": "Elküldtünk neked egy mesterjelszó emlékeztetődet tartalmazó E-mailt."}, "verificationCodeRequired": {"message": "Ellen<PERSON><PERSON><PERSON> kód szükséges."}, "webauthnCancelOrTimeout": {"message": "A hitelesítés megszakításra került vagy túl sok<PERSON> tartott. Próbáljuk újra."}, "invalidVerificationCode": {"message": "Érvénytelen ellenőrző kód"}, "valueCopied": {"message": "$VALUE$ másolásra került.", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "<PERSON>em si<PERSON>ült automatikusan kitölteni a kiválasztott elemet ezen az oldalon. <PERSON><PERSON><PERSON> vágólapon keresztül kell bemásolni."}, "totpCaptureError": {"message": "Az aktuális weboldalól nem lehet szkennelni a QR kódot."}, "totpCaptureSuccess": {"message": "A hitelesítő kulcs hozzáadásra került."}, "totpCapture": {"message": "Hitelesítő QR kód szkennelése az aktuális weboldalról"}, "totpHelperTitle": {"message": "Tegyük zökkenőmentessé a kétlépcsős azonosítást."}, "totpHelper": {"message": "A Bitwarden képes tárolni és kitölteni a kétlépcsős ellenőrző kódokat. Másoljuk ki és illesszük be a kulcsot ebbe a mezőbe."}, "totpHelperWithCapture": {"message": "A Bitwarden képes tárolni és kitölteni a kétlépcsős ellenőrző kódokat. Válasszuk a kamera ikont, hogy képernyőképet készítsünk a webhely hitelesítő QR kódjáról vagy másoljuk ki és illesszük be a kulcsot ebbe a mezőbe."}, "learnMoreAboutAuthenticators": {"message": "További információ a hitelesítőkről"}, "copyTOTP": {"message": "Hitelesítő kód másolása (TOTP)"}, "loggedOut": {"message": "Kijelentkezett"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "Bejelentkezési munkamenete le<PERSON>."}, "logIn": {"message": "Log in"}, "logInToBitwarden": {"message": "Bejelentkezés a Bitwardenbe"}, "enterTheCodeSentToYourEmail": {"message": "Adjuk meg az email címre elküldött kódot."}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Adjuk meg a hitelesítő alkalmazása által generált kódot."}, "pressYourYubiKeyToAuthenticate": {"message": "Nyomjuk meg a YubiKey-t a hitelesítéshez."}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo kétlépcsős bejelentkezés szükséges a fiókhoz. Kövessük az alábbi lépéseket a bejelentkezés befejezéséhez."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Kövessük az alábbi lépéseket a bejelentkezés befejezéséhez."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Kövessük az alábbi lépéseket a biztonsági kulccsal bejelentkezés befejezéséhez."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy ki szeretnél j<PERSON>ntkezni?"}, "yes": {"message": "Igen"}, "no": {"message": "Nem"}, "location": {"message": "<PERSON><PERSON>"}, "unexpectedError": {"message": "Várat<PERSON> hiba tö<PERSON>."}, "nameRequired": {"message": "Név megadása kötelező."}, "addedFolder": {"message": "A mappa hozzáadásra került."}, "twoStepLoginConfirmation": {"message": "A kétlépcsős bejelentkezés biztonságosabbá teszi a fiókot azáltal, hogy ellenőrizni kell a bejelentkezést egy másik olyan eszközzel mint például biztonsági kulcs, hitelesítő alkalmazás, SMS, telefon hívás vagy email. A kétlépcsős bejelentkezést a bitwarden.com webes széfben lehet engedélyezni. Felkeressük a webhelyet most?"}, "twoStepLoginConfirmationContent": {"message": "Tegyük biztonságosabbá a fiókot a kétlépcsős bejelentkezés beállításával a Bitwarden webalkalmazásban."}, "twoStepLoginConfirmationTitle": {"message": "Tovább a webes alkalmazáshoz?"}, "editedFolder": {"message": "A mappa mentésre került."}, "deleteFolderConfirmation": {"message": "Biz<PERSON>, hogy törölni akarod ezt a mappát?"}, "deletedFolder": {"message": "A mappa törlésre került."}, "gettingStartedTutorial": {"message": "Kezdeti ismer<PERSON>tő"}, "gettingStartedTutorialVideo": {"message": "Nézd meg az első lépések oktatóprogramunkat, hogy megtanuld, hogyan hozd ki a legtöbbet a böngésző kiterjesztésből."}, "syncingComplete": {"message": "Szinkronizálás befe<PERSON>zve"}, "syncingFailed": {"message": "Sikertelen szinkronizálás"}, "passwordCopied": {"message": "<PERSON><PERSON><PERSON><PERSON> másolva"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "<PERSON><PERSON>"}, "addDomain": {"message": "Tartom<PERSON>y <PERSON>", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "Az elem hozzáadásra került."}, "editedItem": {"message": "<PERSON>z elem szerkesztésre került."}, "deleteItemConfirmation": {"message": "Biztosan törlésre kerüljön ezt az elem?"}, "deletedItem": {"message": "Az elem a lomtárba került."}, "overwritePassword": {"message": "Jelszó felülírása"}, "overwritePasswordConfirmation": {"message": "Biztosan felül akarod írni a jelenlegi j<PERSON>zavad?"}, "overwriteUsername": {"message": "Felhasználónév felülírása"}, "overwriteUsernameConfirmation": {"message": "Biztosan felülírásra kerüljön az aktuális felhasználónév?"}, "searchFolder": {"message": "Mappa kere<PERSON>"}, "searchCollection": {"message": "Gyűjtemény k<PERSON>sése"}, "searchType": {"message": "<PERSON><PERSON><PERSON>"}, "noneFolder": {"message": "<PERSON><PERSON><PERSON> mappa", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "Bejelentkezés hozzáadás kérése"}, "vaultSaveOptionsTitle": {"message": "<PERSON><PERSON><PERSON> s<PERSON>"}, "addLoginNotificationDesc": {"message": "A \"Bejelentkezés értesítés hozzáadása\" automatikusan felajánlja a bejelentkezés széfbe mentését az első bejelentkezéskor."}, "addLoginNotificationDescAlt": {"message": "Egy elem ho<PERSON> k<PERSON>, ha az nem talá<PERSON>ó a széfben. Minden bejelentkezett fiókra vonatkozik."}, "showCardsInVaultViewV2": {"message": "Mindig jelenítse meg a kártyákat automatikus kitöltési javaslatként a Széf nézetben"}, "showCardsCurrentTab": {"message": "Kártyák megjelenítése a Fül oldalon"}, "showCardsCurrentTabDesc": {"message": "Kártyaelemek listázása a Fül oldalon a könnyű automatikus kitöltéshez."}, "showIdentitiesInVaultViewV2": {"message": "Mindig jelenítse meg a személyazonosságokat automatikus kitöltési javaslatként a Széf nézetben"}, "showIdentitiesCurrentTab": {"message": "Azonosítások megjelenítése a Fül oldalon"}, "showIdentitiesCurrentTabDesc": {"message": "Azonosítás elemek listázása a Fül oldalon a könnyű automatikus kitöltéshez."}, "clickToAutofillOnVault": {"message": "Kattintsunk az elemekre az automatikus kitöltéshez a Széf nézetben"}, "clickToAutofill": {"message": "A kitöltéshez kattintsunk az automatikus kitöltési javaslat elemeire."}, "clearClipboard": {"message": "Vágólap <PERSON>", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Automatikusan törli a vágólapra másolt értékeket.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "A Bitwarden megjegyezze ezt a jelszót?"}, "notificationAddSave": {"message": "Men<PERSON>s"}, "notificationViewAria": {"message": "$ITEMNAME$ megtekintése, megnyitás új ablakban", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationEditTooltip": {"message": "Szerkesztés mentés <PERSON>", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "<PERSON><PERSON>"}, "labelWithNotification": {"message": "$LABEL$: <PERSON>j <PERSON>", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "loginSaveConfirmation": {"message": "$ITEMNAME$ mentésre került a Bitwardenben.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is saved."}, "loginUpdatedConfirmation": {"message": "$ITEMNAME$ frissítésre került a Bitwardenben.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is updated."}, "saveAsNewLoginAction": {"message": "Mentés új <PERSON>", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Bejelentkezés frissítése", "description": "Button text for updating an existing login entry."}, "saveLogin": {"message": "Bejelentkezés menté<PERSON>", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Létező bejelentkezés frissítése", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "A bejelentkezés mentésre került.", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "A bejelentkezés frissítésre került.", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Remek munka! Megtettük a lépéseket, hogy magunk és $ORGANIZATION$ biztonságosabbá váljon.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Köszönet $ORGANIZATION$ biztonságosabbá válásához. További $TASK_COUNT$ frissítend<PERSON> j<PERSON><PERSON><PERSON> van még.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Következő jelszó megváltoztatása", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Hiba történt a bejelentkezés mentésekor.", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Létező bejelentkezés frissítés kérése"}, "changedPasswordNotificationDesc": {"message": "A bejelentkezési jelszó frissítésének kérése a webhelyen történő változás érzékelésekor."}, "changedPasswordNotificationDescAlt": {"message": "A bejelentkezési jelszó frissítésének kérése, ha változást lett érzékelve egy webhelyen. Minden bejelentkezett fiókra vonatkozik."}, "enableUsePasskeys": {"message": "Kérés a hozzáférési kulcs mentésére és használatára"}, "usePasskeysDesc": {"message": "Kérés az új hozzáféréi kulcsok mentésére vagy bejelentkezés a széfben tárolt hozzáférési kulcsokkal. Minden bejelentkezett fiókra vonatkozik."}, "notificationChangeDesc": {"message": "Frissítésre kerüljön a jelszó a Bitwardenben?"}, "notificationChangeSave": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notificationUnlockDesc": {"message": "A Bitwarden széf feloldása az automatikus kitöltési kérés teljesítéséhez."}, "notificationUnlock": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "additionalOptions": {"message": "Kiegészítő opciók"}, "enableContextMenuItem": {"message": "<PERSON><PERSON><PERSON> menü opciók megjelenítése"}, "contextMenuItemDesc": {"message": "Másodlagos kattintással férhetünk hozzá a webhely jelszó-generálásához és a egyező bejelentkezésekhez."}, "contextMenuItemDescAlt": {"message": "Másodlagos kattintással eléérhető a jelszógenerálás és a megfelelő bejelentkezési adatok a webhelyhez. Minden bejelentkezett fiókra vonatkozik."}, "defaultUriMatchDetection": {"message": "Alapértelmezett URI egyezés érzékelés", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Az URI egyezés érzékelés alapértelmezett módjának kiválasztása a bejelentkezéseknél olyan műveletek esetében mint az automatikus kitöltés."}, "theme": {"message": "<PERSON><PERSON><PERSON>"}, "themeDesc": {"message": "Az alkalmazás színtémájának megváltoztatása."}, "themeDescAlt": {"message": "Az alkalmazás színtéma módosítása. Minden bejelentkezett fiókra vonatkozik."}, "dark": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Dark color"}, "light": {"message": "<PERSON>il<PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Exportálás innen:"}, "exportVault": {"message": "Széf <PERSON>"}, "fileFormat": {"message": "Fájlformátum"}, "fileEncryptedExportWarningDesc": {"message": "Ez a fájl exportálás jelszóval védett és a visszafejtéshez a fájl jelszó megadása szükséges."}, "filePassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "exportPasswordDescription": {"message": "Ezt a jelszó kerül használatba a fájl exportálására és importálására."}, "accountRestrictedOptionDescription": {"message": "Használjuk a fiók felhasználónevéből és mesterjelszavából származó fióktitkosítási kulcsot az exportálás titkosításához és az importálást csak az aktuális Bitwarden fiókra korlátozzuk."}, "passwordProtectedOptionDescription": {"message": "Állítsunk be egy fájl jelszót az exportálás titkosításához és importáljuk azt bármely Bitwarden fiókba a visszafejtéshez használt j<PERSON><PERSON><PERSON>."}, "exportTypeHeading": {"message": "Exportálási típus"}, "accountRestricted": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "A “Fájl jelszó”  és a  “Fájl jelszó megerősítés“ nem egyezik."}, "warning": {"message": "FIGYELEM", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Figyelmeztetés", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Széf export<PERSON><PERSON>ás megerősítése"}, "exportWarningDesc": {"message": "Ez az exportálás titkosítás nélkül tartalmazza a széfadatokat. Nem célszerű az exportált fájlt nem biztonságos csatornákon tárolni és tovább küldeni (például emailben). A felhasználás után erősen ajánlott a törlés."}, "encExportKeyWarningDesc": {"message": "Ez az exportálás titkosítja az adatokat a fiók titkosítási kulcsával. Ha valaha a diók forgatási kulcsa más lesz, akkor újra exportálni kell, mert nem lehet visszafejteni ezt az exportálási fájlt."}, "encExportAccountWarningDesc": {"message": "A fiók titkosítási kulcsai minden Bitwarden felhasználói fiókhoz egyediek, ezért nem importálhatunk titkosított exportálást egy másik fiókba."}, "exportMasterPassword": {"message": "Add meg a jelszavad a széf adataid exportálásához."}, "shared": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "bitwardenForBusinessPageDesc": {"message": "A Bitwarden for Business lehetővé teszi a széf elemeinek megosztását másokkal egy szervezet használatával. További információ a bitwarden.com webhelyen."}, "moveToOrganization": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "movedItemToOrg": {"message": "$ITEMNAME$ átkerült $ORGNAME$ szervezethez", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Válasszunk egy s<PERSON>, ah<PERSON> áthelyezni szeretnénk ezt az elemet. A szervezetbe áthelyezés átruházza az elem tulajdonjogát az adott szervezetre. Az áthelyezés után többé nem leszünk az elem közvetlen tulajdonosa."}, "learnMore": {"message": "<PERSON><PERSON><PERSON> meg többet"}, "authenticatorKeyTotp": {"message": "Hitelesítő kul<PERSON> (egyszeri időalapú)"}, "verificationCodeTotp": {"message": "<PERSON><PERSON><PERSON><PERSON> kód (egyszeri időalapú)"}, "copyVerificationCode": {"message": "<PERSON><PERSON><PERSON><PERSON> kód m<PERSON>ol<PERSON>"}, "attachments": {"message": "Mellékletek"}, "deleteAttachment": {"message": "Mellékletek törlése"}, "deleteAttachmentConfirmation": {"message": "Biztos törölni akarod ezt a mellékletet?"}, "deletedAttachment": {"message": "A melléklet törlésre került."}, "newAttachment": {"message": "<PERSON><PERSON> <PERSON> ho<PERSON>"}, "noAttachments": {"message": "<PERSON><PERSON><PERSON><PERSON> mellékletek."}, "attachmentSaved": {"message": "A melléklet mentésre került."}, "file": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fileToShare": {"message": "Megosztandó fájl"}, "selectFile": {"message": "Válasszunk egy fájlt."}, "maxFileSize": {"message": "A naximális fájlméret 500 MB."}, "featureUnavailable": {"message": "A funkció nem érhető el."}, "encryptionKeyMigrationRequired": {"message": "Titkosítási kulcs migráció szükséges. Jelentkezzünk be a webes széfen keresztül a titkosítási kulcs frissítéséhez."}, "premiumMembership": {"message": "Prémium tagság"}, "premiumManage": {"message": "Tagság kez<PERSON>e"}, "premiumManageAlert": {"message": "Prémium tagságod a bitwarden.com webes széfén tudod kezelni. Szeretnéd meglátogatni a most a weboldalt?"}, "premiumRefresh": {"message": "Tagság f<PERSON>"}, "premiumNotCurrentMember": {"message": "Jelenleg nem vagyunk prémium tag."}, "premiumSignUpAndGet": {"message": "Regisztráció a prémium tagságra az alábbi funkciókért:"}, "ppremiumSignUpStorage": {"message": "1 GB titkosított tárhely a fájlmellékleteknek."}, "premiumSignUpEmergency": {"message": "Sürgősségi <PERSON>"}, "premiumSignUpTwoStepOptions": {"message": "Saját k<PERSON>tl<PERSON>csős bejelentkezési lehetőségek mint a YubiKey és a Duo."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON><PERSON><PERSON>, fiók biztonság és adatszivárgási jelentések a széf biztonsága érdekében."}, "ppremiumSignUpTotp": {"message": "TOTP ellenőrző kód (2FA) generátor a széfedben lévő bejelentkezésekhez."}, "ppremiumSignUpSupport": {"message": "Kiemelt ügyfélszolgálati."}, "ppremiumSignUpFuture": {"message": "Minden jövőbeli prémium funkció. Hamarosan jön még több."}, "premiumPurchase": {"message": "Prémium funkció megvásárlása"}, "premiumPurchaseAlertV2": {"message": "Prémium szolgáltatást vásárolhatunk a Bitwarden webalkalmazás fiókbeállításai között."}, "premiumCurrentMember": {"message": "Prémium tag vagyunk!"}, "premiumCurrentMemberThanks": {"message": "Köszönjük a Bitwarden támogatását."}, "premiumFeatures": {"message": "Áttérés prémium verzióra és fogadás:"}, "premiumPrice": {"message": "Mindez csak $PRICE$ /év.", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "Mindez csak $PRICE$ /év!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "enableAutoTotpCopy": {"message": "TOTP automatikus másolása"}, "disableAutoTotpCopyDesc": {"message": "Ha a bejelentkezéshez csatolva van egy hitelesítő kulcs, a TOTP ellenőrző kód automatikusan a vágólapra kerül a bejelentkezési adatok megadásánál."}, "enableAutoBiometricsPrompt": {"message": "Biometria kérése indításkor"}, "premiumRequired": {"message": "Prémium funkció szükséges"}, "premiumRequiredDesc": {"message": "Prémium tagság szükséges ennek a funkciónak eléréséhez a jövőben."}, "authenticationTimeout": {"message": "Hitelesítési időkifutás"}, "authenticationSessionTimedOut": {"message": "A hitelesítési munkamenet időkifutással lejárt. Indítsuk újra a bejelentkezési folyamatot."}, "verificationCodeEmailSent": {"message": "Az ellenőrző kód elküldésre került $EMAIL$ email címre.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Ne kérdezzen újra ezen az eszközön 30 napig"}, "selectAnotherMethod": {"message": "Másik módszer választás", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "insertU2f": {"message": "Illesz be biztonsági kulcsod a számítógéped egyik USB portjába. <PERSON> van rajta egy gomb, nyomd le."}, "openInNewTab": {"message": "Megnyitás új fü<PERSON>ön"}, "webAuthnAuthenticate": {"message": "WebAutn <PERSON>"}, "readSecurityKey": {"message": "Biztonsági kulcs olvasása"}, "awaitingSecurityKeyInteraction": {"message": "Várakozás a biztonsági kulcs interakciójára..."}, "loginUnavailable": {"message": "A bejelentkezés nem érhető el."}, "noTwoStepProviders": {"message": "Ezen a fiókon kétlépcsős bejelentkezés van engedélyezve, de ez az eszköz nem támogatja egyik beállított kétlépcsős szolgáltatót sem."}, "noTwoStepProviders2": {"message": "Kérlek használj támogatott böngészőt (mint például a Chrome) és/vagy adj hozzá jobban támogatott szolgáltatásokat melyek jobban támogatottak más böngészőkben is (mint például egy hitelesítő alkalmazás)."}, "twoStepOptions": {"message": "Kétlépcsős bejelentkezés opciók"}, "selectTwoStepLoginMethod": {"message": "Kétlépcsős bejelentkezési mód használata"}, "recoveryCodeDesc": {"message": "Elveszett a hozzáférés az összes kétlépcsős szolgáltatóhoz? A helyreállító kód használatával letilthatók fiókból a kétlépcsős szolgáltatók."}, "recoveryCodeTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kód"}, "authenticatorAppTitle": {"message": "Hitelesítő alkalmazás"}, "authenticatorAppDescV2": {"message": "Adjunk meg egy hitelesítő alkalmazás, például a Bitwarden Authenticator által generált kódot.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "YubiKey OTP biztonsági kulcs"}, "yubiKeyDesc": {"message": "Haszná<PERSON><PERSON> egy <PERSON>-t, hogy hozz<PERSON> férhess a felhasználódhoz. Működik a YubiKey 4, 4 Nan<PERSON>, 4<PERSON>, és NEO eszközökkel."}, "duoDescV2": {"message": "Adjuk meg a Duo Security által generált kódot.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Ellenőrzés szervezeti Duo Security segítségével a Duo Mobile alkalmazás, SMS, telefonhívás vagy U2F biztonsági kulcs használatával.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Használjunk b<PERSON><PERSON>ilyen WebAuthn engedélyezett biztonsági kulcsot a saját fiók eléréséhez."}, "emailTitle": {"message": "E-mail"}, "emailDescV2": {"message": "Adjuk meg az email címre elküldött kódot."}, "selfHostedEnvironment": {"message": "Saját üzemeltetésű környezet"}, "selfHostedBaseUrlHint": {"message": "Adjuk meg a helyileg tárolt Bitwarden telepítés alap webcímét. Példa: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "Speciális konfigurációhoz külön-külön megadhatjuk az egyes szolgáltatások alap webcímét."}, "selfHostedEnvFormInvalid": {"message": "Hozzá kell adni az alapszerver webcímét vagy legalább egy egyedi környezetet."}, "customEnvironment": {"message": "Egyedi környezet"}, "baseUrl": {"message": "Szerver URL"}, "selfHostBaseUrl": {"message": "Saját üzemeltetésű szerver webcím", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API szerver webcím"}, "webVaultUrl": {"message": "Webes széf s<PERSON>ver webcím"}, "identityUrl": {"message": "Személyazonosság szerver webcím"}, "notificationsUrl": {"message": "Értesítési s<PERSON> webcím"}, "iconsUrl": {"message": "Ikonok szerver webcím"}, "environmentSaved": {"message": "A környezeti webcímek mentésre kerültek."}, "showAutoFillMenuOnFormFields": {"message": "Automatikus kitöltés menü megjelenítése az űrlapmezőkön", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Az identitások megjelenítése javaslatként"}, "showInlineMenuCardsLabel": {"message": "A kártyák megjelenítése javaslatként"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Az ütközések elkerülése érdekében kapcsoljuk ki a böngésző beépített jelszókezelő beállításait."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "A böngésző beállítások szerkesztése."}, "autofillOverlayVisibilityOff": {"message": "<PERSON>", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "Ha az automatikus kitöltés menü került kiválasztásra", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Automatikus kitöltés engedélyezése oldal betöltéskor"}, "enableAutoFillOnPageLoadDesc": {"message": "Ha egy bejelentkezési űrlap észlelésre került, az adatok automatikus kitöltése az oldal betöltésekor."}, "experimentalFeature": {"message": "Az oldalbetöltésnél automatikus kitöltést a feltört vagy nem megbízhatató weboldalak kihasználhatják."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "További információk az automatikus kitöltésről"}, "defaultAutoFillOnPageLoad": {"message": "Alapértelmezett beállítások bejelentkezési elemekhez"}, "defaultAutoFillOnPageLoadDesc": {"message": "Az egyes bejelentkezési elemeknél kikapcsolhatjuk oldalbetöltéskor az automatikus kitöltést az elem Szerkesztés nézetében."}, "itemAutoFillOnPageLoad": {"message": "Automatikus kitöltés oldal betöltésnél (Ha engedélyezett az opcióknál)"}, "autoFillOnPageLoadUseDefault": {"message": "Alapbeállítások használata"}, "autoFillOnPageLoadYes": {"message": "Automatikus kitöltés oldalbetöltésnél"}, "autoFillOnPageLoadNo": {"message": "Nincs automatikus kitöltés oldalbetöltéskor"}, "commandOpenPopup": {"message": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "commandOpenSidebar": {"message": "<PERSON><PERSON><PERSON><PERSON>alsá<PERSON>"}, "commandAutofillLoginDesc": {"message": "Az aktuális webhelynél az utoljára használt bejelentkezés automatikus kitöltése."}, "commandAutofillCardDesc": {"message": "Az aktuális webhelynél az utoljára használt kártya."}, "commandAutofillIdentityDesc": {"message": "Az aktuális webhelynél az utoljára használt személyazonosító."}, "commandGeneratePasswordDesc": {"message": "Új véletlenszerű jelszó generálása ás másolása a vágólapra."}, "commandLockVaultDesc": {"message": "A széf zárolása"}, "customFields": {"message": "Egyedi mezők"}, "copyValue": {"message": "Ért<PERSON>k m<PERSON>olás<PERSON>"}, "value": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "newCustomField": {"message": "<PERSON>j egyedi me<PERSON>"}, "dragToSort": {"message": "Húzás a rendezéshez"}, "dragToReorder": {"message": "Átrendezés <PERSON>"}, "cfTypeText": {"message": "Szöveg"}, "cfTypeHidden": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "cfTypeBoolean": {"message": "Boolean (Logikai)"}, "cfTypeCheckbox": {"message": "Jelölődoboz"}, "cfTypeLinked": {"message": "Csatolva", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Csatolt érték", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Az ellenőrző kódot tartalmazó email egy olyan felugró ablakban nyílik meg, mely a mellette levő területre kattinva bezáródik. Szeretnéd az emailt egy olyan ablakban megnyitni, ami nem záródhat így be?"}, "popupU2fCloseMessage": {"message": "Ez a böngésző nem dolgozza fel az U2F kéréseket ebben a felbukkanó ablakban. Szeretnénk megnyitni a felbukkanó ablakot új böngészőablakban az U2F segítségével történő bejelentkezéshez?"}, "enableFavicon": {"message": "<PERSON><PERSON><PERSON> ikonok megjelenítése"}, "faviconDesc": {"message": "Felismerhető kép megjelenítése minden bejelentkezés mellett."}, "faviconDescAlt": {"message": "Minden bejelentkezés mellett egy felismerhető kép megjelenítése. Minden bejelentkezett fiókra vonatkozik."}, "enableBadgeCounter": {"message": "Szá<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON> me<PERSON>"}, "badgeCounterDesc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> meg, hogy h<PERSON><PERSON> be<PERSON>kez<PERSON> van az aktuális weboldalnál."}, "cardholderName": {"message": "K<PERSON>rtyatulajdonos neve"}, "number": {"message": "Szám"}, "brand": {"message": "<PERSON><PERSON><PERSON>"}, "expirationMonth": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "expirationYear": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "expiration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "january": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "march": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "april": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "may": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "june": {"message": "<PERSON><PERSON><PERSON>"}, "july": {"message": "<PERSON><PERSON><PERSON>"}, "august": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "september": {"message": "Szeptember"}, "october": {"message": "Október"}, "november": {"message": "November"}, "december": {"message": "December"}, "securityCode": {"message": "Biztons<PERSON><PERSON>"}, "ex": {"message": "<PERSON><PERSON><PERSON>:"}, "title": {"message": "Titulus"}, "mr": {"message": "<PERSON><PERSON>"}, "mrs": {"message": "<PERSON><PERSON><PERSON>"}, "ms": {"message": "Kisasszony"}, "dr": {"message": "Dr"}, "mx": {"message": "Mx"}, "firstName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "middleName": {"message": "Középső név"}, "lastName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>v"}, "fullName": {"message": "Teljes név"}, "identityName": {"message": "Személyazonosság megnevezés"}, "company": {"message": "Cég"}, "ssn": {"message": "Társadalombiztosítási szám"}, "passportNumber": {"message": "Útlevélszám"}, "licenseNumber": {"message": "Vez<PERSON><PERSON><PERSON> enged<PERSON><PERSON>"}, "email": {"message": "E-mail"}, "phone": {"message": "Telefonszám"}, "address": {"message": "Lakcím"}, "address1": {"message": "Cím 1"}, "address2": {"message": "Cím 2"}, "address3": {"message": "Cím 3"}, "cityTown": {"message": "Település"}, "stateProvince": {"message": "Állam/Megye"}, "zipPostalCode": {"message": "Irányí<PERSON>"}, "country": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"message": "<PERSON><PERSON><PERSON>"}, "typeLogin": {"message": "Bejelentkezés"}, "typeLogins": {"message": "Bejelentkezések"}, "typeSecureNote": {"message": "Biztonságos j<PERSON>"}, "typeCard": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeIdentity": {"message": "Személyazonosság"}, "typeSshKey": {"message": "SSH kulcs"}, "newItemHeader": {"message": "Új $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "$TYPE$ szerkesztése", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "$TYPE$ megtekintése", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "generatorHistory": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "clearGeneratorHistoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>mé<PERSON>ek kiüríté<PERSON>"}, "cleargGeneratorHistoryDescription": {"message": "Ha folytatjuk, az összes bejegyzés véglegesen törlődik a generátor el<PERSON>zményeiből. Biztosan folytatjuk?"}, "back": {"message": "<PERSON><PERSON><PERSON>"}, "collections": {"message": "Gyűjtemények"}, "nCollections": {"message": "$COUNT$ gyűjtemény", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "Kedvencek"}, "popOutNewWindow": {"message": "Megnyitás új böngészőablakban"}, "refresh": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cards": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "identities": {"message": "Személyazonosságok"}, "logins": {"message": "Bejelentkezések"}, "secureNotes": {"message": "Biztonságos j<PERSON>yzetek"}, "sshKeys": {"message": "SSH kulcsok"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "A jelszóvédelemi állapot ellenőrzése."}, "passwordExposed": {"message": "Ez a jelszó már $VALUE$ alkalommal volt kitéve az adatszivárgásnak. Célszerű megváltoztatni.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Ez a jelszó nem található egyetlen ismert adatszivárgásban sem. Biztonságos a használata."}, "baseDomain": {"message": "Alap domain", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Alap domain (ajánlott)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "Tartománynév", "description": "Domain name. Ex. website.com"}, "host": {"message": "Kiszolg<PERSON><PERSON><PERSON>", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Pontos"}, "startsWith": {"message": "Ezzel kezdődik:"}, "regEx": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Alapértelmezett találat <PERSON>", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Opciók váltása"}, "toggleCurrentUris": {"message": "Aktuális URI elemek váltása", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "Aktuális URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Szervezet", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "Típusok"}, "allItems": {"message": "Összes elem"}, "noPasswordsInList": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "clearHistory": {"message": "Előzmények törlése"}, "nothingToShow": {"message": "<PERSON><PERSON><PERSON> megjelen<PERSON>the<PERSON> elem"}, "nothingGeneratedRecently": {"message": "Mostanában nem lett semmi generál<PERSON>."}, "remove": {"message": "Eltávolítás"}, "default": {"message": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dateUpdated": {"message": "A frissítés megtörtént.", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "Létrehozva", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "A jelszó frissítésre került.", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "Biztosan szeretnénk használni a \"Soha\" opciót? A zárolási opciók \"Soha\" értékre állítása a széf titkosítási kulcsát az eszközön tárolja. Ennek az opciónak a használatakor célszerű az eszköz megfelelő védettségét biztosítani."}, "noOrganizationsList": {"message": "Még nem tartozunk egyik szervezethez sem. A szervezetek lehetővé teszik az elemek megosztását más felhasználókkal."}, "noCollectionsInList": {"message": "Nincsenek megjeleníthető gyűjtemények."}, "ownership": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "whoOwnsThisItem": {"message": "Ki tulajdonolja ezt az elemet?"}, "strong": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "<PERSON><PERSON>", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "Gyenge mesterjelszó"}, "weakMasterPasswordDesc": {"message": "A választott mesterjelszó gyenge. <PERSON><PERSON><PERSON><PERSON> (vagy kife<PERSON>t) kell használni a Bitwarden fiók megfelelő védelme érdekében. Biztosan ezt a mesterjelszót szeretnénk használni?"}, "pin": {"message": "<PERSON><PERSON><PERSON>", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Felnyitás pinkóddal"}, "setYourPinTitle": {"message": "PIN kód be<PERSON>ll<PERSON>a"}, "setYourPinButton": {"message": "PIN kód be<PERSON>ll<PERSON>a"}, "setYourPinCode": {"message": "A pinkód beállítása a Bitwarden feloldásához. A pinkód beállítás alaphelyzetbe kerül, ha teljesen kijelentkezünk az alkalmazásból."}, "setYourPinCode1": {"message": "A Bitwarden feloldásához a PIN kódot használjuk a mesterjelszó helyett. A PIN kód alaphelyzetbe kerül, ha teljesen kijelentkezünk a Bitwardenből."}, "pinRequired": {"message": "A pinkód szükséges."}, "invalidPin": {"message": "A pinkód érvénytelen."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Túl sok az érvénytelen PIN beviteli kísérlet. Kijelentkezés történik."}, "unlockWithBiometrics": {"message": "Biometrik<PERSON> felo<PERSON>"}, "unlockWithMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "awaitDesktop": {"message": "Várakozás megerősítésre az asztali alkalmazásból"}, "awaitDesktopDesc": {"message": "Erősítsük meg a biometrikus adatok használatát a Bitwarden asztali alkalmazásban a biometrikus adatok engedélyezéséhez a böngészőben."}, "lockWithMasterPassOnRestart": {"message": "Lezárás mesterjelszóval a böngésző újraindításakor"}, "lockWithMasterPassOnRestart1": {"message": "Lezárás mesterjelszóval a böngésző újraindításakor"}, "selectOneCollection": {"message": "Legalább egy gyűjteményt ki kell választani."}, "cloneItem": {"message": "<PERSON>em <PERSON>oz<PERSON>"}, "clone": {"message": "Klónozás"}, "passwordGenerator": {"message": "<PERSON><PERSON><PERSON><PERSON> gener<PERSON>"}, "usernameGenerator": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ónév generátor"}, "useThisEmail": {"message": "Ezen email használata"}, "useThisPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "useThisUsername": {"message": "Felhasználónév használata"}, "securePasswordGenerated": {"message": "A biztonságos jelszó generálásra került! Ne felejtsük el frissíteni a jelszót a webhelyen is."}, "useGeneratorHelpTextPartOne": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "er<PERSON><PERSON> egy<PERSON>i j<PERSON> létrehozásához", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "vaultTimeoutAction": {"message": "<PERSON><PERSON><PERSON><PERSON> művelet"}, "vaultTimeoutAction1": {"message": "Időkifutási művelet"}, "newCustomizationOptionsCalloutTitle": {"message": "Új testreszabási opciók"}, "newCustomizationOptionsCalloutContent": {"message": "Szabjuk testre tárhely élményét gyors másolási műveletekkel, kompakt móddal és még sok mással!"}, "newCustomizationOptionsCalloutLink": {"message": "Az összes megjelenési beállítás megtekintése"}, "lock": {"message": "Lezárás", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "Keresés a lomtárban"}, "permanentlyDeleteItem": {"message": "Az elem végleges törlése"}, "permanentlyDeleteItemConfirmation": {"message": "Biztosan véglegesen törlésre kerüljön ez az elem?"}, "permanentlyDeletedItem": {"message": "Véglegesen törölt elem"}, "restoreItem": {"message": "<PERSON><PERSON>í<PERSON>"}, "restoredItem": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elem"}, "alreadyHaveAccount": {"message": "<PERSON> már saj<PERSON>?"}, "vaultTimeoutLogOutConfirmation": {"message": "Kijelentkezve az összes széf elérés eltávolításra kerül és webes hitelesítésre van szükség az időkifutás után. Biztosan szeretnénk használni ezt a beállítást?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Időkifutás művelet megerősítés"}, "autoFillAndSave": {"message": "Automatikus kitöltés és mentés"}, "fillAndSave": {"message": "Kitöltés és mentés"}, "autoFillSuccessAndSavedUri": {"message": "Automatikusan kitöltött elem és mentett URI"}, "autoFillSuccess": {"message": "Automatikusan kitöltött elem"}, "insecurePageWarning": {"message": "Figyelmeztetés: Ez egy nem biztonságos HTTP oldal és az elküldött információkat mások láthatják és módosíthatják. Ezt a bejelentkezést eredetileg egy biztonságos (HTTPS) oldalra mentették."}, "insecurePageWarningFillPrompt": {"message": "Még mindig ki szeretnénk tölteni ezt a bejelentkezést?"}, "autofillIframeWarning": {"message": "Az űrlapot egy másik domain tárolja, mint a mentett bejelentkezés URI-ja. Az automatikus kitöltéshez válasszuk az OK gombot, a leállításhoz pedig a Mégsem lehetőséget."}, "autofillIframeWarningTip": {"message": "Ennek a figyelmeztetésnek a jövőbeni elkerülése érdekében mentsük el ezt az URI-t - $HOSTNAME$ - a Bitwarden bejelentkezési elemébe ennél a webhelynél.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "Mesterje<PERSON><PERSON>ó beállítása"}, "currentMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>zó"}, "newMasterPass": {"message": "<PERSON><PERSON>"}, "confirmNewMasterPass": {"message": "Új mesterjelszó megerősítése"}, "masterPasswordPolicyInEffect": {"message": "Egy vagy több szervezeti rendszabályhoz mesterjelszó szükséges a következő követelmények megfeleléséhez:"}, "policyInEffectMinComplexity": {"message": "Minimális összetettségi pontszám $SCORE$ értékhez", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Minimális hossz $LENGTH$ értékből", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "<PERSON><PERSON> vagy több nagybetűs karaktert tartalmaz"}, "policyInEffectLowercase": {"message": "<PERSON><PERSON> vagy több kisbetűs karaktert tartalmaz"}, "policyInEffectNumbers": {"message": "<PERSON><PERSON> vagy több sz<PERSON><PERSON> tartalmaz"}, "policyInEffectSpecial": {"message": "$CHARS$ speciális karak<PERSON>ől egyet vagy többet tartalmaz", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Az új mesterjelszó nem felel meg a szabály követelményeknek."}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Leiratkozás"}, "atAnyTime": {"message": "b<PERSON><PERSON><PERSON><PERSON>."}, "byContinuingYouAgreeToThe": {"message": "A folytatással elfogadjuk"}, "and": {"message": "és"}, "acceptPolicies": {"message": "A doboz bejelölésével elfogadjuk a következőket:"}, "acceptPoliciesRequired": {"message": "A szolgáltatási feltételeket és az adatvédelmi irányelveket nem vették figyelembe."}, "termsOfService": {"message": "Szolgáltatási feltételek"}, "privacyPolicy": {"message": "Adatvédelem"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "<PERSON>z új jelszó nem lehet azonos a jelenlegi jelszóval."}, "hintEqualsPassword": {"message": "A jelszavas tipp nem lehet azonos a jelszóval."}, "ok": {"message": "Ok"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "Asztali szinkronizálás ellenőrzés"}, "desktopIntegrationVerificationText": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az asztali alkalmazás megjeleníti-e ezt az ujjlenyomatot: "}, "desktopIntegrationDisabledTitle": {"message": "A böngésző integráció nincs beüzemelve."}, "desktopIntegrationDisabledDesc": {"message": "A böngésző integráció nincs beüzemelve a Bitwarden asztali alkalmazásban. Engedélyezzük az asztali alkalmazás beállításai között."}, "startDesktopTitle": {"message": "A Bitwarden asztali alkalmazás indítása"}, "startDesktopDesc": {"message": "A Bitwarden asztali alkalmazást el kell indítani a biometrikus adatokkal feloldás használatához."}, "errorEnableBiometricTitle": {"message": "<PERSON>em le<PERSON>t beüzemelni a biometrikus adatokat."}, "errorEnableBiometricDesc": {"message": "A műveletet az asztali alkalmazás törölte."}, "nativeMessagingInvalidEncryptionDesc": {"message": "Az asztali alkalmazás érvénytelenítette a biztonságos kommunikációs csatornát. Próbálkozzunk újra ezzel a művelettel"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Az asztali kommunikáció megszakadt."}, "nativeMessagingWrongUserDesc": {"message": "<PERSON>z asztali alkalmazás egy másik fiókba van bejelentkezve. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy mindkét alkalmazást azonos fiókba van bejelentkezve."}, "nativeMessagingWrongUserTitle": {"message": "A fiók nem egyezik."}, "nativeMessagingWrongUserKeyTitle": {"message": "A biometrikus kulcs nem egyezik."}, "nativeMessagingWrongUserKeyDesc": {"message": "A biometrikus feloldás nem sikerült. A biometrikus titkos kulcs nem tudta feloldani a széfet. Próbáljuk újra beállítani a biometrikus adatokat."}, "biometricsNotEnabledTitle": {"message": "A biometrikus adatok nincsenek beüzemelve."}, "biometricsNotEnabledDesc": {"message": "A böngésző biometrikus adataihoz először az asztali biometrikus adatokat kell beüzemelni a beállításokban."}, "biometricsNotSupportedTitle": {"message": "A biometrikus adatok nem támogatottak."}, "biometricsNotSupportedDesc": {"message": "A böngésző biometrikus adatait ez az eszköz nem támogatja."}, "biometricsNotUnlockedTitle": {"message": "A felhasználó z<PERSON>va van vagy kijelentkezett."}, "biometricsNotUnlockedDesc": {"message": "Oldjuk fel a felhasználó zárolását az asztali alkalmazásban és próbáljuk újra."}, "biometricsNotAvailableTitle": {"message": "A biometrikus feloldás nem érhető el."}, "biometricsNotAvailableDesc": {"message": "A biometrikus feloldás jelenleg nem érhető el. Próbáljuk újra később."}, "biometricsFailedTitle": {"message": "A biometria nem sikerült."}, "biometricsFailedDesc": {"message": "A biometrikus adatokat nem lehet <PERSON>, fontoljuk meg a mesterjelszó használatát vagy a kijelentkezést. Ha ez továbbra is fennáll, forduljunk a Bitwarden ügyfélszolgálatához."}, "nativeMessaginPermissionErrorTitle": {"message": "A jogosultság nincs megadva."}, "nativeMessaginPermissionErrorDesc": {"message": "A Bitwarden Desktop alkalmazással való kommunikáció engedélye nélkül nem adhatunk meg biometrikus adatokat a böngésző kiterjesztésében. Próbáljuk újra."}, "nativeMessaginPermissionSidebarTitle": {"message": "Engedélykérési hiba tö<PERSON>."}, "nativeMessaginPermissionSidebarDesc": {"message": "Ez a művelet nem hajtható végre az oldalsávon. Próbáljuk meg újra a műveletet a felbukkanó ablakban."}, "personalOwnershipSubmitError": {"message": "Egy vállalati házirend miatt korlátozásra került az elemek személyes tárolóba történő mentése. Módosítsuk a Tulajdon opciót egy szervezetre és válasszunk az elérhető gyűjtemények közül."}, "personalOwnershipPolicyInEffect": {"message": "A szervezeti házirend befolyásolja a tulajdonosi opciókat."}, "personalOwnershipPolicyInEffectImports": {"message": "A szervezeti politika blokkolta az elemek importálását az egyedi széfbe."}, "domainsTitle": {"message": "Tartomány", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Letiltott tartományok"}, "learnMoreAboutBlockedDomains": {"message": "Tov<PERSON><PERSON><PERSON> információ a letiltott tartományokról"}, "excludedDomains": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "excludedDomainsDesc": {"message": "A Bitwarden nem fogja kérni a domainek bejelentkezési adatainak mentését. A változások életbe lépéséhez frissíteni kell az oldalt."}, "excludedDomainsDescAlt": {"message": "A Bitwarden nem kéri a bejelentkezési adatok mentését ezeknél a tartományoknál az összes bejelentkezési fiókra vonatkozva. A változtatások életbe lépéséhez frissíteni kell az oldalt."}, "blockedDomainsDesc": {"message": "Az automatikus kitöltés és az egyéb kapcsolódó funkciók ezeken a webhelyeken nincsenek a kínálatban. A változtatások életbe lépéséhez frissíteni kell az oldalt."}, "autofillBlockedNoticeV2": {"message": "Az automatikus kitöltés blokkolásra került ezen a webhelyen."}, "autofillBlockedNoticeGuidance": {"message": "Megváltoztatás a beállításokban"}, "change": {"message": "Módosítás"}, "changeButtonTitle": {"message": "Jelszó módosítás - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPasswords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ egy jelszó megváltoztatását kéri, mert az kockázatos.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ $COUNT$ jelszó megváltoztatását kéri, mert azok kockázatosak.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "A szervezetek $COUNT$ jelszó megváltoztatását k<PERSON>, mert azok kockázatosak.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "reviewAndChangeAtRiskPassword": {"message": "Tekintsük át és módosítsuk az egyik veszélyeztetett jelszót."}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Tekintsük át és módosítsunk $COUNT$ kockázatnak kitett jelszót.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Változtassuk meg gyorsabban a veszélyeztetett jelszavakat."}, "changeAtRiskPasswordsFasterDesc": {"message": "Frissítsük a beállításokat, hogy gyorsan automatikusan kitölthessük a jelszavakat és újakat generálhassunk."}, "reviewAtRiskLogins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>zések <PERSON>kin<PERSON>"}, "reviewAtRiskPasswords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>"}, "reviewAtRiskLoginsSlideDesc": {"message": "A szervezeti jelszavak k<PERSON><PERSON>, mert g<PERSON>k, <PERSON><PERSON>ra felhasználásra kerültek és/vagy nyilvánosságra kerültek.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "A kockázatos bejelentkezések listájának illusztrációja."}, "generatePasswordSlideDesc": {"message": "Gyorsan gene<PERSON><PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON> j<PERSON> a Bitwarden automatikus kitöltési menüjével a kockázatos webhelyen.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "A Bitwarden automatikus kitöltési menüjének illusztráci<PERSON>ja, amely egy generált j<PERSON>ót jelenít meg."}, "updateInBitwarden": {"message": "Frissítés a Bitwardenben"}, "updateInBitwardenSlideDesc": {"message": "A Bitwarden ezután felkér a jelszó frissítésére a jelszókezelőben.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illusztráció a Bitwarden értesítéséről, amely felszólítja a felhasználót a bejelentkezési adatok frissítésére."}, "turnOnAutofill": {"message": "Automatikus kitöltés bekapcsolása"}, "turnedOnAutofill": {"message": "Az automatikus kitöltés bekapcsolásra került."}, "dismiss": {"message": "Elvetés"}, "websiteItemLabel": {"message": "Webhely $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ nem érvényes domain.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "A letiltott tartomány módosítások mentésre kerültek."}, "excludedDomainsSavedSuccess": {"message": "A kizárt tartomány módosítások mentésre kerültek."}, "limitSendViews": {"message": "Megtekintések korlátozása"}, "limitSendViewsHint": {"message": "Senki sem tudja megtekinteni ezt a Send elemet a korlát elérése után.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ megtekintés maradt", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send részletek", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "Szöveg"}, "sendTypeTextToShare": {"message": "Megosztandó szöveg"}, "sendTypeFile": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "allSends": {"message": "Összes Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "hideTextByDefault": {"message": "Szöveg elrejtése alapértelmezetten"}, "expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "passwordProtected": {"message": "Jelszóval védett"}, "copyLink": {"message": "Hivatkozás másolása"}, "copySendLink": {"message": "Send hivatkozás másolása", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "<PERSON><PERSON><PERSON><PERSON> eltávolítása"}, "delete": {"message": "Törlés"}, "removedPassword": {"message": "A jelszó eltávolításra került."}, "deletedSend": {"message": "A Send törlésre került.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Send hivat<PERSON><PERSON>ás", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "<PERSON><PERSON><PERSON>"}, "removePasswordConfirmation": {"message": "Biztosan eltávolításra kerüljön ez a jelszó?"}, "deleteSend": {"message": "Send törlése", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "Biztosan törlésre kerüljön ez a Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Biztosan véglegesen törlésre kerüljön ez a Send elem?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "Send szerkesztése", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "deletionDateDescV2": {"message": "A Send véglegesen törölve lesz ebben az időpontban.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "oneDay": {"message": "1 nap"}, "days": {"message": "$DAYS$ nap", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendPasswordDescV3": {"message": "Adjunk meg egy opcionális jelszót a címzetteknek a Send eléréséhez.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "<PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "<PERSON><PERSON>"}, "sendDisabled": {"message": "A  Send eltávolításra került.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "A vállalati házirend miatt csak egy meglévő Send törölhető.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "A Send létrejött.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "A Send sikeresen lé<PERSON>jö<PERSON>!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "A Send bárki számára elérhető a hivatkozással a következő 1 órában.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "A Send bárki számára elérhető a hivatkozással a következő $HOURS$ órában.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "A Send bárki számára elérhető a hivatkozással a következő 1 napban.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "A Send bárki számára elérhető a hivatkozással a következő $DAYS$ napban.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "A Send hivatkozás másolásra került.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "A Send mentésre került.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Bővítmény átthelyezése?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "A Send fájl létrehozásához át kell helyezni a bővítményt egy új ablakba.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "A fájl kiválasztásához nyissuk meg a kiterjesztést az oldalsávon (ha lehetséges) vagy kattintsunk erre a sávra új ablak felbukkanásához."}, "sendFirefoxFileWarning": {"message": "Firefox esetén nyissuk meg a bővítményt az oldalsávon vafy erre a hirdetőtáblára kattintva új felbukkanó ablak nyílik meg."}, "sendSafariFileWarning": {"message": "A fájl kiválasztásához Safariban kattintsunk erre a hirdetőtáblára kattintva új ablak nyílik meg."}, "popOut": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sendFileCalloutHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "expirationDateIsInvalid": {"message": "A megadott lejárati idő nem érvényes."}, "deletionDateIsInvalid": {"message": "A megadott törlési dátum nem érvényes."}, "expirationDateAndTimeRequired": {"message": "Lejárati dátum és idő megadása szükséges."}, "deletionDateAndTimeRequired": {"message": "Törlési dátum és idő megadása szükséges."}, "dateParsingError": {"message": "Hiba történt a törlési és a lejárati dátum menté<PERSON>kor."}, "hideYourEmail": {"message": "Saját email cím elrejté<PERSON> a megtekintések elől."}, "passwordPrompt": {"message": "Mesterjelszó ismételt megadás"}, "passwordConfirmation": {"message": "Mesterjelszó megerősítése"}, "passwordConfirmationDesc": {"message": "Ez a művelet védett. A folytatásért ismételten meg kell adni a mesterjelszőt az személyazonosság ellenőrzéséhez."}, "emailVerificationRequired": {"message": "Email hitelesítés szükséges"}, "emailVerifiedV2": {"message": "Az email cím ellenőrzésre került."}, "emailVerificationRequiredDesc": {"message": "A funkció használatához igazolni kell email címet. Az email cím a webtárban ellenőrizhető."}, "updatedMasterPassword": {"message": "A mesterjelszó frissítésre került."}, "updateMasterPassword": {"message": "Mesterjelszó frissí<PERSON>"}, "updateMasterPasswordWarning": {"message": "A szervezet egyik adminisztrátora nemrég megváltoztatta a mesterjelszót. A széf eléréséhez most frissíteni kell a mesterjelszót. Továbblépéskor kijelentkezés történik a jelenlegi munkamenetből és újra be kell jelentkezni. Ha van aktív munkamenet más eszközön, az még legfeljebb egy óráig aktív maradhat."}, "updateWeakMasterPasswordWarning": {"message": "A mesterjelszó nem felel meg egy vagy több szervezeti szabályzatnak. A széf eléréséhez frissíteni kell a meszerjelszót. A továbblépés kijelentkeztet az aktuális munkamenetből és újra be kell jelentkezni. A többi eszközön lévő aktív munkamenetek akár egy óráig is aktívak maradhatnak."}, "tdeDisabledMasterPasswordRequired": {"message": "A szervezete letiltotta a megbízható eszközök titkosítását. Állítsunk be egy mesterjelszót a széf eléréséhez."}, "resetPasswordPolicyAutoEnroll": {"message": "Automatikus regisztr<PERSON>"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Ennek a szervezetnek van egy vállalati házirendje, amely automatikusan regisztrál a j<PERSON><PERSON><PERSON> alaphelyzetbe állítására. A regisztráció lehetővé teszi a szervezet adminisztrátorainak a mesterjelszó megváltoztatását."}, "selectFolder": {"message": "Mappa választása..."}, "noFoldersFound": {"message": "<PERSON><PERSON> mappák.", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "A szervezeti engedélyek frissítésre kerültek, ez<PERSON><PERSON> be kell állítani egy mesterjelszót.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "A szervezet megköveteli egy mesterjelszó beállítását.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "/ $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Ellenőrzés szükséges", "description": "Default title for the user verification dialog."}, "hours": {"message": "<PERSON><PERSON>"}, "minutes": {"message": "Perc"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Erre a beállításra a vállalkozás rendszabály követelmények lettek alkalmazva."}, "vaultTimeoutPolicyInEffect": {"message": "A szervezeti szabályzata $HOURS$ órára és $MINUTES$ percre állította be a maximálisan megengedett széf időtúllépést.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ óra és $MINUTES$ perc maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Az időkifutás meghaladja a szervezet által beállított korlátozást: $HOURS$ óra és $MINUTES$ perc maxximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "A szervezeti házirendek hatással vannak a széf időkorlátjára. A széf időkorlátja legfeljebb $HOURS$ óra és $MINUTES$ perc lehet. A széf időkifutási művelete $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "A szervezeti házirendek által jelenleg beállított időkifutási művelet $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "A széf időkorlátja túllépi a szervezet által beállított korlátozást."}, "vaultExportDisabled": {"message": "A széf exportálás nem engedélyezett."}, "personalVaultExportPolicyInEffect": {"message": "<PERSON>gy vagy több szervezeti házirend tiltja a személyes széf exportálását."}, "copyCustomFieldNameInvalidElement": {"message": "Nem lehet azonosítani egy érvényes űrlapelemet. Ehelyett próbáljuk meg ellenőrizni a HTML -t."}, "copyCustomFieldNameNotUnique": {"message": "<PERSON><PERSON><PERSON> egyedi <PERSON>zonosít<PERSON>."}, "convertOrganizationEncryptionDesc": {"message": "$ORGANIZATION$ jelenleg saját tárolású aláíráskulcsú SSO szervert használ. A mesterjelszó a továbbiakban nem szükséges a szervezeti tagsági bejelentkezéshez.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "leaveOrganization": {"message": "Szervezet elhagyása"}, "removeMasterPassword": {"message": "Mesterjelszó eltávolítása"}, "removedMasterPassword": {"message": "A mesterjelszó eltávolításra került."}, "leaveOrganizationConfirmation": {"message": "Biztosan kilépünk ebből a szervezetből?"}, "leftOrganization": {"message": "Megtörtént a kilépés a szervezetből."}, "toggleCharacterCount": {"message": "Karakterszámláló váltás"}, "sessionTimeout": {"message": "A munkamenet lejárt. Lépjünk vissza és próbáljunk újra bejelentlezni."}, "exportingPersonalVaultTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "exportingIndividualVaultDescription": {"message": "$EMAIL$ email címhez társított egyedi széfek kerülnek csak exportálásra. A szervezeti széf elemei nem lesznek benne. Csak a széf információk kerülnek exportálásra és nem tartalmazzák a kapcsolódó mellékleteket.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Csak $EMAIL$ email címmel társított személyes széf elemek kerülnek exportálásra. Ebbe nem kerülnek be a szervezeti széf elemek.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Szervezeti széf exportálása"}, "exportingOrganizationVaultDesc": {"message": "Csak $ORGANIZATION$ névvel társított szervezeti széf kerül exportálásra. E<PERSON> nem kerüln<PERSON> be a személyes és más szervezeti széf elemek.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Hiba"}, "decryptionError": {"message": "Visszafejtési hiba"}, "couldNotDecryptVaultItemsBelow": {"message": "A Bitwarden nem tudta visszafejteni az alább felsorolt ​​széf elemeket."}, "contactCSToAvoidDataLossPart1": {"message": "Ügyfélszolgálat elérése", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to<PERSON><PERSON><PERSON><PERSON> ad<PERSON>vesztés elkerülése érdekében.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Felhasználónév generálása"}, "generateEmail": {"message": "<PERSON><PERSON>"}, "spinboxBoundariesHint": {"message": "Az érték legyen $MIN$ és $MAX$ között.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Használjunk $RECOMMENDED$ vagy több karaktert egy erős j<PERSON> előállításához.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " $RECOMMENDED$ vagy több szó erős jelmondat generálásához.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "További címzési email cím", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Használjuk az email cím szolgáltató alcímzési képességeit."}, "catchallEmail": {"message": "Összes email cím <PERSON>"}, "catchallEmailDesc": {"message": "Használjuk a tartomány konfigurált összes befogási bejövő postaládát."}, "random": {"message": "Véletlen"}, "randomWord": {"message": "Véletlenszerű szó"}, "websiteName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "service": {"message": "Szolgáltatás"}, "forwardedEmail": {"message": "Továbbított email álnevek"}, "forwardedEmailDesc": {"message": "Email <PERSON>v generálása külső továbbító szolgáltatással."}, "forwarderDomainName": {"message": "<PERSON><PERSON>", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Válasszunk a kiválasztott szolgáltatás által támogatott tartományt.", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ hiba: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generálta: Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Webhely: $WEBSITE$. Generálta: Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "$SERVICENAME$ API vezérjel érvénytelen.", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "$SERVICENAME$ API vezérjel érvénytelen: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ elutasította a kérést. Vegyük fel a kapcsolatot szolgáltatóval segítségért.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ elutasította a kérést: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Nem lehet beolvasni $SERVICENAME$ maszkolt email fiók azonosítót.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "$SERVICENAME$ domain érvénytelen.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "$SERVICENAME$ webcím érvénytelen.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Ismeretlen $SERVICENAME$ hiba történt.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Ismeretlen továbbító: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Kiszolglónév", "description": "Part of a URL."}, "apiAccessToken": {"message": "API hozzáférési vezérjel"}, "apiKey": {"message": "API kulcs"}, "ssoKeyConnectorError": {"message": "Kulcs csatlakozó hiba: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a kulcs csatlakozó rendelkezésre áll-e és megfelelően működik-e."}, "premiumSubcriptionRequired": {"message": "Prémium előfizetés szükséges"}, "organizationIsDisabled": {"message": "A szervezet letiltásra került."}, "disabledOrganizationFilterError": {"message": "A letiltott szervezetek elemei nem érhetők el. Vegyük fel a kapcsolatot a szervezet tulajdonosával segítségért."}, "loggingInTo": {"message": "Bejelentkezés $DOMAIN$ területre", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Szerver verzió"}, "selfHostedServer": {"message": "sa<PERSON><PERSON><PERSON>ze<PERSON>"}, "thirdParty": {"message": "<PERSON><PERSON>dik fél"}, "thirdPartyServerMessage": {"message": "Csatlakozva harmadik féltől származó kiszolgáló implementációhoz: $SERVERNAME$. Ellenőrizzük a hibákat a hivatalos szerver segítségével vagy jelentsük azokat a harmadik fél szerverének.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "utolsó megtekintés: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Bejelentkezés mesterjelszóval"}, "newAroundHere": {"message": "Új felhasználó vagyunk?"}, "rememberEmail": {"message": "Email <PERSON>"}, "loginWithDevice": {"message": "Bejelentkezés eszközzel"}, "fingerprintPhraseHeader": {"message": "Ujjlenyomat <PERSON>"}, "fingerprintMatchInfo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a széf feloldásra került és az Ujjlenyomat kifejezés egyezik a másik eszközön levővel."}, "resendNotification": {"message": "Értesítés újraküldése"}, "viewAllLogInOptions": {"message": "Összes bejelentkezési opció megtekintése"}, "notificationSentDevice": {"message": "Egy értesítés lett elküldve az eszközre."}, "notificationSentDevicePart1": {"message": "A Bitwarden zárolás feloldása az eszközön vagy: "}, "notificationSentDeviceAnchor": {"message": "webalkalmazás"}, "notificationSentDevicePart2": {"message": "Jóváhagyás el<PERSON>tt győződjünk meg arról, hogy az ujjlenyomat kifejezés megegyezik az alábbi kifejezéssel."}, "aNotificationWasSentToYourDevice": {"message": "Egy értesítés lett elküldve az eszközre."}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "A kérelem jóváhagyása után értesítés érkezik."}, "needAnotherOptionV1": {"message": "Másik opció szükséges?"}, "loginInitiated": {"message": "A bejelentkezés elindításra került."}, "logInRequestSent": {"message": "A kérés elküldésre került."}, "exposedMasterPassword": {"message": "Kiszivárgott me<PERSON>jelszó"}, "exposedMasterPasswordDesc": {"message": "A jelszó megtalálható egy adatvédelmi incidensben. A fiók védelméhez használjunk egyedi jelszót. Biztos, hogy kiszivárgott elszót szeretnénk használni?"}, "weakAndExposedMasterPassword": {"message": "Gyenge vagy kitett mesterjelszó"}, "weakAndBreachedMasterPasswordDesc": {"message": "Gyenge jelszó lett azonosítva és megtalálva egy adatvédelmi incidens során. A fók védelme érdekében használjunk erős és egyedi jelszót. Biztosan használni szeretnénk ezt a jelszót?"}, "checkForBreaches": {"message": "Az ehhez a jelszóhoz tartozó ismert adatvédelmi incidensek ellenőrzése"}, "important": {"message": "Fontos:"}, "masterPasswordHint": {"message": "Az elfelejtett mesterjelszó nem állítható helyre, ha elfelejtik!"}, "characterMinimum": {"message": "Legalább $LENGTH$ karakter", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "A szervezeti szabályzat bekapcsolta az automatikus kitöltést az oldalbetöltéskor."}, "howToAutofill": {"message": "Az automatikus kitöltés működése"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "Rendben"}, "autofillSettings": {"message": "Automatikus kitöltés beállítások"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Bullenytűparancsok kezelése"}, "autofillShortcut": {"message": "Automatikus kitöltés billentyűparancs"}, "autofillLoginShortcutNotSet": {"message": "Az automatikus kitöltés billentyűzetparancs nincs beállítva. Módosítsuk ezt a böngésző beállításaiban."}, "autofillLoginShortcutText": {"message": "Az automatikus kitöltés billentyűparancsa: $COMMAND$. Módosítsuk ezt a böngésző beállításaiban.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Alapértelmezett automatikus kitöltési billenytűparancs: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Megnyitás új ablakban"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Emlékezés az eszközre, hogy zökkenőmentes legyen a jövőbeni bejelentkezés"}, "deviceApprovalRequired": {"message": "Az eszköz jóváhagyása szükséges. Válasszunk egy jóváhagyási lehetőséget lentebb:"}, "deviceApprovalRequiredV2": {"message": "Eszköz jóváhagyás szükséges"}, "selectAnApprovalOptionBelow": {"message": "Válasszunk lentebb egy jóváhagyási lehetőséget."}, "rememberThisDevice": {"message": "Eszköz megjegyzése"}, "uncheckIfPublicDevice": {"message": "Töröljük a j<PERSON>, ha nyilvános eszközt használunk."}, "approveFromYourOtherDevice": {"message": "Jóváhagyás másik eszközzel"}, "requestAdminApproval": {"message": "Adminisztrátori jóváhagyás kérés"}, "ssoIdentifierRequired": {"message": "A szervezeti SSO azonosító megadása szükséges."}, "creatingAccountOn": {"message": "Fiók létrehozása:"}, "checkYourEmail": {"message": "Email cím postaláda ellenőrzése"}, "followTheLinkInTheEmailSentTo": {"message": "Kövessük a hivatkozást az elküldött emailben"}, "andContinueCreatingYourAccount": {"message": "és folytassuk a fiók létrehozását."}, "noEmail": {"message": "N<PERSON> email?"}, "goBack": {"message": "<PERSON><PERSON><PERSON>"}, "toEditYourEmailAddress": {"message": "az email cím szerkesztéséhez."}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "A hozzáférés megtagadásra került. Nincs j<PERSON>tság az oldal megtekintésére."}, "general": {"message": "<PERSON><PERSON>lán<PERSON>"}, "display": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "accountSuccessfullyCreated": {"message": "A fiók sikeresen létrehozásra került."}, "adminApprovalRequested": {"message": "Adminisztrátori jóváhagyás kérés tö<PERSON>t"}, "adminApprovalRequestSentToAdmins": {"message": "A kérés elküldésre került az adminisztrátornak."}, "troubleLoggingIn": {"message": "Prob<PERSON><PERSON> van a bejelentkezéssel?"}, "loginApproved": {"message": "A bejelentkezés jóváhagyásra került."}, "userEmailMissing": {"message": "A felhasználói email cím hiányzik."}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Az aktív felhasználói email cím nem található. Jelentkezzünk ki."}, "deviceTrusted": {"message": "Az eszköz megbízható."}, "trustOrganization": {"message": "Bizalmi szervezet"}, "trust": {"message": "Bizalom"}, "doNotTrust": {"message": "<PERSON><PERSON><PERSON> bi<PERSON>"}, "organizationNotTrusted": {"message": "A szervezet nem megbízható."}, "emergencyAccessTrustWarning": {"message": "A fiók biztonság érdekében csak akkor erősítsük meg, ha vészhelyzeti hozzáférést biztosítottunk ehhez a felhasználóhoz és az ujjlenyomata megegyezik a fiókjukban megjelenítettekkel."}, "orgTrustWarning": {"message": "A fiók biztonsága érdekében csak akkor folytassuk, ha tagja vagyunk ennek a szervezetnek, engedé<PERSON><PERSON><PERSON> van a fiók helyreállítása és az alább megjelenített ujjlenyomat megegyezik a szervezet ujjlenyomatával."}, "orgTrustWarning1": {"message": "Ennek a szervezetnek van egy vállalati szabályzata, amely regisztr<PERSON><PERSON> ben<PERSON> a fiók helyreállítási szolgáltatásba. A regisztráció lehetővé teszi a szervezet rendszergazdái számára, hogy megváltoztassák a jelszavunkat. Csak akkor folytassuk, ha felismerjük ezt a szervezetet és az alább megjelenített ujjlenyomat-kifejezés megegyezik a szervezet ujjlenyomatával."}, "trustUser": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sendsNoItemsTitle": {"message": "Nincsenek natív Send elemek.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsNoItemsMessage": {"message": "A Send használatával biztonságosan megoszthatjuk a titkosított információkat bárkivel.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Az adatbevitel kötelező."}, "required": {"message": "kötelező"}, "search": {"message": "Keresés"}, "inputMinLength": {"message": "A bevitel legyen legalább $COUNT$ karakter hosszú.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "A bevitel nem haladhatja meg $COUNT$ karakter hosszt.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "A következő karakterek nem engedélyezettek: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "A beviteli érték legyen legalább $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "A beviteli érték ne haladja meg $MAX$ értéket.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 vagy több email cím érvénytelen."}, "inputTrimValidator": {"message": "A bevitel nem tartalmazhat csak fehér szóköz karaktert.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Az megadott bevitel nem email cím."}, "fieldsNeedAttention": {"message": "$COUNT$ mező fentebb figyelmet érdemel.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 mező igényel figyelmet.."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ mező igényel figyelmet.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Választás --"}, "multiSelectPlaceholder": {"message": "-- Gépelés a szűréshez --"}, "multiSelectLoading": {"message": "Az opciók beolvasása folyamatban can..."}, "multiSelectNotFound": {"message": "<PERSON><PERSON> elem."}, "multiSelectClearAll": {"message": "Összes törlése"}, "plusNMore": {"message": "+ $QUANTITY$ további", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Almenü"}, "toggleCollapse": {"message": "Összezárás váltás", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "A mesterjelszót újra bekérő elemeket nem lehet automatikusan kitölteni az oldal betöltésekor. Az automatikus kitöltés az oldal betöltésekor kikapcsolásra kerül.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Az automatikus kitöltés az oldal betöltésekor az alapértelmezett beállítás használatára lett beállítva.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Kapcsoljuk ki a mesterjelszó újbóli bekérését a mező szerkesztéséhez.", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Ugrás a tartalomra"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden automatikus kitöltési menü", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Unlock your account to view matching logins", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Oldjuk fel a fiók zárolását az automatikus kitöltési javaslatok megtekintéséhez.", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "<PERSON>ók felold<PERSON>", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Oldjuk fel a fiók z<PERSON>t, új ablakban nyílik meg.", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, egysz<PERSON> j<PERSON><PERSON><PERSON> el<PERSON> kód", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "A jelenlegi TOTP lejártáig hátralévő idő", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Töltse kia hitelesítő adatokat", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Részleges felhasználónév", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "Nincsenek megjeleníthető elemek", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "<PERSON><PERSON> elem", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "<PERSON><PERSON>", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "<PERSON><PERSON>", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "<PERSON>j <PERSON><PERSON><PERSON><PERSON> el<PERSON>, új ablakban nyílik meg.", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "<PERSON><PERSON>", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "<PERSON><PERSON> s<PERSON><PERSON><PERSON>, új ablakban nyílik meg.", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "<PERSON><PERSON>", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> elem <PERSON>, új ablakban nyílik meg.", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Bekapcsolás"}, "ignore": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "importData": {"message": "Adatok importálása", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Importálási hiba"}, "importErrorDesc": {"message": "Probléma merült fel az importálni próbált adatokkal. Oldjuk fel a forrásfájlban alább felsorolt hibákat és próbáljuk újra."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Oldjuk fel a hibákat lentebb és próbáljuk újra."}, "description": {"message": "Le<PERSON><PERSON><PERSON>"}, "importSuccess": {"message": "Az adatok sikeresen importálásra kerültek."}, "importSuccessNumberOfItems": {"message": "Összesen $AMOUNT$ elem lett importálva.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Próbáluk <PERSON>"}, "verificationRequiredForActionSetPinToContinue": {"message": "Ehhez a művelethez ellenőrzés szükséges. A folytatáshoz állítsunk be egy PIN kódot."}, "setPin": {"message": "PIN kód be<PERSON>ll<PERSON>a"}, "verifyWithBiometrics": {"message": "Ellenőrzés biometrikusan"}, "awaitingConfirmation": {"message": "Várakozás megerősítésre"}, "couldNotCompleteBiometrics": {"message": "<PERSON><PERSON> a biometrikus adatokat."}, "needADifferentMethod": {"message": "Más módszerre van szükség?"}, "useMasterPassword": {"message": "Mesterjelszó has<PERSON>"}, "usePin": {"message": "PIN kód has<PERSON>"}, "useBiometrics": {"message": "Biometria használata"}, "enterVerificationCodeSentToEmail": {"message": "Az emailben elküldött ellenőrző kód megadása."}, "resendCode": {"message": "<PERSON><PERSON><PERSON>"}, "total": {"message": "Összesen"}, "importWarning": {"message": "Adatokat importálunk $ORGANIZATION$ fájlba. Az adatok megosztásra kerülhetnek a szervezet tagjaival. Folytatni akarjuk?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "A fiókhoz kétlépcsős DUO bejelentkezés szükséges."}, "popoutExtension": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "launchDuo": {"message": "DUO indítása"}, "importFormatError": {"message": "Az adatok nincsenek megfelelően formázva. Ellenőrizzük az importálás fájlt és próbáljuk újra."}, "importNothingError": {"message": "<PERSON><PERSON>i nem lett import<PERSON><PERSON><PERSON>."}, "importEncKeyError": {"message": "Hiba történt az exportált fájl visszafejtése során. A titkosítási kulcs nem egyezik meg az adatok exportálásához használt titkosítási kulccsal."}, "invalidFilePassword": {"message": "A fájl jelszó érvénytelen. Használjuk az exportfájl létrehozásakor megadott j<PERSON>zót."}, "destination": {"message": "<PERSON><PERSON><PERSON>"}, "learnAboutImportOptions": {"message": "Információ az importálási opciókról"}, "selectImportFolder": {"message": "Mappa kiválasztása"}, "selectImportCollection": {"message": "Gyűjtemény kiválasztása"}, "importTargetHint": {"message": "Válasszuk ezt a lehetőséget, ha azt akarjuk, hogy az importált fájl tartalma $DESTINATION$ helyre k<PERSON>ülj<PERSON>n", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "A fájl hozzá nem rendelt elemeket tartalmaz."}, "selectFormat": {"message": "Válasszuk ki az importáló fájl formátumát"}, "selectImportFile": {"message": "Válasszuk ki az import fájlt"}, "chooseFile": {"message": "Fájl kiválasztása"}, "noFileChosen": {"message": "<PERSON><PERSON><PERSON> fájl."}, "orCopyPasteFileContents": {"message": "vagy vágólapon vigyük be fájl tartalmat"}, "instructionsFor": {"message": "$NAME$ utasítások", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>síté<PERSON>"}, "confirmVaultImportDesc": {"message": "Ez a fájl jelszóval védett. Adjuk meg a fájl jelszót az adatok importálásához."}, "confirmFilePassword": {"message": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON>sí<PERSON>"}, "exportSuccess": {"message": "A széfadatok exportálásra kerültek."}, "typePasskey": {"message": "Hozzáférési kulcs"}, "accessing": {"message": "Elérés"}, "loggedInExclamation": {"message": "Megtörtént a bejelentkezés."}, "passkeyNotCopied": {"message": "A hozzáférési kulcs nem kerül másolásra."}, "passkeyNotCopiedAlert": {"message": "A hozzáférési kulcs nem kerül másolásra a klónozott elembe. Folytatjuk ennek az elemnek a klónozását?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "A kezdeményező hely által megkövetelt ellenőrzés. Ez a szolgáltatás még nincs megvalósítva mesterjelszó nélküli fiókok esetén."}, "logInWithPasskeyQuestion": {"message": "Bejelentkezés hozzáférési kulccsal?"}, "passkeyAlreadyExists": {"message": "Az alkalmazáshoz már létezik hozzáférési kulcs."}, "noPasskeysFoundForThisApplication": {"message": "Az alkalmazáshoz nem található hozzáférési kulcs."}, "noMatchingPasskeyLogin": {"message": "<PERSON><PERSON><PERSON> meg<PERSON><PERSON><PERSON>ő bejelentkezés ehhez a webhelyhez."}, "noMatchingLoginsForSite": {"message": "Nincsenek egyező bejelentkezések ehhez a webhelyhez."}, "searchSavePasskeyNewLogin": {"message": "Keressés vagy a belépőkulcs mentése bejelentkezésként."}, "confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "savePasskey": {"message": "Hozzáférési kulcs mentése"}, "savePasskeyNewLogin": {"message": "Hozzáférési kulcs mentése új bejelentkezésként"}, "chooseCipherForPasskeySave": {"message": "Bejelentkezés választás a hozzáférési kulcs mentéséhez"}, "chooseCipherForPasskeyAuth": {"message": "Hozzáférési kulcs választás a bejelentkezéshez"}, "passkeyItem": {"message": "Hozzáférési kulcs elem"}, "overwritePasskey": {"message": "Bejelentkezési kulcs felülírása?"}, "overwritePasskeyAlert": {"message": "Ez az elem már tartalmaz egy hozzáférési kulcsot. Biztosan felülírásra kerüljön az aktuális hozzáférési kulcs?"}, "featureNotSupported": {"message": "<PERSON><PERSON>"}, "yourPasskeyIsLocked": {"message": "A hozzáférési kulcs használatához hitelesítés szükséges. A személyazonosság ellenőrzése szükséges a folytatáshoz."}, "multifactorAuthenticationCancelled": {"message": "A többtényezős hitelesítés megszakításra került."}, "noLastPassDataFound": {"message": "<PERSON><PERSON> LastPass adat."}, "incorrectUsernameOrPassword": {"message": "Helytelen f<PERSON>ználónév vagy j<PERSON>"}, "incorrectPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "incorrectCode": {"message": "<PERSON><PERSON><PERSON><PERSON> kód"}, "incorrectPin": {"message": "Helytelen PIN kód"}, "multifactorAuthenticationFailed": {"message": "A többtényezős hitelesítés sikertelen volt."}, "includeSharedFolders": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> is"}, "lastPassEmail": {"message": "LastPass email cím"}, "importingYourAccount": {"message": "A iók importálása folyamatban van..."}, "lastPassMFARequired": {"message": "LastPass többtényezős hitelesítés szükséges"}, "lastPassMFADesc": {"message": "Adjuk meg az egyszeri jelszót a hitelesítő alkalmazásból"}, "lastPassOOBDesc": {"message": "Hagyjuk jóvá a bejelentkezési kérést a hitelesítő alkalmazásban vagy írjunk be egy egyszeri jelszót."}, "passcode": {"message": "Je<PERSON><PERSON><PERSON>"}, "lastPassMasterPassword": {"message": "LastPass mesterjelszó"}, "lastPassAuthRequired": {"message": "LastPass hitelesítés szükséges"}, "awaitingSSO": {"message": "Várakozás SSO hitelesítésre"}, "awaitingSSODesc": {"message": "Folytassuk a bejelentkezést a cég hitelesítő adataival."}, "seeDetailedInstructions": {"message": "Tekintsük meg a részletes utasításokat a súgó oldalon:", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Importálás közvetlenül a LastPassból"}, "importFromCSV": {"message": "Importálás CSV-ből"}, "lastPassTryAgainCheckEmail": {"message": "Próbál<PERSON>k újra,vagy keressünk egy emailt a LastPasstól a személyazonosság igazolásához."}, "collection": {"message": "Gyűjtemény"}, "lastPassYubikeyDesc": {"message": "Illesszük be a LastPass-fiókhoz társított YubiKey eszközt a számítógép USB portjába, majd érintsük meg annak gombját."}, "switchAccount": {"message": "Fiók váltása"}, "switchAccounts": {"message": "Fiókok váltása"}, "switchToAccount": {"message": "Váltás fiókra"}, "activeAccount": {"message": "Aktív fiók"}, "bitwardenAccount": {"message": "Bitwarden fiók"}, "availableAccounts": {"message": "Elérhető fiókok"}, "accountLimitReached": {"message": "A fiók korlát elérésre került. Jelentkezzünk ki egy fiókból egy másik hozzáadásához."}, "active": {"message": "aktív"}, "locked": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unlocked": {"message": "felo<PERSON><PERSON>"}, "server": {"message": "szerver"}, "hostedAt": {"message": "t<PERSON><PERSON><PERSON>:"}, "useDeviceOrHardwareKey": {"message": "Saját eszköz vagy hardverkulcs használata"}, "justOnce": {"message": "Csak egyszer"}, "alwaysForThisSite": {"message": "En<PERSON><PERSON><PERSON> a <PERSON><PERSON><PERSON><PERSON><PERSON> mindig"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ bekerült a kizárt tartományokba.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Általános form<PERSON>", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Legyen a Bitwarden az alapértelmezett jelszókezelő?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ennek az opciónak a figyelmen kívül hagyása ütközést okozhat a Bitwarden automatikus kitöltési menü és a böngésző között.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Legyen a Bitwarden az alapértelmezett jelszókezelő", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "A Bitwarden nem állítható be alapértelmezett jelszó<PERSON>t.", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "Ki kell o<PERSON>tani a böngésző adatvédelmi jogosultságait a Bitwardennek az alapértelmezett jelszókezelőként történő beállításhoz.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "<PERSON><PERSON><PERSON>", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "A hitelesítések sikeresen mentésre kerültek.", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "A jelszó mentésre került!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "A hitelesítések sikeresen frissítésre kerültek.", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "A jelszó frissítésre került!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Hiba történt a hitelesítések mentésekor.  A részletekért ellenőrizzük a konzolt.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "<PERSON><PERSON><PERSON>"}, "removePasskey": {"message": "<PERSON><PERSON><PERSON><PERSON> eltávolítása"}, "passkeyRemoved": {"message": "A jelszó eltávolításra került."}, "autofillSuggestions": {"message": "Automatikus kitöltés javaslatok"}, "itemSuggestions": {"message": "Javasolt elemek"}, "autofillSuggestionsTip": {"message": "A bejelentkezési elem mentése ehhez a webhelyhez az automatikus kitöltéshez"}, "yourVaultIsEmpty": {"message": "A széf üres."}, "noItemsMatchSearch": {"message": "Nincsenek a keresésnek megfelelő elemek."}, "clearFiltersOrTryAnother": {"message": "Töröljük a szűrőket vagy próbálkozzunk másik keresési kifejezéssel."}, "copyInfoTitle": {"message": "Infó másolása - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Jegyzet másolása - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "To<PERSON><PERSON><PERSON><PERSON> opci<PERSON>k, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "Tová<PERSON>i opciók - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "<PERSON><PERSON> - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "Elem megtekintése - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Automatikus kitöltés - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Automatikus kitöltés - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "$FIELD$, $VALUE$ másolása", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "<PERSON><PERSON><PERSON>ek másolandó értékek."}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "<PERSON><PERSON> cím m<PERSON>"}, "copyPhone": {"message": "Telefonszám másolása"}, "copyAddress": {"message": "<PERSON><PERSON>m <PERSON>"}, "adminConsole": {"message": "Adminisztrátori konzol"}, "accountSecurity": {"message": "Fiókbiztonság"}, "notifications": {"message": "Értesítések"}, "appearance": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "errorAssigningTargetCollection": {"message": "Hiba történt a célgyűjtemény hozzárendelése során."}, "errorAssigningTargetFolder": {"message": "Hiba történt a célmappa hozzárendelése során."}, "viewItemsIn": {"message": "$NAME$ elemek megtekintése", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Vissza: $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "<PERSON><PERSON>"}, "removeItem": {"message": "$NAME$ eltávolítása", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "<PERSON><PERSON>"}, "itemName": {"message": "Elem neve"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selfOwnershipLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> magam", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "itemHistory": {"message": "<PERSON><PERSON>"}, "lastEdited": {"message": "Utoljára szerkesztve"}, "ownerYou": {"message": "Tulajdonos: <PERSON>n"}, "linked": {"message": "Csatolva"}, "copySuccessful": {"message": "A másolás sikeres volt."}, "upload": {"message": "Feltöltés"}, "addAttachment": {"message": "Melléklet hozzáadása"}, "maxFileSizeSansPunctuation": {"message": "A naximális fájlméret 500 MB."}, "deleteAttachmentName": {"message": "$NAME$ melléklet törlése", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "$NAME$ letöltése", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Bitwarden letöltése"}, "downloadBitwardenOnAllDevices": {"message": "Bitwarden letöltése minden eszközön"}, "getTheMobileApp": {"message": "Mobilalkalmazás beszerzése"}, "getTheMobileAppDesc": {"message": "Útközben is hozzáférhetünk a jelszabkhoz a Bitwarden mobilalkalmazással."}, "getTheDesktopApp": {"message": "Asztali alkalmazás beszerzése"}, "getTheDesktopAppDesc": {"message": "Hozzáférhetünk a széfhez böng<PERSON><PERSON><PERSON> né<PERSON>l, majd <PERSON> be a feloldást biometrikus adatokkal, hogy felgyorsítsuk a feloldást mind az asztali alkal<PERSON>ban, mind a böngésző bővítményben."}, "downloadFromBitwardenNow": {"message": "Letöltés most a bitwarden.com webhelyről"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Biztosan véglegesen törlésre kerüljön ez a mellé<PERSON>t?"}, "premium": {"message": "Prémium"}, "freeOrgsCannotUseAttachments": {"message": "Az ingyenes szervezetek nem használhatnak mellékleteket."}, "filters": {"message": "Szűrők"}, "filterVault": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "filterApplied": {"message": "Egy szűrő került alkalmazásra."}, "filterAppliedPlural": {"message": "$COUNT$ szűrő került alkalmazásra.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "identification": {"message": "Azonosítás"}, "contactInfo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> infó"}, "downloadAttachment": {"message": "Letöltés - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "kártyaszám végződés:", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Bejelentkezési hitelesítések"}, "authenticatorKey": {"message": "Hitelesítő kulcs"}, "autofillOptions": {"message": "Automatikus kitöltés opciók"}, "websiteUri": {"message": "<PERSON><PERSON><PERSON> (URI)"}, "websiteUriCount": {"message": "Webhely (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "A webhely hozzáad<PERSON>ra került."}, "addWebsite": {"message": "<PERSON><PERSON><PERSON>"}, "deleteWebsite": {"message": "<PERSON><PERSON><PERSON>"}, "defaultLabel": {"message": "Alapértelmezés ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "$WEBSITE$ egyező érzékelés megjelenítése", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "$WEBSITE$ egyező érzékelés elrejtése", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Automatikus kitöltés oldalbetöltésnél?"}, "cardExpiredTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "cardExpiredMessage": {"message": "Ha megújí<PERSON>, frissítsük a kártya adatait."}, "cardDetails": {"message": "Kártyaadatok"}, "cardBrandDetails": {"message": "$BRAND$ adatok", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Animációk engedélyezése"}, "showAnimations": {"message": "Animációk megjelenítése"}, "addAccount": {"message": "Fiók hozzáadása"}, "loading": {"message": "A betöltés folyamatban van."}, "data": {"message": "Ada<PERSON>"}, "passkeys": {"message": "Hozzáférési kul<PERSON>ok", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Jelszavak", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Bejelentkezés hozzáférési kulccsal", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Csak az ezekhez a gyűjteményekhez hozzáféréssel rendelkező szervezeti tagok láthatják az elemet."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Csak az ezekhez a gyűjteményekhez hozzáféréssel rendelkező szervezeti tagok láthatják az elemeket."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Mező hozzáadása"}, "add": {"message": "Hozzáadás"}, "fieldType": {"message": "Mezőtípus"}, "fieldLabel": {"message": "Mezőfelirat"}, "textHelpText": {"message": "Szövegmezők használata olyan adatokhoz mint a biztonsági kérdések"}, "hiddenHelpText": {"message": "Rejtett mezők használata olyan érzékeny adatokhoz mint a jelszó"}, "checkBoxHelpText": {"message": "Jelölődobozok használata, ha automatikusan ki szeretnénk tölteni olyan űrlap jelölődobozt mint az email cím megjegyzése"}, "linkedHelpText": {"message": "Csatolt mez<PERSON>, ha egy adott webhely automatikus kitöltésével kapcsolatos problémákat tapasztalunk."}, "linkedLabelHelpText": {"message": "Adjuk meg a mező html a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON>, aria címk<PERSON>j<PERSON><PERSON> vagy <PERSON>."}, "editField": {"message": "Mező szerkesztése"}, "editFieldLabel": {"message": "$LABEL$ szerkesztése", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "$LABEL$ törlése", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ hozzáadásra került.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "$LABEL$ átrendezése. A nyílbillentyűkkel mozgassuk az elemet felfelé vagy le<PERSON>.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "A webhely URI átrendezése. A nyílbillentyűkkel mozgassuk az elemet felfelé vagy le<PERSON>."}, "reorderFieldUp": {"message": "$LABEL$ feljebb került, $INDEX$/$LENGTH$ pozícióba", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ lejjebb került, $INDEX$/$LENGTH$ pozícióba", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "<PERSON><PERSON>"}, "fileSend": {"message": "Fájl típus<PERSON>"}, "fileSends": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "textSend": {"message": "Szöveg típusú Send"}, "textSends": {"message": "Szöveg küldés"}, "accountActions": {"message": "Fiókműveletek"}, "showNumberOfAutofillSuggestions": {"message": "Az automatikus bejelentkezési kitöltési javaslatok számának megjelenítése a bővítmény ikonján"}, "showQuickCopyActions": {"message": "Gyors másolási műveletek megjelenítése a Széfen"}, "systemDefault": {"message": "<PERSON><PERSON><PERSON> alapértelmezett"}, "enterprisePolicyRequirementsApplied": {"message": "Erre a beállításra a vállalkozás rendszabály követelmények lettek alkalmazva."}, "sshPrivateKey": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sshPublicKey": {"message": "Nyilván<PERSON> k<PERSON>"}, "sshFingerprint": {"message": "Ujjlenyomat"}, "sshKeyAlgorithm": {"message": "Kulcs típusa"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Újra"}, "vaultCustomTimeoutMinimum": {"message": "A minimális egyedi időkifutás 1 perc."}, "additionalContentAvailable": {"message": "Kiegészítő tartalom érhető el."}, "fileSavedToDevice": {"message": "A fájl mentésre került az eszközre. Kezeljük az eszközről a letöltéseket."}, "showCharacterCount": {"message": "Karakterszá<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "hideCharacterCount": {"message": "Karakterszá<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "itemsInTrash": {"message": "elem van a lo<PERSON>tárban."}, "noItemsInTrash": {"message": "<PERSON><PERSON><PERSON> elem a lomtárban."}, "noItemsInTrashDesc": {"message": "A törölt elemek itt jelennek meg és 30 nap elteltével véglegesen törlődnek."}, "trashWarning": {"message": "A 30 napnál régebben lomtárba került elemek automatikusan törlésre kerülnek."}, "restore": {"message": "Visszaállítás"}, "deleteForever": {"message": "Végleges törlés"}, "noEditPermissions": {"message": "<PERSON><PERSON><PERSON> ezen elem szerkesztéséheu."}, "biometricsStatusHelptextUnlockNeeded": {"message": "A biometrikus feloldás nem érhető el, mert először PIN kóddal vagy j<PERSON>zó<PERSON> kell feloldani."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "A biometrikus feloldás jelenleg nem érhető el."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "A biometrikus feloldás nem érhető el a rosszul konfigurált rendszerfájlok miatt."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "A biometrikus feloldás nem érhető el a rosszul konfigurált rendszerfájlok miatt."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "A biometrikus feloldás nem érhető el, mert a Bitwarden asztali alkalmazás be van z<PERSON>rva."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "A biometrikus feloldás nem érhető el, mert nincs engedélyezve $EMAIL$ számára a Bitwarden asztali alkalmazásban.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "A biometrikus feloldás jelenleg ismeretlen okból nem érhető el."}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "<PERSON>r<PERSON><PERSON> j<PERSON>", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "A jelszó generálásra került.", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Mentés a Bitwardenbe", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Szóköz", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON> karakter", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick karakter", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Felkiáltójel", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "@ karakter", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "# karakter", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "$ karakter", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "% karakter", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "^ karakter", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "& karakter", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "* karakter", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "( karakter", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": ") karakter", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "_ karakter", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "- karak<PERSON>", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "+ karakter", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "= karakter", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "{ karakter", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "} karakter", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "[ karakter", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "] karakter", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "| karakter", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "\\ karakter", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": ": karakter", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "; karakter", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "\" karakter", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "' <PERSON><PERSON>ter", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "< karakter", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "> karakter", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": ", karakter", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": ". karakter", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "? karakter", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "/ karakter", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Kisbetű"}, "uppercaseAriaLabel": {"message": "Nagybetű"}, "generatedPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "compactMode": {"message": "Komp<PERSON><PERSON> mód"}, "beta": {"message": "<PERSON><PERSON><PERSON>"}, "extensionWidth": {"message": "Kiterjesztés szélesség"}, "wide": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "extraWide": {"message": "Extra széles"}, "sshKeyWrongPassword": {"message": "A megadott j<PERSON>ó he<PERSON>en."}, "importSshKey": {"message": "Importálás"}, "confirmSshKeyPassword": {"message": "<PERSON><PERSON><PERSON>ó megerősítése"}, "enterSshKeyPasswordDesc": {"message": "Adjuk meg az SSH kulcs jelszót."}, "enterSshKeyPassword": {"message": "Jelszó megadása"}, "invalidSshKey": {"message": "Az SSH kulcs érvénytelen."}, "sshKeyTypeUnsupported": {"message": "Az SSH kulcstípus nem támogatott."}, "importSshKeyFromClipboard": {"message": "Kulcs importálása vágólapból"}, "sshKeyImported": {"message": "Az SSH kulcs sikeresen importálásra került."}, "cannotRemoveViewOnlyCollections": {"message": "Nem távolíthatók el a csak megtekintési engedéllyel bíró gyűjtemények: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Frissítsük az asztali alkalmazást."}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "A biometrikus feloldás használatához frissítsük az asztali alkalmazást vagy tiltsuk le az ujjlenyomatos feloldást az asztali beállításokban."}, "changeAtRiskPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>ó megváltoztatása"}, "settingsVaultOptions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "emptyVaultDescription": {"message": "A széf többre alkalmas, mint a jelszavak mentése. Menthetünk belépéseket, azonosítókat, kártyákat és feljegyzéseket teljes biztonságban."}, "introCarouselLabel": {"message": "Üdvözlet a Bitwardenben"}, "securityPrioritized": {"message": "Biztonság, prioritásos"}, "securityPrioritizedBody": {"message": "Mentsük el a bejelentkezési adatokat, kártyákat és azonosításokat a biztonságos széfbe. A Bitwarden tudás né<PERSON>, végpontok közötti titkosítást használ a felhasználók számára fontos dolgok védelmére."}, "quickLogin": {"message": "Gyors és könnyű bejelentkezés"}, "quickLoginBody": {"message": "Állítsuk be a biometrikus feloldást és az automatikus kitöltést, hogy egyetlen betű beírása nélkül jelentkezzünk be a fiókokba."}, "secureUser": {"message": "A bejelentkezések magasabb szintre emelése"}, "secureUserBody": {"message": "Használjuk a gener<PERSON><PERSON><PERSON>, hogy <PERSON>, egyedi j<PERSON>zavakat hozzunk létre és mentsük az összes fióknál."}, "secureDevices": {"message": "<PERSON><PERSON><PERSON><PERSON>, mi<PERSON> ho<PERSON> van rá szükség"}, "secureDevicesBody": {"message": "Mentsünk el a korlátlan jelszót korlátlan számú eszközön a Bitwarden mobil, böngésző és asztali alkalmazásokkal."}, "emptyVaultNudgeTitle": {"message": "Létező jelszavak importálása"}, "emptyVaultNudgeBody": {"message": "Az import<PERSON><PERSON><PERSON>val gyorsan átvihetünk bejelentkezéseket a Bitwardenbe anélkül, hogy manu<PERSON><PERSON><PERSON> hozzáadnánk azokat."}, "emptyVaultNudgeButton": {"message": "Importálás most"}, "hasItemsVaultNudgeTitle": {"message": "Üdvözlet a széfben!"}, "hasItemsVaultNudgeBody": {"message": "Az aktuális oldal elemeinek automatikus kitöltése\nKedvenc elemek a könnyű hozzáférés érdekében\nValami más keresése a széfben"}, "newLoginNudgeTitle": {"message": "<PERSON><PERSON><PERSON>akarítás automatikus kitöltéssel"}, "newLoginNudgeBodyOne": {"message": "Bevonás:", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "<PERSON><PERSON><PERSON>", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "így ez a bejelentkezés automatikus kitöltési javaslatként jelenik meg.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Zökkenőmentes online fizetés"}, "newCardNudgeBody": {"message": "Kártyákkal könnyedén, biztonságosan és pontosan tölthetjük ki automatikusan a fizetési űrlapokat."}, "newIdentityNudgeTitle": {"message": "Egyszerűsítsük a fiókok létrehozását"}, "newIdentityNudgeBody": {"message": "Azonosítókkal gyorsan automatikusan kitölthetjük a hosszú regisztrációs vagy ka<PERSON>olatfelvételi űrlapokat."}, "newNoteNudgeTitle": {"message": "Tartsuk biztonságban az érzékeny adatokat"}, "newNoteNudgeBody": {"message": "Jegyzetekkel biztonságosan tárolhatjuk az érzékeny adatokat, például a banki vagy biztosítási adatokat."}, "newSshNudgeTitle": {"message": "Fejlesztőbarát SSH hozzáférés"}, "newSshNudgeBodyOne": {"message": "Tároljuk el a kulcsokat és csatlakozzunk az SSH ügynökhöz a gyors, titkosított hitelesítéshez.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "További információ az SSH ügynökről", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}}