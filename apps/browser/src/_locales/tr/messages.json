{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logosu"}, "extName": {"message": "Bitwarden Parol<PERSON>", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "Bitwarden tüm parolalarınızı, geçiş anahtarlarınızı ve hassas bilgilerinizi güvenle saklar", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "Güvenli kasanıza ulaşmak için giriş yapın veya yeni bir hesap oluşturun."}, "inviteAccepted": {"message": "<PERSON><PERSON> kabul edildi"}, "createAccount": {"message": "<PERSON><PERSON><PERSON>"}, "newToBitwarden": {"message": "Bitwarden'da yeni misiniz?"}, "logInWithPasskey": {"message": "Geçiş anahtarıyla giriş yap"}, "useSingleSignOn": {"message": "Çoklu oturum açma kullan"}, "welcomeBack": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setAStrongPassword": {"message": "Güçlü bir parola belirleyin"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Parolanızı belirleyerek hesabınızı oluşturmayı tamamlayın"}, "enterpriseSingleSignOn": {"message": "Kurumsal tek oturum açma (SSO)"}, "cancel": {"message": "İptal"}, "close": {"message": "Ka<PERSON><PERSON>"}, "submit": {"message": "<PERSON><PERSON><PERSON>"}, "emailAddress": {"message": "E-posta adresi"}, "masterPass": {"message": "<PERSON>"}, "masterPassDesc": {"message": "<PERSON> parola, ka<PERSON><PERSON><PERSON> ulaşmak için kullanacağınız paroladır. Ana parolanızı unutmamanız çok önemlidir. Unutursanız parolalarınızı asla kurtaramazsınız."}, "masterPassHintDesc": {"message": "Ana parolanızı unutursanız bu ipucuna bakınca size ana parolanızı hatırlatacak bir şey yazabilirsiniz."}, "masterPassHintText": {"message": "Parolanızı unutursanız parola ipucunuzu e-posta adresinize gönderebiliriz. Maksimum $CURRENT$/$MAXIMUM$ karakter.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Ana parolayı tekrar yazın"}, "masterPassHint": {"message": "<PERSON> parola i<PERSON> (isteğe bağlı)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Ku<PERSON><PERSON>şa katıl"}, "joinOrganizationName": {"message": "$ORGANIZATIONNAME$ kuruluşuna katıl", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Kuruluşa katılmayı tamamlamak için ana parolanızı belirleyin."}, "tab": {"message": "Sekme"}, "vault": {"message": "<PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON><PERSON>"}, "allVaults": {"message": "<PERSON><PERSON><PERSON>"}, "tools": {"message": "Araçlar"}, "settings": {"message": "<PERSON><PERSON><PERSON>"}, "currentTab": {"message": "Geç<PERSON><PERSON> sekme"}, "copyPassword": {"message": "Parolayı kopyala"}, "copyPassphrase": {"message": "Parolayı kopyala"}, "copyNote": {"message": "<PERSON>u k<PERSON>ala"}, "copyUri": {"message": "URI'yi <PERSON>"}, "copyUsername": {"message": "Kullanıcı adını kopyala"}, "copyNumber": {"message": "Numarayı kopyala"}, "copySecurityCode": {"message": "Güvenlik kodunu kopyala"}, "copyName": {"message": "Adı k<PERSON>ala"}, "copyCompany": {"message": "Şirketi kopyala"}, "copySSN": {"message": "Sosyal güvenlik numarasını kopyala"}, "copyPassportNumber": {"message": "Pasaport numarasını kopyala"}, "copyLicenseNumber": {"message": "Ruhsat numarasını kopyala"}, "copyPrivateKey": {"message": "<PERSON><PERSON> k<PERSON>"}, "copyPublicKey": {"message": "Ortak anahtarı kopyala"}, "copyFingerprint": {"message": "Parmak izini kopyala"}, "copyCustomField": {"message": "$FIELD$ alanını kopyala", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Web sitesini kopyala"}, "copyNotes": {"message": "Notları kopyala"}, "copy": {"message": "Kopyala", "description": "Copy to clipboard"}, "fill": {"message": "<PERSON><PERSON><PERSON>", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Otomatik doldur"}, "autoFillLogin": {"message": "Hesabı otomatik doldur"}, "autoFillCard": {"message": "Kartı otomatik doldur"}, "autoFillIdentity": {"message": "Kimliği otomatik doldur"}, "fillVerificationCode": {"message": "Doğ<PERSON>lama kodunu doldur"}, "fillVerificationCodeAria": {"message": "Doğ<PERSON>lama kodunu doldur", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON><PERSON><PERSON> (ve kopyala)"}, "copyElementIdentifier": {"message": "<PERSON><PERSON> alan adı<PERSON> k<PERSON>"}, "noMatchingLogins": {"message": "Eşleşen hesap yok"}, "noCards": {"message": "Kart yok"}, "noIdentities": {"message": "Kimlik yok"}, "addLoginMenu": {"message": "<PERSON><PERSON><PERSON> e<PERSON>"}, "addCardMenu": {"message": "<PERSON>rt e<PERSON>"}, "addIdentityMenu": {"message": "<PERSON><PERSON>"}, "unlockVaultMenu": {"message": "Kasanızın kilidini açın"}, "loginToVaultMenu": {"message": "Kasanıza giriş ya<PERSON>ın"}, "autoFillInfo": {"message": "Mevcut sekme için otomatik doldurulabilecek hesap yok."}, "addLogin": {"message": "<PERSON><PERSON><PERSON> e<PERSON>"}, "addItem": {"message": "<PERSON><PERSON><PERSON> <PERSON>"}, "accountEmail": {"message": "Hesap e-posta adresi"}, "requestHint": {"message": "İpucunu iste"}, "requestPasswordHint": {"message": "<PERSON>rola i<PERSON> iste"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Hesabınızın e-posta adresini girdiğinizde parola ipucunuz size gönderilecektir"}, "getMasterPasswordHint": {"message": "Ana parola ipucunu al"}, "continue": {"message": "<PERSON><PERSON>"}, "sendVerificationCode": {"message": "E-posta adresime doğrulama kodu gönder"}, "sendCode": {"message": "<PERSON><PERSON>"}, "codeSent": {"message": "<PERSON><PERSON>"}, "verificationCode": {"message": "Doğrulama kodu"}, "confirmIdentity": {"message": "<PERSON>am etmek için kimliğinizi doğrulayın."}, "changeMasterPassword": {"message": "Ana parolayı değiştir"}, "continueToWebApp": {"message": "Web uygulamasına devam edilsin mi?"}, "continueToWebAppDesc": {"message": "Web uygulamasında Bitwarden hesabınızın diğer özelliklerini keşfedin."}, "continueToHelpCenter": {"message": "<PERSON><PERSON>m merkezine gitmek ister misiniz?"}, "continueToHelpCenterDesc": {"message": "Yardım merkezinde Bitwarden kullanımı hakkında daha fazla bilgi alabilirsiniz."}, "continueToBrowserExtensionStore": {"message": "Tarayıcınızın uzantı sitesine gitmek ister misiniz?"}, "continueToBrowserExtensionStoreDesc": {"message": "Bitwarden'ı başkalarına da tanımak ister misiniz? Tarayıcınızın uzantı mağazasını ziyaret edip Bitwarden'ı değerlendirin."}, "changeMasterPasswordOnWebConfirmation": {"message": "Ana parolanızı Bitwarden web uygulamasında değiştirebilirsiniz."}, "fingerprintPhrase": {"message": "Parmak izi ifadesi", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Hesabınızın parmak izi ifadesi", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "İki aşamalı giriş"}, "logOut": {"message": "Çıkış yap"}, "aboutBitwarden": {"message": "Bitwarden hakkında"}, "about": {"message": "Hakkında"}, "moreFromBitwarden": {"message": "Diğer Bitwarden ürünleri"}, "continueToBitwardenDotCom": {"message": "bitwarden.com'a gitmek ister misiniz?"}, "bitwardenForBusiness": {"message": "İşletmeler için <PERSON>"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden Authenticator, kimlik doğrulama anahtarlarınızı saklama ve 2 aşamalı doğrulama akışları için TOTP anahtarı üretme imkânı sağlar. Bilgi almak için bitwarden.com sitesini ziyaret edin"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Securely store, manage, and share developer secrets with Bitwarden Secrets Manager. Learn more on the bitwarden.com website."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Create smooth and secure login experiences free from traditional passwords with Passwordless.dev. Learn more on the bitwarden.com website."}, "freeBitwardenFamilies": {"message": "Ücretsiz Bitwarden Aile"}, "freeBitwardenFamiliesPageDesc": {"message": "You are eligible for Free Bitwarden Families. Redeem this offer today in the web app."}, "version": {"message": "S<PERSON>r<PERSON><PERSON>"}, "save": {"message": "<PERSON><PERSON>"}, "move": {"message": "Taşı"}, "addFolder": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "name": {"message": "Ad"}, "editFolder": {"message": "Klasörü <PERSON>"}, "editFolderWithName": {"message": "Klasörü düzenle: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "<PERSON><PERSON>"}, "folderName": {"message": "Klasör adı"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "<PERSON><PERSON>"}, "createFoldersToOrganize": {"message": "Kasanızdaki kayıtları organize etmek için klasörler oluşturun"}, "deleteFolderPermanently": {"message": "Bu klasörü kalıcı olarak silmek istediğinizden emin misiniz?"}, "deleteFolder": {"message": "Klasörü sil"}, "folders": {"message": "Klasörler"}, "noFolders": {"message": "Listelenecek klasör yok."}, "helpFeedback": {"message": "<PERSON><PERSON>m ve geribildirim"}, "helpCenter": {"message": "Bitwarden yardım merkezi"}, "communityForums": {"message": "Bitwarden forumlarını keşfedin"}, "contactSupport": {"message": "Bitwarden destek ekibiyle iletişime geçin"}, "sync": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "syncVaultNow": {"message": "Kasayı şimdi eşitle"}, "lastSync": {"message": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>:"}, "passGen": {"message": "<PERSON><PERSON><PERSON>"}, "generator": {"message": "Üreteç", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Hesaplarınız için otomatik olarak güçlü, özgün parolalar oluşturun."}, "bitWebVaultApp": {"message": "Bitwarden web uygulaması"}, "importItems": {"message": "Hesapları içe aktar"}, "select": {"message": "Seç"}, "generatePassword": {"message": "<PERSON><PERSON>a <PERSON>"}, "generatePassphrase": {"message": "<PERSON><PERSON><PERSON>"}, "passwordGenerated": {"message": "<PERSON><PERSON><PERSON>"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Kullanıcı adı üretildi"}, "emailGenerated": {"message": "E-posta üretildi"}, "regeneratePassword": {"message": "<PERSON><PERSON> par<PERSON> o<PERSON>"}, "options": {"message": "Seçenekler"}, "length": {"message": "Uzunluk"}, "include": {"message": "<PERSON><PERSON> et", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Büyük harfleri dahil et", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Küçük harfleri dahil et", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Rakamları dahil et", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "<PERSON><PERSON> ka<PERSON>i dahil et", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "<PERSON><PERSON><PERSON> say<PERSON>"}, "wordSeparator": {"message": "<PERSON><PERSON><PERSON> ayracı"}, "capitalize": {"message": "Baş harfleri büyük yap", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "<PERSON><PERSON><PERSON> e<PERSON>"}, "minNumbers": {"message": "En az rakam"}, "minSpecial": {"message": "En az özel karakter"}, "avoidAmbiguous": {"message": "Okurken karışabilecek karakterleri kullanma", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Üreteç seçeneklerinize kurumsal ilke gereksinimleri uygulandı.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "<PERSON><PERSON><PERSON> ara"}, "edit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "view": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "noItemsInList": {"message": "Listelenecek kayıt yok."}, "itemInformation": {"message": "<PERSON><PERSON><PERSON>"}, "username": {"message": "Kullanıcı adı"}, "password": {"message": "Pa<PERSON><PERSON>"}, "totp": {"message": "Kimlik doğrulama sırrı"}, "passphrase": {"message": "Uzun söz"}, "favorite": {"message": "<PERSON><PERSON><PERSON>"}, "unfavorite": {"message": "Favorilerden kaldır"}, "itemAddedToFavorites": {"message": "<PERSON><PERSON><PERSON> favorilere e<PERSON>"}, "itemRemovedFromFavorites": {"message": "<PERSON><PERSON><PERSON> silindi"}, "notes": {"message": "Notlar"}, "privateNote": {"message": "<PERSON><PERSON> not"}, "note": {"message": "Not"}, "editItem": {"message": "Kaydı düzenle"}, "folder": {"message": "Klasör"}, "deleteItem": {"message": "Kaydı sil"}, "viewItem": {"message": "Kaydı göster"}, "launch": {"message": "Aç"}, "launchWebsite": {"message": "Web sitesini aç"}, "launchWebsiteName": {"message": "$ITEMNAME$ sitesini aç", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Web sitesi"}, "toggleVisibility": {"message": "Görünürlüğünü aç/kapat"}, "manage": {"message": "<PERSON><PERSON><PERSON>"}, "other": {"message": "<PERSON><PERSON><PERSON>"}, "unlockMethods": {"message": "<PERSON><PERSON> a<PERSON>"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Kasa zaman aşımı eyleminizi değiştirmek için kilit açma yönteminizi ayarlayın."}, "unlockMethodNeeded": {"message": "Ayarlar'da bir kilit açma yöntemi ayarlayın"}, "sessionTimeoutHeader": {"message": "Oturum zaman aşımı"}, "vaultTimeoutHeader": {"message": "<PERSON><PERSON> zaman aşımı"}, "otherOptions": {"message": "<PERSON><PERSON><PERSON>"}, "rateExtension": {"message": "Uzantıyı değerlendirin"}, "browserNotSupportClipboard": {"message": "Web tarayıcınız panoya kopyalamayı desteklemiyor. Parolayı elle kopyalayın."}, "verifyYourIdentity": {"message": "Kimliğinizi doğrulayın"}, "weDontRecognizeThisDevice": {"message": "Bu cihazı tanıyamadık. Kimliğinizi doğrulamak için e-postanıza gönderilen kodu girin."}, "continueLoggingIn": {"message": "<PERSON><PERSON><PERSON> devam et"}, "yourVaultIsLocked": {"message": "Kasanız kilitli. Devam etmek için kimliğinizi doğrulayın."}, "yourVaultIsLockedV2": {"message": "Kasan<PERSON>z kilitli"}, "yourAccountIsLocked": {"message": "Hesabınız kilitlendi"}, "or": {"message": "veya"}, "unlock": {"message": "<PERSON><PERSON><PERSON>"}, "loggedInAsOn": {"message": "$HOSTNAME$ üzerinde $EMAIL$ adresiyle oturum açtınız.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Geçersiz ana parola"}, "vaultTimeout": {"message": "<PERSON><PERSON> zaman aşımı"}, "vaultTimeout1": {"message": "Zaman aşımı"}, "lockNow": {"message": "Şimdi kilitle"}, "lockAll": {"message": "Tümünü kilitle"}, "immediately": {"message": "<PERSON><PERSON>"}, "tenSeconds": {"message": "10 saniye"}, "twentySeconds": {"message": "20 saniye"}, "thirtySeconds": {"message": "30 saniye"}, "oneMinute": {"message": "1 dakika"}, "twoMinutes": {"message": "2 dakika"}, "fiveMinutes": {"message": "5 dakika"}, "fifteenMinutes": {"message": "15 dakika"}, "thirtyMinutes": {"message": "30 dakika"}, "oneHour": {"message": "1 saat"}, "fourHours": {"message": "4 saat"}, "onLocked": {"message": "Sistem kilitlenince"}, "onRestart": {"message": "Tarayıcı yeniden başlatılınca"}, "never": {"message": "<PERSON><PERSON>"}, "security": {"message": "Güvenlik"}, "confirmMasterPassword": {"message": "Ana parolayı onaylayın"}, "masterPassword": {"message": "<PERSON>"}, "masterPassImportant": {"message": "Ana parolanızı unutursanız kurtaramazsınız!"}, "masterPassHintLabel": {"message": "<PERSON> parola i<PERSON>"}, "errorOccurred": {"message": "<PERSON>ir hata o<PERSON>"}, "emailRequired": {"message": "E-posta adresi gereklidir."}, "invalidEmail": {"message": "Geçersiz e-posta adresi."}, "masterPasswordRequired": {"message": "<PERSON> parola gere<PERSON>."}, "confirmMasterPasswordRequired": {"message": "Ana parolayı yeniden yazmalısınız."}, "masterPasswordMinlength": {"message": "Ana parola en az $VALUE$ karakter uzunluğunda olmalıdır.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "<PERSON> parola onayı eşleşmiyor."}, "newAccountCreated": {"message": "Yeni hesabınız oluşturuldu! Şimdi giriş yapabilirsiniz."}, "newAccountCreated2": {"message": "<PERSON><PERSON> he<PERSON>ı<PERSON><PERSON><PERSON> oluşturuldu."}, "youHaveBeenLoggedIn": {"message": "Oturum açtınız."}, "youSuccessfullyLoggedIn": {"message": "Başarıyla giriş yaptı<PERSON>ız"}, "youMayCloseThisWindow": {"message": "<PERSON>u pencer<PERSON>i kapa<PERSON>"}, "masterPassSent": {"message": "Size ana parolanızın ipucunu içeren bir e-posta gönderdik."}, "verificationCodeRequired": {"message": "Doğrulama kodu gereklidir."}, "webauthnCancelOrTimeout": {"message": "Kimlik doğrulama iptal edildi ve çok uzun sürdü. Lütfen yeniden deneyin."}, "invalidVerificationCode": {"message": "Geçersiz doğrulama kodu"}, "valueCopied": {"message": "$VALUE$ kopyalandı", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Seçilen kayıt bu sayfada otomatik olarak doldurulamadı. Lütfen bilgileri elle kopyalayıp yapıştırın."}, "totpCaptureError": {"message": "Mevcut web sayfasındaki QR kodu taranamıyor"}, "totpCaptureSuccess": {"message": "Kimlik doğrulama anahtarı eklendi"}, "totpCapture": {"message": "Mevcut web sayfasındaki kimlik doğrulayıcı QR kodunu tarayın"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Kimlik doğrulayıcılar hakkında bilgi alın"}, "copyTOTP": {"message": "Kimlik doğrulama anahtarını kopyala (TOTP)"}, "loggedOut": {"message": "Çıkış yapıldı"}, "loggedOutDesc": {"message": "Hesabınızdan çıkış yapıldı."}, "loginExpired": {"message": "Oturumunuz zaman aşımına uğradı."}, "logIn": {"message": "<PERSON><PERSON><PERSON> yap"}, "logInToBitwarden": {"message": "Bitwarden'a giriş yapın"}, "enterTheCodeSentToYourEmail": {"message": "E-posta adresinize gönderilen kodu girin"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Kimlik doğrulama uygulamanızdaki kodu girin"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "G<PERSON>şi tamamlamak için aşağıdaki adımları izleyin."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Güvenlik anahtarınızla girişi tamamlamak için aşağıdaki adımları izleyin."}, "restartRegistration": {"message": "<PERSON><PERSON>ı yeniden başlat"}, "expiredLink": {"message": "Bağlantının süresi dolmuş"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Lütfen kaydı yeniden başlatın veya giriş yapmayı deneyin."}, "youMayAlreadyHaveAnAccount": {"message": "Zaten hesabınız olabilir"}, "logOutConfirmation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> yapmak istediğinize emin misiniz?"}, "yes": {"message": "<PERSON><PERSON>"}, "no": {"message": "Hay<PERSON><PERSON>"}, "location": {"message": "<PERSON><PERSON>"}, "unexpectedError": {"message": "Beklenmedik bir hata <PERSON>."}, "nameRequired": {"message": "Ad gereklidir."}, "addedFolder": {"message": "Klas<PERSON>r <PERSON>"}, "twoStepLoginConfirmation": {"message": "İki aşamalı giriş, hesabınıza girererken işlemi bir güvenlik anahtarı, şifrematik uygulaması, SMS, telefon araması veya e-posta gibi ek bir yöntemle doğrulamanızı isteyerek hesabınızın güvenliğini artırır. İki aşamalı giriş özelliğini bitwarden.com web kasası üzerinden etkinleştirebilirsiniz. Şimdi siteye gitmek ister misiniz?"}, "twoStepLoginConfirmationContent": {"message": "Bitwarden web uygulamasında iki aşamalı girişi ayarlayarak hesabınızın güvenliğini artırabilirsiniz."}, "twoStepLoginConfirmationTitle": {"message": "Web uygulamasına devam edilsin mi?"}, "editedFolder": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteFolderConfirmation": {"message": "Bu klasörü silmek istediğinizden emin misiniz?"}, "deletedFolder": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "gettingStartedTutorial": {"message": "Başlangıç rehberi"}, "gettingStartedTutorialVideo": {"message": "Tarayıcı uzantımızdan en iyi şekilde yararlanmayı öğrenmek için başlangıç eğitimimizi izleyebilirsiniz."}, "syncingComplete": {"message": "Eşitleme <PERSON>ı"}, "syncingFailed": {"message": "Eşitleme başarısız"}, "passwordCopied": {"message": "Parola kopyalandı"}, "uri": {"message": "URl"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "<PERSON>ni <PERSON>"}, "addDomain": {"message": "<PERSON> ad<PERSON>", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "Kayıt eklendi"}, "editedItem": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>"}, "deleteItemConfirmation": {"message": "Çöp kutusuna göndermek istediğinizden emin misiniz?"}, "deletedItem": {"message": "Kayıt çöp kutusuna gönderildi"}, "overwritePassword": {"message": "Parolanın üzerine yaz"}, "overwritePasswordConfirmation": {"message": "Mevcut parolanın üzerine kaydetmek istediğinize emin misiniz?"}, "overwriteUsername": {"message": "Kullanıcı adının üzerine yaz"}, "overwriteUsernameConfirmation": {"message": "Kullanıcı adının üzerine kaydetmek istediğinizden emin misiniz?"}, "searchFolder": {"message": "Klasörde ara"}, "searchCollection": {"message": "Koleksiyonda ara"}, "searchType": {"message": "<PERSON><PERSON>"}, "noneFolder": {"message": "Klasör yok", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "vaultSaveOptionsTitle": {"message": "<PERSON><PERSON><PERSON>"}, "addLoginNotificationDesc": {"message": "\"Hesap ekle\" bi<PERSON><PERSON><PERSON>, ilk kez kullandığınız hesap bilgilerini kasanıza kaydetmek isteyip istemediğinizi otomatik olarak sorar."}, "addLoginNotificationDescAlt": {"message": "Kasanızda bulunmayan kayıtların eklenmesini isteyip istemediğinizi sorar. Oturum açmış tüm hesaplar için geçerlidir."}, "showCardsInVaultViewV2": {"message": "<PERSON><PERSON> g<PERSON>ünümünde kartları her zaman otomatik doldurma önerisi olarak göster"}, "showCardsCurrentTab": {"message": "Sekme sayfasında kartları göster"}, "showCardsCurrentTabDesc": {"message": "<PERSON><PERSON> otomatik doldurma için sekme sayfasında kartları listele."}, "showIdentitiesInVaultViewV2": {"message": "<PERSON><PERSON> g<PERSON>ümünde kimlikleri her zaman otomatik doldurma önerisi olarak göster"}, "showIdentitiesCurrentTab": {"message": "<PERSON><PERSON>me sayfa<PERSON>ında kimlikleri göster"}, "showIdentitiesCurrentTabDesc": {"message": "<PERSON><PERSON> otomatik doldurma için sekme sayfasında kimlikleri listele."}, "clickToAutofillOnVault": {"message": "Kasa görünümünde kayıtlara tıklayınca otomatik doldur"}, "clickToAutofill": {"message": "Otomatik doldurma önerisindeki kayıtlara tıkladığımda doldur"}, "clearClipboard": {"message": "<PERSON><PERSON><PERSON> te<PERSON>", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Ko<PERSON>alanan değerleri otomatik olarak panodan sil.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Bitwarden bu parolayı sizin için hatırlasın mı?"}, "notificationAddSave": {"message": "<PERSON><PERSON>"}, "notificationViewAria": {"message": "$ITEMNAME$ kaydını görüntüle. <PERSON><PERSON> pencerede açılır", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationEditTooltip": {"message": "Kaydetmeden önce düzenle", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "<PERSON><PERSON> bi<PERSON>"}, "labelWithNotification": {"message": "$LABEL$: <PERSON>ni bildirim", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "loginSaveConfirmation": {"message": "$ITEMNAME$ Bitwarden'a kaydedildi.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is saved."}, "loginUpdatedConfirmation": {"message": "$ITEMNAME$ Bitward<PERSON>'da gü<PERSON><PERSON>di.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is updated."}, "saveAsNewLoginAction": {"message": "<PERSON><PERSON> hesap o<PERSON>ak ka<PERSON>et", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Hesabı güncelle", "description": "Button text for updating an existing login entry."}, "saveLogin": {"message": "Hesabı kaydet", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Mevcut hesabı gü<PERSON>lle", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON><PERSON>", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "<PERSON><PERSON><PERSON>", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Bu hesabı kaydedemedik. Bilgileri elle girmeyi deneyin.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Mevcut hesapları güncellemeyi öner"}, "changedPasswordNotificationDesc": {"message": "Sitede bir değişiklik tespit edildiğinde hesap parolasını güncellemeyi öner."}, "changedPasswordNotificationDescAlt": {"message": "Bir sitede değişiklik tespit edildiğinde hesap parolasını güncellemek isteyip istemediğiniz sorar. Oturum açmış tüm hesaplar için geçerlidir."}, "enableUsePasskeys": {"message": "Geçiş anahtarlarını kaydet ve kullan"}, "usePasskeysDesc": {"message": "Yeni geçiş anahtarlarını kaydetmek ve kasanızda saklanan geçiş anahtarlarıyla giriş yapmak için sor. <PERSON><PERSON><PERSON> yapmış tüm hesaplar için geçerlidir."}, "notificationChangeDesc": {"message": "Bu parolayı Bitwarden'da güncellemek ister misiniz?"}, "notificationChangeSave": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationUnlockDesc": {"message": "Otomatik doldurma isteğini tamamlamak için Bitwarden kasanızın kilidini açın."}, "notificationUnlock": {"message": "<PERSON><PERSON><PERSON>"}, "additionalOptions": {"message": "Ek seç<PERSON>kler"}, "enableContextMenuItem": {"message": "Bağlam menüsü seçeneklerini göster"}, "contextMenuItemDesc": {"message": "Parola oluşturmak ve eşleşen hesaplara ulaşmak için sağ tıklamayı kullan."}, "contextMenuItemDescAlt": {"message": "Web sitesi için parola oluşturmak ve eşleşen hesap bilgilerine erişmek için sağ tıklamayı kullanabilirsiniz. Oturum açmış tüm hesaplar için geçerlidir."}, "defaultUriMatchDetection": {"message": "Varsayılan URI eşleşme tespiti", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Otomatik doldurma gibi eylemler gerçekleştirilirken hesaplar için URI eşleşme tespitinin nasıl yapılacağını seçin."}, "theme": {"message": "<PERSON><PERSON>"}, "themeDesc": {"message": "Uygulamanın renk temasını değiştir."}, "themeDescAlt": {"message": "Uygulamanın renk temasını değiştirir. Oturum açmış tüm hesaplar için geçerlidir."}, "dark": {"message": "<PERSON><PERSON>", "description": "Dark color"}, "light": {"message": "Açık", "description": "Light color"}, "exportFrom": {"message": "Dışa aktarılacak konum"}, "exportVault": {"message": "Kasayı dışa aktar"}, "fileFormat": {"message": "<PERSON><PERSON><PERSON>"}, "fileEncryptedExportWarningDesc": {"message": "Dışarı aktarılan bu dosya parola korumalı olacak ve dosyanın çözülmesi için parolayı girmeniz gerekecek."}, "filePassword": {"message": "<PERSON><PERSON><PERSON>"}, "exportPasswordDescription": {"message": "Bu parola, bu dosyayı dışa ve içe aktarmak için kullanılacaktır"}, "accountRestrictedOptionDescription": {"message": "Dışa aktarmayı şifrelemek ve içe aktarmayı yalnızca mevcut Bitwarden hesabıyla kısıtlamak için, hesabınızın kullanıcı adı ve ana parolasından türetilen hesap şifreleme anahtarınızı kullanın."}, "passwordProtectedOptionDescription": {"message": "Dışa aktardığınız dosyayı şifrelemek ve bir Bitwarden hesabına içe aktarmak için kullanacağınız parolayı belirleyin."}, "exportTypeHeading": {"message": "Dışa aktarma türü"}, "accountRestricted": {"message": "<PERSON><PERSON>p k<PERSON>"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "\"Dosya parolası\" ile \"Dosya parolasını onaylayın\" eşleşmiyor."}, "warning": {"message": "UYARI", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Uyarı", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Kasayı dışa aktarmayı onaylayın"}, "exportWarningDesc": {"message": "Dışa aktarılan dosyadaki verileriniz şifrelenmemiş olacak. Bu dosyayı güvensiz yö<PERSON>mlerle (örn. e-posta) göndermemeli ve saklamamalısınız. İşiniz bittikten sonra dosyayı hemen silin."}, "encExportKeyWarningDesc": {"message": "Dışa aktardığınız bu dosyadaki verileriniz, hesabınızın şifreleme anahtarıyla şifrelenir. Hesabınızın şifreleme anahtarını değiştirirseniz bu dosyanın şifresi çözülemez hale gelir, dolayısıyla dosyayı yeniden dışa aktarmanız gerekir."}, "encExportAccountWarningDesc": {"message": "Her Bitwarden kullanıcı hesabının hesap şifreleme anahtarları farklıdır. <PERSON><PERSON> bir dışa aktarmayı farklı bir hesapta içe aktaramazsınız."}, "exportMasterPassword": {"message": "Kasadaki verilerinizi dışa aktarmak için ana parolanızı girin."}, "shared": {"message": "Paylaşılan"}, "bitwardenForBusinessPageDesc": {"message": "İşletmeler için Bitwarden'ı kullanarak kasanızdaki kayıtları başkalarıyla paylaşabilirsiniz. Daha fazla bilgi için bitwarden.com sitesini ziyaret edin."}, "moveToOrganization": {"message": "Kuruluşa taşı"}, "movedItemToOrg": {"message": "$ITEMNAME$ $ORGNAME$ kuruluşuna taşındı", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Bu kaydı taşımak istediğiniz kuruluşu seçin. Taşıdığınız kaydın sahipliği seçtiğiniz kuruluşa aktarılacak. Artık bu kaydın doğrudan sahibi olmayacaksınız."}, "learnMore": {"message": "Daha fazla bilgi al"}, "authenticatorKeyTotp": {"message": "Kimlik doğrulama anahtarı (TOTP)"}, "verificationCodeTotp": {"message": "Doğrulama kodu (TOTP)"}, "copyVerificationCode": {"message": "Doğ<PERSON>lama kodunu kopyala"}, "attachments": {"message": "Ekler"}, "deleteAttachment": {"message": "<PERSON><PERSON> sil"}, "deleteAttachmentConfirmation": {"message": "Bu eki silmek istediğinize emin mi<PERSON>?"}, "deletedAttachment": {"message": "<PERSON><PERSON>"}, "newAttachment": {"message": "<PERSON><PERSON> e<PERSON>"}, "noAttachments": {"message": "Ek yok."}, "attachmentSaved": {"message": "<PERSON><PERSON><PERSON>"}, "file": {"message": "<PERSON><PERSON><PERSON>"}, "fileToShare": {"message": "Paylaşılacak dosya"}, "selectFile": {"message": "<PERSON><PERSON> <PERSON><PERSON> se<PERSON>"}, "maxFileSize": {"message": "<PERSON><PERSON><PERSON><PERSON> dosya boyutu 500 MB'dir."}, "featureUnavailable": {"message": "Özellik kullanılamıyor"}, "encryptionKeyMigrationRequired": {"message": "Şifreleme anahtarınızın güncellenmesi gerekiyor. Şifreleme anahtarınızı güncellemek için lütfen web kasasına giriş yapın."}, "premiumMembership": {"message": "Premium üyelik"}, "premiumManage": {"message": "Üyeliği yönet"}, "premiumManageAlert": {"message": "Üyeliğinizi bitwarden.com web kasası üzerinden yönetebilirsiniz. Şimdi siteye gitmek ister misiniz?"}, "premiumRefresh": {"message": "Üyeliği yenile"}, "premiumNotCurrentMember": {"message": "Şu anda premium üye değilsiniz."}, "premiumSignUpAndGet": {"message": "Premium üye olarak sahip olacağınız avantajlar:"}, "ppremiumSignUpStorage": {"message": "Dosya ekleri için 1 GB şifrelenmiş depolama."}, "premiumSignUpEmergency": {"message": "Acil durum erişimi."}, "premiumSignUpTwoStepOptions": {"message": "YubiKey ve Duo gibi marka bazlı iki aşamalı giriş <PERSON>ri."}, "ppremiumSignUpReports": {"message": "Kasanızı güvende tutmak için parola hijyeni, <PERSON><PERSON><PERSON> ve veri ihlali raporları."}, "ppremiumSignUpTotp": {"message": "Kasanızdaki hesaplar için TOTP doğrulama kodu (2FA) oluşturucu."}, "ppremiumSignUpSupport": {"message": "Öncelikli müşteri desteği."}, "ppremiumSignUpFuture": {"message": "Ve ileride duyuracağımız tüm premium özellikler. Daha fazlası yakında!"}, "premiumPurchase": {"message": "Premium satın al"}, "premiumPurchaseAlertV2": {"message": "Bitwarden web uygulamasındaki hesap ayarlarınızdan Premium abonelik satın alabilirsiniz."}, "premiumCurrentMember": {"message": "Premium üyesiniz!"}, "premiumCurrentMemberThanks": {"message": "Bitwarden'ı desteklediğiniz için teşekkür ederiz."}, "premiumFeatures": {"message": "Premium'a geçmenin <PERSON>:"}, "premiumPrice": {"message": "Bunların hepsi sadece yılda $PRICE$!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "Bunların hepsi yılda sadece $PRICE$!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "enableAutoTotpCopy": {"message": "TOTP'yi otomatik kopyala"}, "disableAutoTotpCopyDesc": {"message": "Hesabınıza bağlı bir kimlik doğrulama anahtarı varsa giriş bilgilerini otomatik olarak doldurduğunuzda TOTP doğrulama kodu da otomatik olarak panonuza kopyalanır."}, "enableAutoBiometricsPrompt": {"message": "Açılışta biyometri doğrulaması iste"}, "premiumRequired": {"message": "Premium gerekli"}, "premiumRequiredDesc": {"message": "Bu özelliği kullanmak için premium üyelik gereklidir."}, "authenticationTimeout": {"message": "Kimlik doğrulama zaman aşımı"}, "authenticationSessionTimedOut": {"message": "Kimlik doğrulama oturumu zaman aşımına uğradı. Lütfen giriş sürecini yeniden başlatın."}, "verificationCodeEmailSent": {"message": "Doğrulama e-postası $EMAIL$ adresine gönderildi.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Bu cihazda 30 gün boyunca sorma"}, "selectAnotherMethod": {"message": "Başka bir yöntem seç", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "<PERSON><PERSON> kodu kullan"}, "insertU2f": {"message": "Güvenlik anahtarınızı bilgisayarınızın USB portuna takın. Düğmesi varsa dokunun."}, "openInNewTab": {"message": "<PERSON><PERSON> sek<PERSON> aç"}, "webAuthnAuthenticate": {"message": "WebAutn ile doğrula"}, "readSecurityKey": {"message": "Güvenlik anahtarını oku"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "<PERSON><PERSON><PERSON>ıyor"}, "noTwoStepProviders": {"message": "Bu hesapta iki aşamalı giriş açık ama yapılandırdığınız iki aşamalı giriş sağlayıcılarının hiçbiri bu web tarayıcısını desteklemiyor."}, "noTwoStepProviders2": {"message": "Lütfen desteklenen bir web tarayıcısı (örn. Chrome) kullanın ve/veya web tarayıcılarında daha iyi desteklenen sağlayıcılar (örn. kimlik doğrulama uygulaması) ekleyin."}, "twoStepOptions": {"message": "İki aşamalı giriş <PERSON>"}, "selectTwoStepLoginMethod": {"message": "İki aşamalı giriş yöntemini seçin"}, "recoveryCodeDesc": {"message": "İki aşamalı doğrulama sağlayıcılarınıza ulaşamıyor musunuz? Kurtarma kodunuzu kullanarak hesabınızdaki tüm iki aşamalı giriş sağlayıcılarını devre dışı bırakabilirsiniz."}, "recoveryCodeTitle": {"message": "<PERSON><PERSON> kodu"}, "authenticatorAppTitle": {"message": "Kimlik doğrulama uygulaması"}, "authenticatorAppDescV2": {"message": "Kimlik doğrulama uygulamanızın (örn. Bitwarden Authenticator) ürettiği kodu girin.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "YubiKey OTP güvenlik anahtarı"}, "yubiKeyDesc": {"message": "Hesabınıza erişmek için bir YubiKey kullanın. YubiKey 4, 4 Nan<PERSON>, 4C ve NEO cihazlarıyla çalışır."}, "duoDescV2": {"message": "Duo Security'nin <PERSON> kodu girin.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Kuruluşunuzun Duo Security doğrulaması için Duo Mobile uygulaması, SMS, telefon araması veya U2F güvenlik anahtarını kullanın.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Hesabınıza erişmek için WebAuthn uyumlu bir güvenlik anahtarı kullanın."}, "emailTitle": {"message": "E-posta"}, "emailDescV2": {"message": "E-posta adresinize gönderilen kodu girin."}, "selfHostedEnvironment": {"message": "Şirket içinde barındırılan ortam"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "İleri dü<PERSON>y ya<PERSON> için her hizmetin taban URL'sini bağımsız olarak belirleyebilirsiniz."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "<PERSON><PERSON> or<PERSON>"}, "baseUrl": {"message": "Sunucu URL'si"}, "selfHostBaseUrl": {"message": "<PERSON><PERSON> kendine barındırılan sunucu URL'si", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API sunucu URL'si"}, "webVaultUrl": {"message": "Web kasası sunucu URL'si"}, "identityUrl": {"message": "<PERSON><PERSON> sunucusu URL'si"}, "notificationsUrl": {"message": "<PERSON><PERSON><PERSON><PERSON> sunucusu URL'si"}, "iconsUrl": {"message": "Simge sunucusu URL'si"}, "environmentSaved": {"message": "Ortam URL'leri ka<PERSON>"}, "showAutoFillMenuOnFormFields": {"message": "Form alanlarında otomatik doldurma menüsünü göster", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Otomatik doldurma önerileri"}, "showInlineMenuLabel": {"message": "Form alanlarında otomatik doldurma önerilerini göster"}, "showInlineMenuIdentitiesLabel": {"message": "Kimlikleri öneri olarak gö<PERSON>"}, "showInlineMenuCardsLabel": {"message": "Kartları öneri olarak göster"}, "showInlineMenuOnIconSelectionLabel": {"message": "Simge seçildiğinde önerileri göster"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Oturum açmış tüm hesaplara uygulanır."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Çakışmaları önlemek için tarayıcınızın yerleşik şifre yöneticisi ayarlarını kapatın."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Tarayıcı ayarlarını düzenleyin."}, "autofillOverlayVisibilityOff": {"message": "<PERSON><PERSON><PERSON>", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "<PERSON>", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "Otomatik doldurma simgesi seçildiğinde", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Sayfa yüklenince otomatik doldur"}, "enableAutoFillOnPageLoad": {"message": "Sayfa yüklenince otomatik doldur"}, "enableAutoFillOnPageLoadDesc": {"message": "Say<PERSON> y<PERSON>nince giriş formu tespit edilirse otomatik olarak formu doldur."}, "experimentalFeature": {"message": "Ele geçirilmiş veya güvenilmeyen web siteleri sayfa yüklenirken otomatik doldurmayı suistimal edebilir."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Riskleri öğrenin"}, "learnMoreAboutAutofill": {"message": "Otomatik doldurma hakkında bilgi alın"}, "defaultAutoFillOnPageLoad": {"message": "Hesaplar i<PERSON>in <PERSON>lan otomatik doldurma ayarı"}, "defaultAutoFillOnPageLoadDesc": {"message": "\"Sayfa yüklenince otomatik doldur\"u her hesabın \"Düzenle\" görünümünden ayrı ayrı kapatabilirsiniz."}, "itemAutoFillOnPageLoad": {"message": "Sayfa yüklenince otomatik doldur (Seçeneklerde ayarlanmışsa)"}, "autoFillOnPageLoadUseDefault": {"message": "Varsayılan ayarı kullan"}, "autoFillOnPageLoadYes": {"message": "Sayfa yüklenince otomatik doldur"}, "autoFillOnPageLoadNo": {"message": "Sayfa yüklenince otomatik doldurma"}, "commandOpenPopup": {"message": "Kasayı açılır pencerede aç"}, "commandOpenSidebar": {"message": "Kasayı kenar çubuğunda aç"}, "commandAutofillLoginDesc": {"message": "Geçerli site için son kull<PERSON>ı<PERSON> hesabı otomatik doldur"}, "commandAutofillCardDesc": {"message": "Geçerli site için son kullanılan kartı otomatik doldur"}, "commandAutofillIdentityDesc": {"message": "Geçerli site iç<PERSON> son kullanılan kimliği otomatik doldur"}, "commandGeneratePasswordDesc": {"message": "Rast<PERSON><PERSON> yeni bir parola oluş<PERSON> ve panoya kopyala"}, "commandLockVaultDesc": {"message": "Kasayı kilitle"}, "customFields": {"message": "<PERSON><PERSON>"}, "copyValue": {"message": "<PERSON><PERSON><PERSON>"}, "value": {"message": "<PERSON><PERSON><PERSON>"}, "newCustomField": {"message": "<PERSON><PERSON>"}, "dragToSort": {"message": "Sıralamak için <PERSON>"}, "dragToReorder": {"message": "Sıralamak için <PERSON>"}, "cfTypeText": {"message": "<PERSON><PERSON>"}, "cfTypeHidden": {"message": "<PERSON><PERSON><PERSON>"}, "cfTypeBoolean": {"message": "Boolean"}, "cfTypeCheckbox": {"message": "<PERSON><PERSON> kut<PERSON>u"}, "cfTypeLinked": {"message": "Bağlantılı", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Bağlı değer", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Doğrulama kodunuzu alacağınız e-postayı kontrol etmek için bu pencerenin dışında bir yere tıklarsanız bu pencere kapanacaktır. Bu pencerenin kapanmaması için yeni bir pencerede açmak ister misiniz?"}, "popupU2fCloseMessage": {"message": "Bu tarayıcı bu açılır pencerede U2F isteklerini işleyemiyor. U2F kullanarak giriş yapmak için bu açılır pencereyi yeni bir pencerede açmak ister misiniz?"}, "enableFavicon": {"message": "Web sitesi simgelerini göster"}, "faviconDesc": {"message": "Hesapların yanında tanıdık görseller göster."}, "faviconDescAlt": {"message": "Her hesabın yanında tanınabilir bir resim göster. Oturum açmış tüm hesaplar için geçerlidir."}, "enableBadgeCounter": {"message": "Rozet sayacını göster"}, "badgeCounterDesc": {"message": "Geçerli web sayfasına ait kaç tane hesabınız olduğunu gösterir."}, "cardholderName": {"message": "Kart sahibinin adı"}, "number": {"message": "<PERSON><PERSON><PERSON>"}, "brand": {"message": "<PERSON><PERSON>"}, "expirationMonth": {"message": "<PERSON> kullanma ayı"}, "expirationYear": {"message": "<PERSON> kullanma yılı"}, "expiration": {"message": "<PERSON> kullanma tarihi"}, "january": {"message": "Ocak"}, "february": {"message": "Ş<PERSON><PERSON>"}, "march": {"message": "Mart"}, "april": {"message": "<PERSON><PERSON>"}, "may": {"message": "May"}, "june": {"message": "Haziran"}, "july": {"message": "Temmuz"}, "august": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "september": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "october": {"message": "<PERSON><PERSON>"}, "november": {"message": "Kasım"}, "december": {"message": "Aralık"}, "securityCode": {"message": "Güvenlik kodu"}, "ex": {"message": "örn."}, "title": {"message": "<PERSON><PERSON>"}, "mr": {"message": "Bay"}, "mrs": {"message": "Mrs"}, "ms": {"message": "Ms"}, "dr": {"message": "Dr"}, "mx": {"message": "Mx"}, "firstName": {"message": "Ad"}, "middleName": {"message": "İkin<PERSON> ad"}, "lastName": {"message": "Soyadı"}, "fullName": {"message": "Adı soyadı"}, "identityName": {"message": "Kimlik adı"}, "company": {"message": "Şirket"}, "ssn": {"message": "Sosyal güvenlik numarası"}, "passportNumber": {"message": "Pasaport numarası"}, "licenseNumber": {"message": "Ehliyet numarası"}, "email": {"message": "E-posta"}, "phone": {"message": "Telefon"}, "address": {"message": "<PERSON><PERSON>"}, "address1": {"message": "Adres 1"}, "address2": {"message": "Adres 2"}, "address3": {"message": "Adres 3"}, "cityTown": {"message": "İlçe"}, "stateProvince": {"message": "İl / eyalet"}, "zipPostalCode": {"message": "Posta kodu"}, "country": {"message": "<PERSON><PERSON><PERSON>"}, "type": {"message": "<PERSON><PERSON><PERSON>"}, "typeLogin": {"message": "<PERSON><PERSON><PERSON>"}, "typeLogins": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeSecureNote": {"message": "<PERSON><PERSON><PERSON><PERSON> not"}, "typeCard": {"message": "Kart"}, "typeIdentity": {"message": "<PERSON><PERSON>"}, "typeSshKey": {"message": "SSH anahtarı"}, "newItemHeader": {"message": "Yeni $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "$TYPE$ düzenle", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "$TYPE$ bilgileri", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "Parola geçmişi"}, "generatorHistory": {"message": "Üreteç geçmişi"}, "clearGeneratorHistoryTitle": {"message": "Üreteç geçmişini temizle"}, "cleargGeneratorHistoryDescription": {"message": "Devam ederseniz üreteç geçmişindeki tüm kayıtlar kalıcı olarak silinecektir. Devam etmek istediğinizden emin misiniz?"}, "back": {"message": "<PERSON><PERSON>"}, "collections": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nCollections": {"message": "$COUNT$ koleksiyon", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popOutNewWindow": {"message": "<PERSON><PERSON> pencerede aç"}, "refresh": {"message": "<PERSON><PERSON><PERSON>"}, "cards": {"message": "<PERSON><PERSON><PERSON>"}, "identities": {"message": "<PERSON><PERSON><PERSON>"}, "logins": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "secureNotes": {"message": "<PERSON><PERSON><PERSON><PERSON>lar"}, "sshKeys": {"message": "SSH anahtarları"}, "clear": {"message": "<PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "Parolanız ele geçirilip geçirilmediğini kontrol edin."}, "passwordExposed": {"message": "<PERSON><PERSON> parola, veri ihlallerinde $VALUE$ kere açığa çıkmış. Değiştirmenizi tavsiye ederiz.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Bilinen veri ihlallerinde bu parola bulunamadı. Güvenle kullanabilirsiniz."}, "baseDomain": {"message": "<PERSON> alan adı", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "<PERSON> (önerilen)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "<PERSON>", "description": "Domain name. Ex. website.com"}, "host": {"message": "<PERSON><PERSON><PERSON>", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Tam"}, "startsWith": {"message": "URI başlangıcı"}, "regEx": {"message": "Düzenli ifade", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Eşleşme tespiti", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Varsayılan eşleşme tespiti", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Seçenekleri aç/kapat"}, "toggleCurrentUris": {"message": "Geçerli URI'leri aç/kapat", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "Geçerli URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Kuruluş", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "allItems": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>"}, "noPasswordsInList": {"message": "Listelenecek parola yok."}, "clearHistory": {"message": "Geçmişi temizle"}, "nothingToShow": {"message": "Gösterilecek bir şey yok"}, "nothingGeneratedRecently": {"message": "Ya<PERSON><PERSON>n zamanda herhangi bir şey üretmediniz"}, "remove": {"message": "Kaldır"}, "default": {"message": "Varsayılan"}, "dateUpdated": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "Oluşturma", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "\"Asla\" seçeneğini kullanmak istediğinizden emin misiniz? <PERSON><PERSON> seçeneğ<PERSON> \"Asla\" olarak ayarlarsanız kasanızın şifreleme anahtarı cihazınızda saklanacaktır. Bu seçeneği kullanırsanız cihazınızı çok iyi korumalısınız."}, "noOrganizationsList": {"message": "Herhangi bir kuruluşa dahil değilsiniz. <PERSON><PERSON><PERSON><PERSON><PERSON>, kayıtlarınızı diğer kullanıcılarla güvenli bir <PERSON><PERSON>ilde <PERSON>laşmanıza olanak verir."}, "noCollectionsInList": {"message": "Listelenecek koleksiyon yok."}, "ownership": {"message": "Sahip"}, "whoOwnsThisItem": {"message": "Bu öğenin sahibi kim?"}, "strong": {"message": "Güçlü", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "İyi", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> ana parola"}, "weakMasterPasswordDesc": {"message": "Seçtiğiniz ana parola zayıf. Bitwarden hesabınızı korumak için daha güçlü bir ana parola seçmenizi öneririz. Bu ana parolayı kullanmak istediğinizden emin misiniz?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Kilidi PIN koduyla aç"}, "setYourPinTitle": {"message": "P<PERSON> belirle"}, "setYourPinButton": {"message": "P<PERSON> belirle"}, "setYourPinCode": {"message": "Bitwarden'ı açarken kullanacağınız PIN kodunu belirleyin. Uygulamadan tamamen çıkış yaparsanız PIN ayarlarınız sıfırlanacaktır."}, "setYourPinCode1": {"message": "Bitwarden'ın kilidini açmak için ana parolanız yerine PIN'iniz kullanılacaktır. Bitwarden'dan tamamen çıkış yaparsanız PIN'iniz sıfırlanır."}, "pinRequired": {"message": "PIN kodu gerekli."}, "invalidPin": {"message": "PIN kodu geçersiz."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Çok fazla geçersiz PIN girişi denemesi. Oturum kapatılıyor."}, "unlockWithBiometrics": {"message": "Kilidi biyometri ile aç"}, "unlockWithMasterPassword": {"message": "<PERSON>lidi ana parola ile aç"}, "awaitDesktop": {"message": "Masaüstünden onay bekleniyor"}, "awaitDesktopDesc": {"message": "Tarayıcıda biyometriyi etkinleştirmek için lütfen Bitwarden masaüstü uygulamasında biyometri kullanımını onaylayın."}, "lockWithMasterPassOnRestart": {"message": "Tarayıcı yeniden başlatıldığında ana parola ile kilitle"}, "lockWithMasterPassOnRestart1": {"message": "Tarayıcı yeniden başlatıldığında ana parolayı sor"}, "selectOneCollection": {"message": "En az bir koleksiyon seçmelisiniz."}, "cloneItem": {"message": "Kaydı klonla"}, "clone": {"message": "<PERSON><PERSON><PERSON>"}, "passwordGenerator": {"message": "<PERSON><PERSON><PERSON>"}, "usernameGenerator": {"message": "Kullanıcı adı üreteci"}, "useThisEmail": {"message": "Bu e-postayı kullan"}, "useThisPassword": {"message": "<PERSON>u parolayı kullan"}, "useThisUsername": {"message": "Bu kullanıcı adını kullan"}, "securePasswordGenerated": {"message": "Güvenli parola üretildi. Web sitesindeki parolanızı da güncellemeyi unutmayın."}, "useGeneratorHelpTextPartOne": {"message": "Güçlü ve benzersiz bir parola üretmek için", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "üreteci k<PERSON>bilirsiniz", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "<PERSON><PERSON>"}, "vaultTimeoutAction": {"message": "<PERSON><PERSON> zaman aşımı e<PERSON>mi"}, "vaultTimeoutAction1": {"message": "Zaman aşımı eylemi"}, "newCustomizationOptionsCalloutTitle": {"message": "<PERSON><PERSON>ri"}, "newCustomizationOptionsCalloutContent": {"message": "Customize your vault experience with quick copy actions, compact mode, and more!"}, "newCustomizationOptionsCalloutLink": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON> he<PERSON> g<PERSON>"}, "lock": {"message": "<PERSON><PERSON><PERSON>", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "<PERSON><PERSON><PERSON> kut<PERSON>", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "Çöp kutusunda ara"}, "permanentlyDeleteItem": {"message": "Kaydı kalıcı olarak sil"}, "permanentlyDeleteItemConfirmation": {"message": "Bu kaydı kalıcı olarak silmek istediğinizden emin misiniz?"}, "permanentlyDeletedItem": {"message": "<PERSON><PERSON><PERSON> kalıcı olarak silindi"}, "restoreItem": {"message": "<PERSON><PERSON><PERSON> geri <PERSON>"}, "restoredItem": {"message": "<PERSON><PERSON><PERSON> geri <PERSON>"}, "alreadyHaveAccount": {"message": "Zaten hesabınız var mı?"}, "vaultTimeoutLogOutConfirmation": {"message": "Çıkış yaptığınızda kasanıza erişiminiz tamamen sonlanacak ve zaman aşımının ardından çevrimiçi kimlik doğrulaması yapmanız gerekecek. Bu ayarı kullanmak istediğinizden emin misiniz?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Zaman aşımı eylem onayı"}, "autoFillAndSave": {"message": "Otomatik doldur ve kaydet"}, "fillAndSave": {"message": "<PERSON><PERSON><PERSON> ve kaydet"}, "autoFillSuccessAndSavedUri": {"message": "Kay<PERSON>t otomatik dolduruldu ve URI kaydedildi"}, "autoFillSuccess": {"message": "Kayıt otomatik dolduruldu "}, "insecurePageWarning": {"message": "Uyarı: <PERSON><PERSON><PERSON><PERSON> olmayan bir HTTP sayfasındasınız. Gönderdiğiniz bilgiler potansiyel olarak başkaları tarafından görülebilir ve değiştirilebilir. Bu hesabı güvenli (HTTPS) bir sayfa üzerinden kaydetmiştiniz."}, "insecurePageWarningFillPrompt": {"message": "<PERSON>e de bu hesabı doldurmak istiyor musunuz?"}, "autofillIframeWarning": {"message": "Bu form, kayıtlı hesabınızın URI'sinden farklı bir alan adında yer alıyor. Yine de otomatik doldurmak isterseniz \"Tamam\"ı, durdurmak için \"İptal\"i seçin."}, "autofillIframeWarningTip": {"message": "İleride bu uyarıyı görmek istemiyorsanız bu siteye ait Bitwarden hesap kaydınıza $HOSTNAME$ URI'sini ekleyin.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "<PERSON> parolayı belirle"}, "currentMasterPass": {"message": "Mevcut ana parola"}, "newMasterPass": {"message": "<PERSON>ni ana parola"}, "confirmNewMasterPass": {"message": "Yeni ana parolayı onaylayın"}, "masterPasswordPolicyInEffect": {"message": "Bir veya daha fazla kuruluş ilkesi gereğince ana parolanız aşağıdaki gereksinimleri karşılamalıdır:"}, "policyInEffectMinComplexity": {"message": "Minimum karmaşıklık puanı: $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Minimum uzunluk: $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Bir veya daha fazla büyük harf içermeli"}, "policyInEffectLowercase": {"message": "Bir veya daha fazla küçük harf içermeli"}, "policyInEffectNumbers": {"message": "Bir veya daha fazla rakam i<PERSON>"}, "policyInEffectSpecial": {"message": "Şu özel karakterlerden birini veya daha fazlasını içermeli: $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Yeni ana parolanız ilke gereksinimlerini karşılamıyor."}, "receiveMarketingEmailsV2": {"message": "Bitwarden'da<PERSON>, du<PERSON><PERSON><PERSON> ve araştırma fırsatları e-posta adresinize gelsin."}, "unsubscribe": {"message": "İstediğ<PERSON>z zaman"}, "atAnyTime": {"message": "aboneliğinizi iptal edebilirsiniz."}, "byContinuingYouAgreeToThe": {"message": "Devam ederek şunları kabul etmiş olursunuz:"}, "and": {"message": "ve"}, "acceptPolicies": {"message": "Bu kutuyu işaretleyerek aşağıdakileri kabul etmiş olursunuz:"}, "acceptPoliciesRequired": {"message": "Hizmet Koşulları ve Gizlilik Politikası kabul edilmemiş."}, "termsOfService": {"message": "Hizmet Koşulları"}, "privacyPolicy": {"message": "Gizlilik Politikası"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "Parola ipucunuz parolanızla aynı olamaz."}, "ok": {"message": "<PERSON><PERSON>"}, "errorRefreshingAccessToken": {"message": "<PERSON><PERSON><PERSON>im Anahtarı Yenileme Hatası"}, "errorRefreshingAccessTokenDesc": {"message": "Yenileme veya API anahtarı bulunamadı. Lütfen çıkış yapıp tekrar giriş yapmayı deneyin."}, "desktopSyncVerificationTitle": {"message": "Masaüstü eşitleme doğrulaması"}, "desktopIntegrationVerificationText": {"message": "Lütfen masaüstü uygulamasında bu parmak izinin göründüğünü onaylayın: "}, "desktopIntegrationDisabledTitle": {"message": "Tarayıcı entegrasyonu ayarlanmadı"}, "desktopIntegrationDisabledDesc": {"message": "Bitwarden masaüstü uygulamasında tarayıcı entegrasyonu ayarlanmamış. Lütfen masaüstü uygulamasının ayarlarından tarayıcı entegrasyonunu kurun."}, "startDesktopTitle": {"message": "Bitwarden masaüstü uygulamasını başlat"}, "startDesktopDesc": {"message": "Biyometri ile açma işlevini kullanmak için Bitwarden masaüstü uygulamasını çalıştırmanız gerekir."}, "errorEnableBiometricTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "errorEnableBiometricDesc": {"message": "İşlem masaüstü uygulaması tarafından iptal edildi"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Masaüstü uygulaması güvenli iletişim kanalını geçersiz kıldı. Lütfen bu işlemi tekrar deneyin"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Masaüstü ile iletişim kesildi"}, "nativeMessagingWrongUserDesc": {"message": "Masaüstü uygulamasında farklı bir hesaba giriş yapılmış. Lütfen her iki uygulamada da aynı hesaba giriş yapın."}, "nativeMessagingWrongUserTitle": {"message": "Hesap uyuşmazlığı"}, "nativeMessagingWrongUserKeyTitle": {"message": "<PERSON><PERSON>yo<PERSON><PERSON>"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biyometrik kilit açma başarısız oldu. Biyometrik gizli anahtarınız kasanın kilidini açamadı. Lütfen biyometriyi yeniden ayarlamayı deneyin."}, "biometricsNotEnabledTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "biometricsNotEnabledDesc": {"message": "Tarayıcıda biyometriyi kullanmak için önce ayarlardan masaüstü biyometrisini ayarlamanız gerekir."}, "biometricsNotSupportedTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "biometricsNotSupportedDesc": {"message": "Tarayıcı biyometrisi bu cihazda desteklenmiyor."}, "biometricsNotUnlockedTitle": {"message": "Kullanıcı kilitlendi ve oturumu kapatıldı"}, "biometricsNotUnlockedDesc": {"message": "Lütfen masaüstü uygulamasından bu kullanıcının kilidini açıp yeniden deneyin."}, "biometricsNotAvailableTitle": {"message": "Biyometrik kilit açma kullanılamıyor"}, "biometricsNotAvailableDesc": {"message": "Biyometrik kilit açma şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin."}, "biometricsFailedTitle": {"message": "Biyometri <PERSON>"}, "biometricsFailedDesc": {"message": "Biyometri doğrulaması tamamlanamadı. Ana parolanızı kullanabilir veya çıkış yapabilirsiniz. Sorun devam ederse Bitwarden destek ekibiyle iletişime geçin."}, "nativeMessaginPermissionErrorTitle": {"message": "<PERSON><PERSON> verilmedi"}, "nativeMessaginPermissionErrorDesc": {"message": "Bitwarden masaüstü uygulamasıyla iletişim kurma iznimiz olmadan tarayıcı uzantısında biyometriyi kullanamayız. Lütfen tekrar deneyin."}, "nativeMessaginPermissionSidebarTitle": {"message": "İzin isteme hatası"}, "nativeMessaginPermissionSidebarDesc": {"message": "Bu işlemi kenar çubuğundan yapamazsınız. Lütfen açılır pencereden yapmayı deneyin."}, "personalOwnershipSubmitError": {"message": "Bir kuruluş ilkesi nedeniyle kişisel kasanıza hesap kaydetmeniz kısıtlanmış. Sahip seçeneğini bir kuruluş olarak değiştirin ve mevcut koleksiyonlar arasından seçim yapın."}, "personalOwnershipPolicyInEffect": {"message": "Bir kuruluş ilkesi sahiplik seçeneklerinizi etkiliyor."}, "personalOwnershipPolicyInEffectImports": {"message": "Bir kuruluş ilkesi, kayıtları kişisel kasanıza içe aktarmayı engelledi."}, "domainsTitle": {"message": "<PERSON>", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Engel<PERSON>n alan <PERSON>"}, "learnMoreAboutBlockedDomains": {"message": "Engellenmiş alan adları hakkında bilgi alın"}, "excludedDomains": {"message": "<PERSON><PERSON> tutulan alan ad<PERSON>ı"}, "excludedDomainsDesc": {"message": "Bitwarden bu alan ad<PERSON>ında hesaplarınızı kaydetmeyi sormayacaktır. Değişikliklerin etkili olması için sayfayı yenilemelisiniz."}, "excludedDomainsDescAlt": {"message": "Bit<PERSON>en, oturum açmış tüm hesaplar için bu alan adlarının hesap bilgilerini kaydetmeyi sormayacaktır. Değişikliklerin etkili olması için sayfayı yenilemeniz gerekir."}, "blockedDomainsDesc": {"message": "Bu siteler için otomatik doldurma ve diğer ilgili özellikler önerilmeyecektir. Değişikliklerin devreye girmesi için sayfayı yenilemelisiniz."}, "autofillBlockedNoticeV2": {"message": "Bu sitede otomatik doldurma engellenmiş."}, "autofillBlockedNoticeGuidance": {"message": "<PERSON><PERSON><PERSON>"}, "change": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "changeButtonTitle": {"message": "Parolayı değiştir - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPasswords": {"message": "<PERSON><PERSON> parola<PERSON>"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Otomatik doldurmayı etkinleştir"}, "turnedOnAutofill": {"message": "Otomatik doldurma etkinleştirildi"}, "dismiss": {"message": "Ka<PERSON><PERSON>"}, "websiteItemLabel": {"message": "Web sitesi $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ geçerli bir alan adı <PERSON>", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "<PERSON><PERSON><PERSON> alan ad<PERSON> değişiklikleri kaydedildi"}, "excludedDomainsSavedSuccess": {"message": "<PERSON> adı istisnası değişiklikleri kaydedildi"}, "limitSendViews": {"message": "<PERSON><PERSON><PERSON><PERSON>i sı<PERSON>"}, "limitSendViewsHint": {"message": "Bu sınıra ulaşıldıktan sonra bu Send'i kimse göremez.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ gösterim kaldı", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send ayrıntıları", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "<PERSON><PERSON>"}, "sendTypeTextToShare": {"message": "Paylaşılacak metin"}, "sendTypeFile": {"message": "<PERSON><PERSON><PERSON>"}, "allSends": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "hideTextByDefault": {"message": "<PERSON><PERSON> o<PERSON>ak gizle"}, "expired": {"message": "S<PERSON><PERSON>i <PERSON>ş"}, "passwordProtected": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "copyLink": {"message": "Bağlantıyı kopyala"}, "copySendLink": {"message": "Send bağlantısını kopyala", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "Parolayı kaldır"}, "delete": {"message": "Sil"}, "removedPassword": {"message": "<PERSON><PERSON><PERSON> kaldı<PERSON>ı"}, "deletedSend": {"message": "Send silindi", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Send bağlantısı", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "<PERSON><PERSON> dı<PERSON>ı"}, "removePasswordConfirmation": {"message": "Parolayı kaldırmak istediğinizden emin misiniz?"}, "deleteSend": {"message": "Send'i sil", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "Bu Send'i silmek istediğinizden emin misiniz?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Bu Send'i kalıcı olarak silmek istediğinizden emin misiniz?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "<PERSON>'i düzenle", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "<PERSON><PERSON><PERSON> tarihi"}, "deletionDateDescV2": {"message": "Bu Send belirtilen tarihte kalıcı olacak silinecek.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "<PERSON> kullanma tarihi"}, "oneDay": {"message": "1 gün"}, "days": {"message": "$DAYS$ gün", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "<PERSON><PERSON>"}, "sendPasswordDescV3": {"message": "Alıcıların bu <PERSON>'e erişmesi için isterseniz parola ekleyebilirsiniz.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "<PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "<PERSON><PERSON>"}, "sendDisabled": {"message": "Send silindi", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Bir kuruluş ilkesi nedeniyle yalnızca mevcut Send'leri silebil<PERSON>z.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send oluş<PERSON><PERSON>u", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send başarıyla oluşturuldu.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "Önümüzdeki 1 saat boyunca bu bağlantıya sahip olan herkes bu Send'e ulaşabilir.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "Önümüzdeki $HOURS$ saat boyunca bu bağlantıya sahip olan herkes bu Send'e ulaşabilir.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "Önümüzdeki 1 gün boyunca bu bağlantıya sahip olan herkes bu Send'e ulaşabilir.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "Önümüzdeki $DAYS$ gün boyunca bu bağlantıya sahip olan herkes bu Send'e ulaşabilir.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send bağlantısı kopyalandı", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send kaydedildi", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Uzantı dışarı alınsın mı?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "Dosya Send'i oluşturmak için uzantıyı yeni bir pencere halinde dışarı almalısınız.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "Dosya seçmek için eklentiyi kenar çubuğunda açın (mümkünse) veya bu banner'a tıklayarak yeni bir pencerede açın."}, "sendFirefoxFileWarning": {"message": "Firefox ile dosya seçmek için eklentiyi kenar çubuğunda açın veya bu banner'a tıklayarak yeni bir pencerede açın."}, "sendSafariFileWarning": {"message": "Safari ile dosya seçmek için bu banner'a tıklayarak eklentiyi yeni bir pencerede açın."}, "popOut": {"message": "Dışarı al"}, "sendFileCalloutHeader": {"message": "Başlamadan önce"}, "expirationDateIsInvalid": {"message": "<PERSON><PERSON><PERSON><PERSON> son kull<PERSON><PERSON> ta<PERSON>hi ge<PERSON><PERSON><PERSON>."}, "deletionDateIsInvalid": {"message": "Belirtilen silinme tarihi geç<PERSON>iz."}, "expirationDateAndTimeRequired": {"message": "Son kullanma tarihi ve saati gere<PERSON>ir."}, "deletionDateAndTimeRequired": {"message": "<PERSON><PERSON>me tarihi ve saati gere<PERSON>lid<PERSON>."}, "dateParsingError": {"message": "Silinme ve son kullanma ta<PERSON>hler<PERSON> kaydedili<PERSON>en bir hata o<PERSON>."}, "hideYourEmail": {"message": "E-posta adresimi Send'i görüntüleyenlerden gizle."}, "passwordPrompt": {"message": "Ana parolayı yeniden iste"}, "passwordConfirmation": {"message": "<PERSON> parola onayı"}, "passwordConfirmationDesc": {"message": "Bu işlem korumalıdır. İşleme devam etmek için lütfen ana parolanızı yeniden girin."}, "emailVerificationRequired": {"message": "E-posta doğrulaması gerekiyor"}, "emailVerifiedV2": {"message": "E-posta doğrulandı"}, "emailVerificationRequiredDesc": {"message": "Bu özelliği kullanmak için e-postanızı doğrulamanız gerekir. E-postanızı web kasasında doğrulayabilirsiniz."}, "updatedMasterPassword": {"message": "<PERSON> parola g<PERSON>"}, "updateMasterPassword": {"message": "<PERSON>ü<PERSON>"}, "updateMasterPasswordWarning": {"message": "Ana parolanız kuruluşunuzdaki bir yönetici tarafından yakın zamanda değiştirildi. Kasanıza erişmek için parolanızı güncellemelisiniz. Devam ettiğinizde oturumunuz kapanacak ve yeniden oturum açmanız gerekecektir. Diğer cihazlardaki aktif oturumlar bir saate kadar aktif kalabilir."}, "updateWeakMasterPasswordWarning": {"message": "Ana parolanız kuruluş ilkelerinizi karşılamıyor. Kasanıza erişmek için ana parolanızı güncellemelisiniz. Devam ettiğinizde oturumunuz kapanacak ve yeniden oturum açmanız gerekecektir. Diğer cihazlardaki aktif oturumlar bir saate kadar aktif kalabilir."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "Otomatik eklenme"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Bu kuruluşun sizi otomatik olarak parola sıfırlamaya ekleyen bir ilkesi bulunmakta. Bu ilkeye eklenmek, kuruluş yöneticilerinin ana parolanızı değiştirebilmesini sağlar."}, "selectFolder": {"message": "Klasör seç..."}, "noFoldersFound": {"message": "Hiçbir klasör bulunamadı", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Kuruluş izinleriniz güncellendi ve bir ana parola belirlemeniz gerekiyor.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Kuruluşunuz bir ana parola belirlemenizi gerektiriyor.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "/ $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Default title for the user verification dialog."}, "hours": {"message": "Saat"}, "minutes": {"message": "Dakika"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Zaman aşımı ayarlarınıza kurumsal ilke gereksinimleri uygulandı"}, "vaultTimeoutPolicyInEffect": {"message": "Kuruluş ilkeleriniz izin verilen maksimum kasa zaman aşımını $HOURS$ saat $MINUTES$ dakika olarak belirlemiş.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "En fazla $HOURS$ saat $MINUTES$ dakika.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Zaman aşımınız kuruluşunuzun belirlediği maksimum süreyi aşıyor: Maksimum $HOURS$ saat $MINUTES$ dakika", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Kuruluş ilkeleriniz kasa zaman aşımınızı etkiliyor. İzin verilen maksimum kasa zaman aşımı $HOURS$ saat $MINUTES$ dakikadır. Kasa zaman aşımı eyleminiz $ACTION$ olarak ayarlanmış.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Kuru<PERSON>ş ilkeleriniz, kasa zaman aşımı eyleminizi $ACTION$ olarak ayarladı.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "<PERSON><PERSON> zaman a<PERSON>, kuruluşunuz tarafından belirlenen kısıtlamaları aşıyor."}, "vaultExportDisabled": {"message": "Kasayı dışa aktarma devre dışı"}, "personalVaultExportPolicyInEffect": {"message": "Bir veya daha fazla kuruluş ilkesi, k<PERSON><PERSON><PERSON><PERSON> kasanızı dışa aktarmanızı engelliyor."}, "copyCustomFieldNameInvalidElement": {"message": "Geçerli bir form elemanı bulunamadı. HTML'i denetlemeyi deneyebilirsiniz."}, "copyCustomFieldNameNotUnique": {"message": "Benzersiz tanımlayıcı bulunamadı."}, "convertOrganizationEncryptionDesc": {"message": "$ORGANIZATION$ kendi barındırdığı bir anahtar sunucusuyla SSO kullanıyor. Bu kuruluşun üyelerinin artık ana parola kullanması gerekmiyor.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "leaveOrganization": {"message": "Kuruluştan ayrıl"}, "removeMasterPassword": {"message": "<PERSON> parolayı kaldır"}, "removedMasterPassword": {"message": "Ana parola kaldırıldı"}, "leaveOrganizationConfirmation": {"message": "Bu kuruluştan ayrılmak istediğinizden emin misiniz?"}, "leftOrganization": {"message": "Kuruluştan ayrıldınız."}, "toggleCharacterCount": {"message": "<PERSON><PERSON><PERSON> sayacını aç/kapat"}, "sessionTimeout": {"message": "Oturumunuzun süresi doldu. Lütfen geri dönüp yeniden giriş yapın."}, "exportingPersonalVaultTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> kasa dışa aktarılıyor"}, "exportingIndividualVaultDescription": {"message": "Yalnızca $EMAIL$ ile ilişkili kasa kayıtları dışa aktarılacaktır. Kuruluş kasasındaki kayıtlar dahil edilmeyecektir. Yalnızca kasa kayıt bilgileri dışa aktarılacak, kayıtlara eklenen dosyalar aktarılmayacaktır.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Yalnızca $EMAIL$ ile ilişkili kişisel kasadaki kayıtlar ve dosyalar dışa aktarılacaktır. Kuruluş kasasındaki kayıtlar dahil edilmeyecektir", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Kuruluş kasasını dışa aktarma"}, "exportingOrganizationVaultDesc": {"message": "Yalnızca $ORGANIZATION$ ile ilişkili kuruluş kasası dışarı aktarılacak. Kişisel kasalardaki ve diğer kuruluşlardaki kayıtlar dahil edilmeyecek.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "<PERSON><PERSON>"}, "decryptionError": {"message": "<PERSON><PERSON><PERSON>"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Kullanıcı adı oluştur"}, "generateEmail": {"message": "E-posta üret"}, "spinboxBoundariesHint": {"message": "Değer $MIN$ ile $MAX$ arasında olmalıdır.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Artı adresli e-posta", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "E-posta sağlayıcınızın alt adres özelliklerini kullanın."}, "catchallEmail": {"message": "Catch-all e-posta"}, "catchallEmailDesc": {"message": "<PERSON> adınızın tüm iletileri yakalamaya ayarlanmış adresini kullanın."}, "random": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "randomWord": {"message": "<PERSON><PERSON><PERSON><PERSON> keli<PERSON>"}, "websiteName": {"message": "Web sitesi adı"}, "service": {"message": "<PERSON><PERSON>"}, "forwardedEmail": {"message": "İletilen e-posta maskesi"}, "forwardedEmailDesc": {"message": "Harici bir yönlendirme servisiyle e-posta maskesi oluştur."}, "forwarderDomainName": {"message": "E-posta alan adı", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Seçtiğiniz servisin desteklediği bir alan adı seçin", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ hatası: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Bitwarden tarafından üretildi.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Web sitesi: $WEBSITE$. Bitwarden tarafından üretildi.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Geçersiz $SERVICENAME$ alan adı.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Geçersiz $SERVICENAME$ URL'si.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Bilinmeyen $SERVICENAME$ hatası oluştu.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Bilinmeyen yönlendirici: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "<PERSON><PERSON><PERSON>", "description": "Part of a URL."}, "apiAccessToken": {"message": "API erişim token'ı"}, "apiKey": {"message": "API anahtarı"}, "ssoKeyConnectorError": {"message": "<PERSON><PERSON><PERSON> bağ<PERSON><PERSON><PERSON><PERSON> hatası: <PERSON><PERSON><PERSON> bağlayıcının kullanılabilir olduğundan ve doğru çalıştığından emin olun."}, "premiumSubcriptionRequired": {"message": "Premium abonelik gerekli"}, "organizationIsDisabled": {"message": "Kuruluş askıya alındı."}, "disabledOrganizationFilterError": {"message": "Askıya alınmış kuruluşlardaki kayıtlara erişilemez. Destek almak için kuruluş sahibinizle iletişime geçin."}, "loggingInTo": {"message": "$DOMAIN$ sitesine giriş yapılıyor", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "<PERSON><PERSON><PERSON>"}, "selfHostedServer": {"message": "şirket içinde barındırılan"}, "thirdParty": {"message": "Üçüncü taraf"}, "thirdPartyServerMessage": {"message": "$SERVERNAME$ adresindeki üçüncü taraf sunucuya bağlandınız. Lütfen resmi sunucuyu kullanarak hataları doğrulayın veya üçüncü taraf sunucuya bildirin.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "son g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarih: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Ana parola ile giriş yap"}, "newAroundHere": {"message": "<PERSON><PERSON><PERSON><PERSON> yeni misiniz?"}, "rememberEmail": {"message": "E-postayı hatırla"}, "loginWithDevice": {"message": "Cihazla giri<PERSON> yap"}, "fingerprintPhraseHeader": {"message": "Parmak izi ifadesi"}, "fingerprintMatchInfo": {"message": "Lütfen kasanızın kilidinin açık olduğundan ve parmak izi ifadesinin diğer cihazla eşleştiğinden emin olun."}, "resendNotification": {"message": "Bildirimi ye<PERSON>"}, "viewAllLogInOptions": {"message": "<PERSON><PERSON><PERSON> g<PERSON> gör"}, "notificationSentDevice": {"message": "Cihazınıza bir bildirim gönderildi."}, "notificationSentDevicePart1": {"message": "Bitwarden kilidini cihazınızdan veya"}, "notificationSentDeviceAnchor": {"message": "web uygulamasından açın"}, "notificationSentDevicePart2": {"message": "Onay vermeden önce parmak izi ifadesinin aşağıdakiyle eşleştiğini kontrol edin."}, "aNotificationWasSentToYourDevice": {"message": "Cihazınıza bir bildirim gö<PERSON>ildi"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "İsteğiniz onaylanınca size haber vereceğiz"}, "needAnotherOptionV1": {"message": "Başka bir seçeneğe mi ihtiyacınız var?"}, "loginInitiated": {"message": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>ld<PERSON>"}, "logInRequestSent": {"message": "İstek gönderildi"}, "exposedMasterPassword": {"message": "Açığa Çıkmış Ana Pa<PERSON>a"}, "exposedMasterPasswordDesc": {"message": "Bu parola bir veri ihlalinde tespit edildi. Hesabınızı korumak için aynı parolayı farklı yerlerde kullanmayın. Bu parolayı kullanmak istediğinizden emin misiniz?"}, "weakAndExposedMasterPassword": {"message": "Z<PERSON><PERSON>f ve Açığa Çıkmış Ana <PERSON>"}, "weakAndBreachedMasterPasswordDesc": {"message": "<PERSON><PERSON> zayı<PERSON> hem de veri ihlalinde yer alan bir tespit edildi. Hesabınızı korumak için güçlü bir parola seçin ve o parolayı başka yerlerde kullanmayın. Bu parolayı kullanmak istediğinizden emin misiniz?"}, "checkForBreaches": {"message": "Bilinen veri ihlallerinde bu parolayı kontrol et"}, "important": {"message": "Önemli:"}, "masterPasswordHint": {"message": "Ana parolanızı unutursanız kurtaramazsınız!"}, "characterMinimum": {"message": "En az $LENGTH$ karakter", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Ku<PERSON><PERSON><PERSON> ilkeleriniz, sayfa yüklenince otomatik doldurmayı etkinleştirdi."}, "howToAutofill": {"message": "Otomatik doldurma nasıl yapılır?"}, "autofillSelectInfoWithCommand": {"message": "Bu ekrandan bir öğe se<PERSON>, $COMMAND$ kısayolunu kullanın veya ayarlardaki diğer seçenekleri keşfedin.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Bu ekrandan bir öğe seçin veya ayarlardaki diğer seçenekleri keşfedin."}, "gotIt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "autofillSettings": {"message": "Otomatik doldurma ayarları"}, "autofillKeyboardShortcutSectionTitle": {"message": "Otomatik doldurma kısayolu"}, "autofillKeyboardShortcutUpdateLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Kısayolları yönet"}, "autofillShortcut": {"message": "Otomatik doldurma klavye kısayolu"}, "autofillLoginShortcutNotSet": {"message": "Hesabı otomatik doldurma kısayolu ayarlanmamış. Bunu tarayıcı ayarlarından değiştirebilirsiniz."}, "autofillLoginShortcutText": {"message": "Hesabı otomatik doldurma kısayolu: $COMMAND$. Tüm kısayolları tarayıcı ayarlarından değiştirebilirsiniz.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Varsayılan otomatik doldurma kısayolu: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "<PERSON><PERSON> pencerede açılır"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Sonraki girişleri kolaylaştırmak için bu cihazı hatırla"}, "deviceApprovalRequired": {"message": "Cihaz onayı gerekiyor. Lütfen onay yönteminizi seçin:"}, "deviceApprovalRequiredV2": {"message": "Cihazı onaylamanız gerekiyor"}, "selectAnApprovalOptionBelow": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bir onay yöntemi seçin"}, "rememberThisDevice": {"message": "Bu cihazı hatırla"}, "uncheckIfPublicDevice": {"message": "Paylaşılan bir cihaz kullanıyorsanız işaretlemeyin"}, "approveFromYourOtherDevice": {"message": "<PERSON><PERSON><PERSON>"}, "requestAdminApproval": {"message": "Yönetici onayı iste"}, "ssoIdentifierRequired": {"message": "Kuruluş SSO tanımlayıcısı gereklidir."}, "creatingAccountOn": {"message": "<PERSON><PERSON><PERSON>:"}, "checkYourEmail": {"message": "E-postanızı kontrol edin"}, "followTheLinkInTheEmailSentTo": {"message": "Hesabınızı oluşturmaya devam etmek için"}, "andContinueCreatingYourAccount": {"message": "adresine gönderdiğimiz e-postadaki bağlantıya tıklayın."}, "noEmail": {"message": "E-posta gelmedi mi?"}, "goBack": {"message": "<PERSON><PERSON>"}, "toEditYourEmailAddress": {"message": "e-posta adresinizi dü<PERSON>."}, "eu": {"message": "AB", "description": "European Union"}, "accessDenied": {"message": "Erişim engellendi. Bu sayfayı görüntüleme iznine sahip değilsiniz."}, "general": {"message": "<PERSON><PERSON>"}, "display": {"message": "G<PERSON>rü<PERSON><PERSON><PERSON>"}, "accountSuccessfullyCreated": {"message": "<PERSON><PERSON>p başarıyla oluşturuldu!"}, "adminApprovalRequested": {"message": "Yönetici onayı istendi"}, "adminApprovalRequestSentToAdmins": {"message": "İsteğiniz yöneticinize gönderildi."}, "troubleLoggingIn": {"message": "<PERSON><PERSON><PERSON> yaparken sorun mu yaşıyorsunuz?"}, "loginApproved": {"message": "<PERSON><PERSON><PERSON>ı"}, "userEmailMissing": {"message": "Kullanıcının e-postası eksik"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Aktif <PERSON>cı e-postası bulunamadı. Çıkış yapılıyor."}, "deviceTrusted": {"message": "Cihaza güvenildi"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "organizationNotTrusted": {"message": "Kuruluş güven<PERSON>r <PERSON>"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsNoItemsTitle": {"message": "Aktif <PERSON>k", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsNoItemsMessage": {"message": "Şifrelenmiş bilgileri güvenle paylaşmak için Send'i kullanabilirsiniz.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "<PERSON><PERSON><PERSON>."}, "required": {"message": "gere<PERSON><PERSON>"}, "search": {"message": "Ara"}, "inputMinLength": {"message": "Girdi en az $COUNT$ karakter uzunluğunda olmalıdır.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Girdi $COUNT$ karakter uzunluğunu geçmemelidir.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "<PERSON><PERSON> karakterlere izin verilmez: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Girdi değeri en az $MIN$ olmalı.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Girdi değeri en fazla $MAX$ olmalı.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "Bir veya daha fazla e-posta geçersiz"}, "inputTrimValidator": {"message": "Girdi yalnızca boşluktan ibaret olamaz.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Girdi bir e-posta adresi <PERSON>."}, "fieldsNeedAttention": {"message": "Yukarıdaki $COUNT$ alanla ilgilenmeniz gerekiyor.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 alanla ilgilenmeniz gerekiyor."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ alanla ilgilenmeniz gerekiyor.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- <PERSON><PERSON><PERSON> --"}, "multiSelectPlaceholder": {"message": "-- Filtrelemek için ya<PERSON>ı<PERSON> --"}, "multiSelectLoading": {"message": "Seçenekler alınıyor..."}, "multiSelectNotFound": {"message": "<PERSON><PERSON> kayıt bulunamadı"}, "multiSelectClearAll": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle"}, "plusNMore": {"message": "+ $QUANTITY$ tane daha", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "<PERSON>"}, "toggleCollapse": {"message": "Daraltmayı aç/kapat", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "<PERSON><PERSON> alan adı"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Ana parolayı yeniden isteyen kayıtlar sayfa yüklenince otomatik olarak doldurulamaz. Sayfa yüklenince otomatik doldurma kapatıldı.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Sayfa yüklenince otomatik doldurma, varsayılan ayarı kullanacak şekilde ayarlandı.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Bu alanı düzenlemek için ana parolayı yeniden istemeyi kapatın", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "<PERSON><PERSON>ü aç/kapat"}, "skipToContent": {"message": "İçeriğe geç"}, "bitwardenOverlayButton": {"message": "Bitwarden otomatik doldurma menüsü düğmesi", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Bitwarden otomatik doldurma menüsünü aç/kapat", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden otomatik doldurma menüsü", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Eşleşen hesaplarınızı görmek için hesabınızın kilidini açın", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Otomatik doldurma önerilerini görmek için hesabınızın kilidini açın", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "<PERSON><PERSON><PERSON> kili<PERSON> a<PERSON>", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Zamana dayalı tek seferlik parola doğrulama kodu", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Geçerli TOTP için kalan süre", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Bilgileri doldur", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> adı", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "Gösterilecek kayıt yok", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "<PERSON><PERSON> ka<PERSON>ıt", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Kasaya yeni kayıt ekle", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "<PERSON><PERSON>", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "Yeni kart", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "<PERSON><PERSON> kimlik", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden otomatik doldurma menüsü mevcut. Seçmek için aşağı ok tuşuna basın.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Aç"}, "ignore": {"message": "<PERSON><PERSON> say"}, "importData": {"message": "Verileri içe aktar", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "İçe aktarma hatası"}, "importErrorDesc": {"message": "İçe aktarmaya çalıştığınız verilerle ilgili bir problem var. Lütfen kaynak dosyanızdaki aşağıda belirtilen hataları çözüp tekrar deneyin."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Aşağıdaki hataları düzeltip yeniden deneyin."}, "description": {"message": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "importSuccess": {"message": "Veriler başarıyla içe aktarıldı"}, "importSuccessNumberOfItems": {"message": "Toplam $AMOUNT$ kayıt içe aktarıldı.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "<PERSON><PERSON><PERSON> dene"}, "verificationRequiredForActionSetPinToContinue": {"message": "Bu işlem için doğrulama gerekiyor. Devam etmek için bir PIN belirleyin."}, "setPin": {"message": "P<PERSON> belirle"}, "verifyWithBiometrics": {"message": "Biyometri ile doğrula"}, "awaitingConfirmation": {"message": "<PERSON><PERSON> be<PERSON>"}, "couldNotCompleteBiometrics": {"message": "Biyometri işlemi tamamlanamadı."}, "needADifferentMethod": {"message": "Farklı bir yönteme mi ihtiyacınız var?"}, "useMasterPassword": {"message": "<PERSON> parola<PERSON> kullan"}, "usePin": {"message": "<PERSON><PERSON> kullan"}, "useBiometrics": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "enterVerificationCodeSentToEmail": {"message": "E-posta adresinize gönderilen doğrulama kodunu girin."}, "resendCode": {"message": "<PERSON><PERSON> yeniden gönder"}, "total": {"message": "Toplam"}, "importWarning": {"message": "$ORGANIZATION$ kuruluşuna veri aktarıyorsunuz. Verileriniz bu kuruluşun üyeleriyle paylaşılabilir. Devam etmek istiyor musunuz?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Hesabınız için Duo iki adımlı giriş gereklidir."}, "popoutExtension": {"message": "Uzantıyı dışarı al"}, "launchDuo": {"message": "<PERSON>'yu aç"}, "importFormatError": {"message": "Veriler doğru biçimlendirilmemiş. Lütfen içe aktarma dosyanızı kontrol edin ve tekrar deneyin."}, "importNothingError": {"message": "Hiçbir şey içe aktarılmadı."}, "importEncKeyError": {"message": "Dışa aktarılmış dosya çözülemedi. Şifreleme anahtarınız, veriler dışa aktarılırken kullanılanla uyuşmuyor."}, "invalidFilePassword": {"message": "Geçersiz dosya parolası. Lütfen dışa aktardığınız dosyayı oluştururken girdiğiniz parolayı kullanın."}, "destination": {"message": "<PERSON><PERSON><PERSON>"}, "learnAboutImportOptions": {"message": "İçe aktarma seçeneklerinizi öğrenin"}, "selectImportFolder": {"message": "<PERSON><PERSON> k<PERSON>ö<PERSON>"}, "selectImportCollection": {"message": "Bir koleksiyon se<PERSON>"}, "importTargetHint": {"message": "İçe aktarılan dosya içeriklerinin $DESTINATION$ konumuna taşınmasını istiyorsanız bu seçeneği seçin", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "<PERSON><PERSON>a atanmamış öğeler içeriyor."}, "selectFormat": {"message": "İçe aktarma dosyasının biçimini seçin"}, "selectImportFile": {"message": "İçe aktarma dosyasını seçin"}, "chooseFile": {"message": "<PERSON><PERSON><PERSON>"}, "noFileChosen": {"message": "<PERSON><PERSON><PERSON>"}, "orCopyPasteFileContents": {"message": "veya içe aktarma dosyasının içeriğini kopyalayıp yapıştırın"}, "instructionsFor": {"message": "$NAME$ Talimatları", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Kasayı içe aktarmayı onaylayın"}, "confirmVaultImportDesc": {"message": "Bu dosya parola korumalıdır. Verileri içe aktarmak için lütfen dosya parolasını girin."}, "confirmFilePassword": {"message": "<PERSON><PERSON>a <PERSON>ını onaylayın"}, "exportSuccess": {"message": "Kasa verileri dışa aktarıldı"}, "typePasskey": {"message": "Geçiş anahtarı"}, "accessing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> konum:"}, "loggedInExclamation": {"message": "G<PERSON>ş yapıldı!"}, "passkeyNotCopied": {"message": "Geçiş anahtarı kopyalanmayacak"}, "passkeyNotCopiedAlert": {"message": "Geçiş anahtarı klonlanan öğeye kopyalanmayacaktır. Bu öğeyi klonlamaya devam etmek istiyor musunuz?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Site kimlik doğrulaması gerektiriyor. Bu özellik henüz ana parolası olmayan hesaplarda kullanılamaz."}, "logInWithPasskeyQuestion": {"message": "Geçiş anahtarı ile giriş yapılsın mı?"}, "passkeyAlreadyExists": {"message": "Bu uygulama için bir geçiş anahtarı zaten mevcut."}, "noPasskeysFoundForThisApplication": {"message": "Bu uygulamaya ait hiç geçiş anahtarı bulunamadı."}, "noMatchingPasskeyLogin": {"message": "Bu siteyle eşleşen hiç hesabınız yok."}, "noMatchingLoginsForSite": {"message": "Bu siteyle eşleşen hesap bulunamadı"}, "searchSavePasskeyNewLogin": {"message": "Geçiş anahtarı ara veya yeni hesap olarak kaydet"}, "confirm": {"message": "<PERSON><PERSON><PERSON>"}, "savePasskey": {"message": "Geçiş anahtarını kaydet"}, "savePasskeyNewLogin": {"message": "Geçiş anahtarını yeni hesap olarak kaydet"}, "chooseCipherForPasskeySave": {"message": "Bu geçiş anahtarının kaydedileceği hesabı seçin"}, "chooseCipherForPasskeyAuth": {"message": "G<PERSON>ş yapılacak geçiş anahtarını seçin"}, "passkeyItem": {"message": "Geçiş anahtarı kaydı"}, "overwritePasskey": {"message": "Geçiş anahtarının üzerine yazılsın mı?"}, "overwritePasskeyAlert": {"message": "Bu kayıt zaten bir geçiş anahtarı içeriyor. Mevcut geçiş anahtarının üzerine yazmak istediğinizden emin misiniz?"}, "featureNotSupported": {"message": "Bu özellik henüz desteklenmiyor"}, "yourPasskeyIsLocked": {"message": "Geçiş anahtarını kullanmak için kimlik doğrulama gerekiyor. Devam etmek için kimliğinizi doğrulayın."}, "multifactorAuthenticationCancelled": {"message": "Çok faktörlü kimlik doğrulama iptal edildi"}, "noLastPassDataFound": {"message": "LastPass verisi bulunamadı"}, "incorrectUsernameOrPassword": {"message": "Kullanıcı adı veya parola yanlış"}, "incorrectPassword": {"message": "Yanlış parola"}, "incorrectCode": {"message": "Yanlış kod"}, "incorrectPin": {"message": "Yanlış PIN"}, "multifactorAuthenticationFailed": {"message": "Çok faktörlü kimlik doğrulama başarısız oldu"}, "includeSharedFolders": {"message": "Paylaşılan klasörleri dahil et"}, "lastPassEmail": {"message": "LastPass E-postası"}, "importingYourAccount": {"message": "Hesabınız içe aktarılıyor..."}, "lastPassMFARequired": {"message": "LastPass çok faktörlü kimlik doğrulaması gerekli"}, "lastPassMFADesc": {"message": "Kimlik doğrulama uygulamanızdaki tek kullanımlık kodu girin"}, "lastPassOOBDesc": {"message": "Kimlik doğrulama uygulamanızda oturum açma isteğini onaylayın veya tek kullanımlık kodu girin."}, "passcode": {"message": "Kod"}, "lastPassMasterPassword": {"message": "LastPass ana parola<PERSON>ı"}, "lastPassAuthRequired": {"message": "LastPass kimlik doğrulaması gerekli"}, "awaitingSSO": {"message": "SSO kimlik doğrulaması bekleniyor"}, "awaitingSSODesc": {"message": "Lütfen şirket hesabınızla giriş yapmaya devam edin."}, "seeDetailedInstructions": {"message": "Ayrıntılı talimatları yardım sitemizde bulabilirsiniz:", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "<PERSON><PERSON><PERSON><PERSON>Pass'ten içe aktar"}, "importFromCSV": {"message": "CSV'den içe aktar"}, "lastPassTryAgainCheckEmail": {"message": "<PERSON><PERSON><PERSON> deneyin veya LastPass'ten gelecek kimlik doğrulama e-postasını kontrol edin."}, "collection": {"message": "Koleksiyon"}, "lastPassYubikeyDesc": {"message": "LastPass hesabınızla ilişkili YubiKey'i bilgisayarınızın USB portuna takıp düğmesine dokunun."}, "switchAccount": {"message": "Hesabı değiştir"}, "switchAccounts": {"message": "Hesapları değiştir"}, "switchToAccount": {"message": "Hesaba geç"}, "activeAccount": {"message": "<PERSON><PERSON><PERSON>"}, "bitwardenAccount": {"message": "Bitwarden hesabı"}, "availableAccounts": {"message": "Mevcut hesaplar"}, "accountLimitReached": {"message": "Hesap sınırına ulaştınız. Yeni hesap eklemek için hesaplardan birinden çıkış yapın."}, "active": {"message": "aktif"}, "locked": {"message": "kilitli"}, "unlocked": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "server": {"message": "<PERSON><PERSON><PERSON>"}, "hostedAt": {"message": "konum"}, "useDeviceOrHardwareKey": {"message": "Cihazımı veya anahtar donanımımı kullanacağım"}, "justOnce": {"message": "Yalnızca bir defa"}, "alwaysForThisSite": {"message": "Bu site i<PERSON>in her zaman"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ hariç tutulan alan ad<PERSON>ına eklendi.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "<PERSON>ık kullanılan biçimler", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Tarayıcı ayarlarına gidilsin mi?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "<PERSON><PERSON>m merkezine gitmek ister misiniz?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Bitwarden varsayılan parola yöneticiniz yapılsın mı?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Bu seçeneği göz ardı ederseniz Bitwarden otomatik doldurma menüsüyle tarayıcınızınki arasında çakışma yaşanabilir.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Bitwarden'ı varsayılan parola yöneticiniz yapın", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Bitwarden varsayılan parola yöneticisi olarak ayarlanamadı", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "Bitwarden'ı varsayılan parola yöneticisi olarak ayarlamak için tarayıcı gizlilik izinlerini vermeniz gerekir.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Varsayılan yap", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Kimlik bilgileri başarıyla kaydedildi!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Kimlik bilgileri başarıyla güncellendi!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "<PERSON><PERSON><PERSON> g<PERSON>!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Kimlik bilgileri kaydedilirken hata oluştu. Ayrıntılar için konsolu kontrol edin.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Başarılı"}, "removePasskey": {"message": "Geçiş anahtarını kaldır"}, "passkeyRemoved": {"message": "Geçiş anahtarı kaldırıldı"}, "autofillSuggestions": {"message": "Otomatik doldurma önerileri"}, "itemSuggestions": {"message": "<PERSON>ner<PERSON>n ka<PERSON>ı<PERSON>"}, "autofillSuggestionsTip": {"message": "Otomatik doldurma için bu siteye ait bir hesa<PERSON> ka<PERSON>"}, "yourVaultIsEmpty": {"message": "Ka<PERSON><PERSON><PERSON> boş"}, "noItemsMatchSearch": {"message": "Aramanızla eşleşen kayıt yok"}, "clearFiltersOrTryAnother": {"message": "Filtreleri temizleyin veya başka bir arama yapmayı deneyin"}, "copyInfoTitle": {"message": "Bilgileri kopyala - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Notu kopyala - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "<PERSON><PERSON><PERSON>, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "<PERSON><PERSON><PERSON>r - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "Kaydı görüntüle - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "Kaydı görüntüle - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Otomatik doldur - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Otomatik doldur - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Kopyala: $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "Kopyalanacak değer yok"}, "assignToCollections": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ata"}, "copyEmail": {"message": "E-postayı kopyala"}, "copyPhone": {"message": "Telefonu kopyala"}, "copyAddress": {"message": "<PERSON><PERSON><PERSON>"}, "adminConsole": {"message": "Yönetici Konsolu"}, "accountSecurity": {"message": "<PERSON><PERSON><PERSON>"}, "notifications": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "appearance": {"message": "G<PERSON>rü<PERSON><PERSON><PERSON>"}, "errorAssigningTargetCollection": {"message": "<PERSON><PERSON><PERSON> atama hatası."}, "errorAssigningTargetFolder": {"message": "<PERSON><PERSON><PERSON> atama hatas<PERSON>."}, "viewItemsIn": {"message": "$NAME$ içindeki kayıtları görüntüle", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "$NAME$ klasörüne dön", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "<PERSON><PERSON>"}, "removeItem": {"message": "$NAME$ klasörünü kaldır", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Klasörü o<PERSON> ka<PERSON>"}, "itemDetails": {"message": "Kayıt ayrıntıları"}, "itemName": {"message": "<PERSON><PERSON>t adı"}, "organizationIsDeactivated": {"message": "Kuruluş pasifleştirilmiş"}, "owner": {"message": "<PERSON><PERSON>"}, "selfOwnershipLabel": {"message": "Siz", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Pasif <PERSON>lardaki kayıtlara erişilemez. Destek almak için kuruluş sahibinizle iletişime geçin."}, "additionalInformation": {"message": "Ek bi<PERSON>"}, "itemHistory": {"message": "Kayıt geçmişi"}, "lastEdited": {"message": "Son dü<PERSON><PERSON>me"}, "ownerYou": {"message": "Sahibi: <PERSON><PERSON>"}, "linked": {"message": "Bağlandı"}, "copySuccessful": {"message": "Kopyalama başarılı"}, "upload": {"message": "<PERSON><PERSON><PERSON>"}, "addAttachment": {"message": "<PERSON><PERSON><PERSON>"}, "maxFileSizeSansPunctuation": {"message": "<PERSON><PERSON><PERSON><PERSON> dosya boyutu 500 MB'dir"}, "deleteAttachmentName": {"message": "$NAME$ dosyasını sil", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "$NAME$ dosyasını indir", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Bitwarden’ı indirin"}, "downloadBitwardenOnAllDevices": {"message": "Bitwarden’ı tüm cihazlarınıza indirin"}, "getTheMobileApp": {"message": "Mobil uygulamayı indirin"}, "getTheMobileAppDesc": {"message": "Parolalarınıza Bitwarden mobil uygulaması sayesinde her yerden ulaşın."}, "getTheDesktopApp": {"message": "Masaüstü uygulamasını indirin"}, "getTheDesktopAppDesc": {"message": "Kasanıza tarayıcıyı kullanmadan erişin, biyometri ile kilit açmayı etkinleştirerek hem masaüstü uygulamasından hem de tarayıcı uzantısından kilit açmayı geliştirin."}, "downloadFromBitwardenNow": {"message": "Şimdi bitwarden.com’dan indirin"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Bu dosyayı kalıcı olarak silmek istediğinizden emin misiniz?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "filterVault": {"message": "Kasayı filtrele"}, "filterApplied": {"message": "1 filtre uygulandı"}, "filterAppliedPlural": {"message": "$COUNT$ filtre uygulandı", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "<PERSON><PERSON><PERSON><PERSON> bil<PERSON>"}, "identification": {"message": "<PERSON><PERSON>"}, "contactInfo": {"message": "İletişim bilgileri"}, "downloadAttachment": {"message": "İndir - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "kart numarasının sonu", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "<PERSON><PERSON><PERSON>"}, "authenticatorKey": {"message": "Kimlik doğrulama anahtarı"}, "autofillOptions": {"message": "Otomatik doldurma ayarları"}, "websiteUri": {"message": "Web sitesi (URI)"}, "websiteUriCount": {"message": "Web sitesi (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Web sitesi eklendi"}, "addWebsite": {"message": "Web sitesi ekle"}, "deleteWebsite": {"message": "Web sitesini sil"}, "defaultLabel": {"message": "Varsayılan ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "$WEBSITE$ eşleşme tespitini göster", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "$WEBSITE$ eşleşme tespitini gizle", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Sayfa yüklenince otomatik doldur"}, "cardExpiredTitle": {"message": "Kartın süresi dolmuş"}, "cardExpiredMessage": {"message": "Kartı yenilediyseniz kart bilgilerini güncelleyin"}, "cardDetails": {"message": "<PERSON><PERSON> bil<PERSON>i"}, "cardBrandDetails": {"message": "$BRAND$ bilgileri", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Animasyonları etkinleştir"}, "showAnimations": {"message": "Animasyonları göster"}, "addAccount": {"message": "<PERSON><PERSON><PERSON> e<PERSON>"}, "loading": {"message": "Yükleniyor"}, "data": {"message": "<PERSON><PERSON>"}, "passkeys": {"message": "Geçiş Anahtarları", "description": "A section header for a list of passkeys."}, "passwords": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Geçiş anahtarıyla giriş yap", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "<PERSON>a"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "<PERSON>"}, "add": {"message": "<PERSON><PERSON>"}, "fieldType": {"message": "<PERSON>"}, "fieldLabel": {"message": "<PERSON>"}, "textHelpText": {"message": "Güvenlik sorusu gibi veriler için metin alanlarını kullanın"}, "hiddenHelpText": {"message": "Parola gibi hassas verileri için gizli alanları kullanın"}, "checkBoxHelpText": {"message": "Formlardaki onay kutularını (örn. \"e-posta adresimi hatırla\") otomatik doldurmak isterseniz onay kutularını kullanın"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "<PERSON><PERSON>"}, "editFieldLabel": {"message": "$LABEL$ alanını düzenle", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "$LABEL$ etiketini sil", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ eklendi", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "Hiçbir şey seçmediniz."}, "itemsMovedToOrg": {"message": "Kayıtlar $ORGNAME$ kuruluşuna taşındı", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Kayıt $ORGNAME$ kuruluşuna taşındı", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "<PERSON><PERSON>t konumu"}, "fileSend": {"message": "<PERSON><PERSON><PERSON>"}, "fileSends": {"message": "<PERSON><PERSON><PERSON>"}, "textSend": {"message": "Metin Send'i"}, "textSends": {"message": "Metin <PERSON>leri"}, "accountActions": {"message": "<PERSON><PERSON><PERSON>"}, "showNumberOfAutofillSuggestions": {"message": "Otomatik öneri sayısını uzantı simgesinde göster"}, "showQuickCopyActions": {"message": "Kasada hızlı kopyalama komutlarını göster"}, "systemDefault": {"message": "Sistem <PERSON>ılanı"}, "enterprisePolicyRequirementsApplied": {"message": "Bu ayara kurumsal ilke gereksinimleri uygulandı"}, "sshPrivateKey": {"message": "<PERSON><PERSON>"}, "sshPublicKey": {"message": "Ortak <PERSON>"}, "sshFingerprint": {"message": "Parmak izi"}, "sshKeyAlgorithm": {"message": "<PERSON><PERSON><PERSON>"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048 bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072 bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096 bit"}, "retry": {"message": "<PERSON><PERSON><PERSON> dene"}, "vaultCustomTimeoutMinimum": {"message": "Minimum özel zaman aşımı 1 dakikadır."}, "additionalContentAvailable": {"message": "Ek içerikler mevcut"}, "fileSavedToDevice": {"message": "Dosya cihaza kaydedildi. Cihazınızın indirilenler klasöründen yönetebilirsiniz."}, "showCharacterCount": {"message": "<PERSON><PERSON><PERSON>ö<PERSON>"}, "hideCharacterCount": {"message": "<PERSON><PERSON><PERSON> gizle"}, "itemsInTrash": {"message": "Çöp kutusundaki kayıtlar"}, "noItemsInTrash": {"message": "Çöp kutusunda hiç kayıt yok"}, "noItemsInTrashDesc": {"message": "Sildiğiniz kayıtlar burada görünecek ve 30 gün sonra kalıcı olarak silinecektir"}, "trashWarning": {"message": "30 günden uzun süre çöp kutusunda duran kayıtlar otomatik olarak silinecektir"}, "restore": {"message": "<PERSON><PERSON>"}, "deleteForever": {"message": "Kalıcı olarak sil"}, "noEditPermissions": {"message": "Bu kaydı düzenleme yetkisine sahip değilsiniz"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "authenticating": {"message": "Kimlik doğrulanıyor"}, "fillGeneratedPassword": {"message": "Üretilen parolayı doldur", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "<PERSON><PERSON><PERSON>", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Bitwarden’a kaydet", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Boşluk", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Ters tırnak", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Ünlem işareti", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At işareti", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Yüzde işareti", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "D<PERSON>zeltme işareti", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ve işareti", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Yıld<PERSON>z işareti", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Sol parantez", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Sağ parantez", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "<PERSON>", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Tire", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Artı", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Eş<PERSON><PERSON>", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Sol küme parantezi", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "<PERSON><PERSON> küme parantezi", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Sol köşeli parantez", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Sağ köşeli parantez", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "Ç<PERSON>uk", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Ters eğik <PERSON>", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "İki nokta", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Noktalı virgül", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Çift tırnak", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Tek tırnak", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Küçüktür", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Büyüktür", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Virg<PERSON><PERSON>", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Nokta", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Bölü işareti", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Küçük harf"}, "uppercaseAriaLabel": {"message": "Büyük harf"}, "generatedPassword": {"message": "Üretilen parola"}, "compactMode": {"message": "Kompakt mod"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Uzantı genişliği"}, "wide": {"message": "Geniş"}, "extraWide": {"message": "Ekstra geniş"}, "sshKeyWrongPassword": {"message": "Girdiğiniz parola yanlış."}, "importSshKey": {"message": "İçe aktar"}, "confirmSshKeyPassword": {"message": "Parolayı onaylayın"}, "enterSshKeyPasswordDesc": {"message": "SSH anahtarının parolasını girin."}, "enterSshKeyPassword": {"message": "Parolayı girin"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Lütfen masaüstü uygulamanızı güncelleyin"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "<PERSON><PERSON>"}, "emptyVaultDescription": {"message": "Ka<PERSON><PERSON>z sadece par<PERSON>ınız iç<PERSON> de<PERSON>. Hesaplarınızı, k<PERSON><PERSON><PERSON><PERSON><PERSON>, kredi kartların<PERSON>zı ve notlarınızı da güvenle burada depolayabilirsiniz."}, "introCarouselLabel": {"message": "Bitwarden’a hoş geldiniz"}, "securityPrioritized": {"message": "<PERSON>nce güvenlik"}, "securityPrioritizedBody": {"message": "Hesaplarınızı, kartlarınızı ve kimliklerinizi güvenli kasanıza kaydedin. Bitwarden'ın sıfır bilgi ispatlı uçtan uca şifrelemesi sizin için önemli olan her şeyi korur."}, "quickLogin": {"message": "Hızlı ve kolay giriş"}, "quickLoginBody": {"message": "Hesaplarınıza parola yazmadan giriş yapmak için biyometrik kilit açmayı ayarlayabilirsiniz."}, "secureUser": {"message": "Hesaplarınızı güçlendirin"}, "secureUserBody": {"message": "Hesaplarınız için güçlü ve benzersiz parolalar oluşturmak amacıyla üreteci kullanabilirsiniz."}, "secureDevices": {"message": "Verilerinize her zaman, her yerden ulaşın"}, "secureDevicesBody": {"message": "Bitwarden mobil, tarayıcı ve masaüstü uygulamalarıyla istediğiniz kadar cihaza istediğiniz kadar parola kaydedebilirsiniz."}, "emptyVaultNudgeTitle": {"message": "Mevcut parolaları içe aktar"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Şimdi içe aktar"}, "hasItemsVaultNudgeTitle": {"message": "Kasanıza hoş geldiniz!"}, "hasItemsVaultNudgeBody": {"message": "Autofill items for the current page\nFavorite items for easy access\nSearch your vault for something else"}, "newLoginNudgeTitle": {"message": "Otomatik doldurmayla zaman kazanın"}, "newLoginNudgeBodyOne": {"message": "Bu hesabın otomatik doldurma önerisi olarak görünmesi için bir", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "web sitesi", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "e<PERSON><PERSON>.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Kesintisiz çevrimiçi alışveriş"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Hesap oluşturmak artık daha basit"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "<PERSON><PERSON><PERSON> veril<PERSON> gü<PERSON>de tutun"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}}