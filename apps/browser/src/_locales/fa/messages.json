{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "مدیریت رمز عبور Bitwarden", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "در خانه، محل کار و هر کجای دیگر، Bitwarden به راحتی همه‌ رمزها و کلیدهای عبور، و اطلاعات حساس شما را ایمن می‌کند", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "وارد شوید یا یک حساب کاربری بسازید تا به گاوصندوق امن‌تان دسترسی یابید."}, "inviteAccepted": {"message": "Invitation accepted"}, "createAccount": {"message": "ایج<PERSON> حساب کاربری"}, "newToBitwarden": {"message": "New to Bitwarden?"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "Welcome back"}, "setAStrongPassword": {"message": "تنظیم رمز عبور قوی"}, "finishCreatingYourAccountBySettingAPassword": {"message": "ایجاد حساب خود را با تنظیم رمز عبور تکمیل کنید"}, "enterpriseSingleSignOn": {"message": "ورود به سیستم پروژه"}, "cancel": {"message": "انصراف"}, "close": {"message": "بستن"}, "submit": {"message": "ثبت"}, "emailAddress": {"message": "نشانی ایمیل"}, "masterPass": {"message": "کلمه عبور اصلی"}, "masterPassDesc": {"message": "کلمه عبور اصلی، کلمه عبوری است که شما برای دسترسی به گاوصندوق خود استفاده می‌کنید. به یاد داشتن کلمه عبور اصلی بسیار اهمیت دارد. اگر فراموشش کنید هیچ راهی برای بازگردانی آن وجود ندارد."}, "masterPassHintDesc": {"message": "یادآور کلمه عبور اصلی کمک می‌کند در صورت فراموشی آن را به یاد بیارید."}, "masterPassHintText": {"message": "If you forget your password, the password hint can be sent to your email. $CURRENT$/$MAXIMUM$ character maximum.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "نوشتن دوباره کلمه عبور اصلی"}, "masterPassHint": {"message": "یادآور کلمه عبور اصلی (اختیاری)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Join organization"}, "joinOrganizationName": {"message": "Join $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Finish joining this organization by setting a master password."}, "tab": {"message": "زبانه"}, "vault": {"message": "گاوصندوق"}, "myVault": {"message": "گاوصندوق من"}, "allVaults": {"message": "تمام گاوصندوق‌ها"}, "tools": {"message": "ابزار"}, "settings": {"message": "تنظیمات"}, "currentTab": {"message": "زبانه فعلی"}, "copyPassword": {"message": "کپی کلمه عبور"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "کپی یادداشت"}, "copyUri": {"message": "کپی نشانی اینترنتی"}, "copyUsername": {"message": "کپی نام کاربری"}, "copyNumber": {"message": "کپی شماره"}, "copySecurityCode": {"message": "ک<PERSON>ی کد امنیتی"}, "copyName": {"message": "Copy name"}, "copyCompany": {"message": "Copy company"}, "copySSN": {"message": "Copy Social Security number"}, "copyPassportNumber": {"message": "Copy passport number"}, "copyLicenseNumber": {"message": "Copy license number"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "Copy notes"}, "copy": {"message": "Copy", "description": "Copy to clipboard"}, "fill": {"message": "Fill", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "پر کردن خودکار"}, "autoFillLogin": {"message": "پر کردن خودکار ورود"}, "autoFillCard": {"message": "پر کردن خودکار کارت"}, "autoFillIdentity": {"message": "پر کردن خودکار هویت"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "ساخت کلمه عبور (کپی شد)"}, "copyElementIdentifier": {"message": "کپی نام فیلد سفارشی"}, "noMatchingLogins": {"message": "ورودی‌ها منتطبق نیست"}, "noCards": {"message": "کارتی وجود ندارد"}, "noIdentities": {"message": "هویتی وجود ندارد"}, "addLoginMenu": {"message": "افزودن ورود"}, "addCardMenu": {"message": "افزودن کارت"}, "addIdentityMenu": {"message": "افزودن هویت"}, "unlockVaultMenu": {"message": "قفل گاوصندوق خود را باز کنید"}, "loginToVaultMenu": {"message": "وارد شدن به گاو‌صندوقتان"}, "autoFillInfo": {"message": "پر کردن خودکار برای برگه فعلی مرورگر در دسترس نیست."}, "addLogin": {"message": "افزودن یک ورود"}, "addItem": {"message": "افزو<PERSON>ن مورد"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "دریافت یادآور کلمه عبور اصلی"}, "continue": {"message": "ادامه"}, "sendVerificationCode": {"message": "یک کد تأیید به ایمیل خود ارسال کنید"}, "sendCode": {"message": "ارسال کد"}, "codeSent": {"message": "ک<PERSON> ارسال شد"}, "verificationCode": {"message": "کد تأیید"}, "confirmIdentity": {"message": "برای ادامه، هویت خود را تأیید کنید."}, "changeMasterPassword": {"message": "تغییر کلمه عبور اصلی"}, "continueToWebApp": {"message": "Continue to web app?"}, "continueToWebAppDesc": {"message": "ویژگی‌های بیشتر حسا<PERSON> <PERSON><PERSON><PERSON> خود را در برنامه وب کاوش کنید."}, "continueToHelpCenter": {"message": "Continue to Help Center?"}, "continueToHelpCenterDesc": {"message": "درباره استفاده از Bitwarden در مرکز راهنما بیشتر بیاموزید."}, "continueToBrowserExtensionStore": {"message": "آیا میخواهید به فروشگاه افزونه مرورگر ادامه دهید?"}, "continueToBrowserExtensionStoreDesc": {"message": "به دیگران کمک کنید تا بفهمند آیا Bitwarden برایشان مناسب است یا نه. به فروشگاه افزونه مرورگر خود بروید و نظر خود را به اشتراک بگذارید."}, "changeMasterPasswordOnWebConfirmation": {"message": "You can change your master password on the Bitwarden web app."}, "fingerprintPhrase": {"message": "عبارت اثر انگشت", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "عبارت اثر انگشت حساب شما", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "ورود دو مرحله ای"}, "logOut": {"message": "خروج"}, "aboutBitwarden": {"message": "در<PERSON><PERSON><PERSON><PERSON> Bitwarden"}, "about": {"message": "درباره"}, "moreFromBitwarden": {"message": "More from Bitwarden"}, "continueToBitwardenDotCom": {"message": "Continue to bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden برای کسب و کارها"}, "bitwardenAuthenticator": {"message": "تاییدکننده هویت Bitwarden"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden Authenticator allows you to store authenticator keys and generate TOTP codes for 2-step verification flows. Learn more on the bitwarden.com website"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Securely store, manage, and share developer secrets with Bitwarden Secrets Manager. Learn more on the bitwarden.com website."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Create smooth and secure login experiences free from traditional passwords with Passwordless.dev. Learn more on the bitwarden.com website."}, "freeBitwardenFamilies": {"message": "خانواده‌های رایگان Bitwarden"}, "freeBitwardenFamiliesPageDesc": {"message": "You are eligible for Free Bitwarden Families. Redeem this offer today in the web app."}, "version": {"message": "نسخه"}, "save": {"message": "ذخیره"}, "move": {"message": "انتقال"}, "addFolder": {"message": "افزودن پوشه"}, "name": {"message": "نام"}, "editFolder": {"message": "ويرايش پوشه"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "New folder"}, "folderName": {"message": "Folder name"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "حذ<PERSON> پوشه"}, "folders": {"message": "پوشه‌ها"}, "noFolders": {"message": "هیچ پوشه‌ای برای نمایش وجود ندارد."}, "helpFeedback": {"message": "کمک و بازخورد"}, "helpCenter": {"message": "مرکز راهنمایی Bitwarden"}, "communityForums": {"message": "انجمن‌های Bitwarden را کاوش کنید"}, "contactSupport": {"message": "پیام به پشتیبانیBitwarden"}, "sync": {"message": "همگام‌سازی"}, "syncVaultNow": {"message": "همگام‌سازی گاوصندوق"}, "lastSync": {"message": "آخرین همگام‌سازی:"}, "passGen": {"message": "تولید کننده کلمه عبور"}, "generator": {"message": "تو<PERSON>ید کننده", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "به طور خودکار کلمه‌های عبور قوی و منحصر به فرد برای ورود به سیستم خود ایجاد کنید."}, "bitWebVaultApp": {"message": "Bitwarden web app"}, "importItems": {"message": "درون ریزی موارد"}, "select": {"message": "انتخاب"}, "generatePassword": {"message": "تولید کلمه عبور"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "تولید مجدد کلمه عبور"}, "options": {"message": "گزینه‌ها"}, "length": {"message": "طول"}, "include": {"message": "Include", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Include numbers", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Include special characters", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "تعداد کلمات"}, "wordSeparator": {"message": "جداکننده کلمات"}, "capitalize": {"message": "بزرگ کردن", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "شا<PERSON><PERSON> عدد"}, "minNumbers": {"message": "حداقل اعداد"}, "minSpecial": {"message": "حدا<PERSON><PERSON> حرف خاص"}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "جستجوی گاوصندوق"}, "edit": {"message": "ویرایش"}, "view": {"message": "مشاهده"}, "noItemsInList": {"message": "هیچ موردی برای نمایش وجود ندارد."}, "itemInformation": {"message": "اطلاعات مورد"}, "username": {"message": "نام کاربری"}, "password": {"message": "کلمه عبور"}, "totp": {"message": "Authenticator secret"}, "passphrase": {"message": "عبارت عبور"}, "favorite": {"message": "مو<PERSON><PERSON> علاقه"}, "unfavorite": {"message": "حذ<PERSON> از علایق"}, "itemAddedToFavorites": {"message": "به موارد مورد علاقه افزوده شد"}, "itemRemovedFromFavorites": {"message": "از علایق حذف شد"}, "notes": {"message": "یادداشت‌ها"}, "privateNote": {"message": "Private note"}, "note": {"message": "یادداشت"}, "editItem": {"message": "ویرایش مورد"}, "folder": {"message": "پوشه"}, "deleteItem": {"message": "<PERSON><PERSON><PERSON> مورد"}, "viewItem": {"message": "مشاهده مورد"}, "launch": {"message": "راه اندازی"}, "launchWebsite": {"message": "Launch website"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "وب‌سایت"}, "toggleVisibility": {"message": "قابلیت مشاهده را تغییر دهید"}, "manage": {"message": "مدی<PERSON><PERSON>ت"}, "other": {"message": "ساير"}, "unlockMethods": {"message": "Unlock options"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "یک روش بازگشایی برای پایان زمان مجاز تنظیم کنید."}, "unlockMethodNeeded": {"message": "Set up an unlock method in Settings"}, "sessionTimeoutHeader": {"message": "Session timeout"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "سایر گزینه‌ها"}, "rateExtension": {"message": "به این افزونه امتیاز دهید"}, "browserNotSupportClipboard": {"message": "مرورگر شما از کپی کلیپ بورد آسان پشتیبانی نمی‌کند. به جای آن به صورت دستی کپی کنید."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "گاوصندوق شما قفل شده است. برای ادامه هویت خود را تأیید کنید."}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "باز کردن قفل"}, "loggedInAsOn": {"message": "وارد شده با $EMAIL$ در $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "کلمه عبور اصلی نامعتبر است"}, "vaultTimeout": {"message": "متوقف شدن گاو‌صندوق"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "ال<PERSON> قفل شود"}, "lockAll": {"message": "Lock all"}, "immediately": {"message": "بلافاصله"}, "tenSeconds": {"message": "۱۰ ثانیه"}, "twentySeconds": {"message": "۲۰ ثانیه"}, "thirtySeconds": {"message": "۳۰ ثانیه"}, "oneMinute": {"message": "۱ دقیقه"}, "twoMinutes": {"message": "۲ دقیقه"}, "fiveMinutes": {"message": "۵ دقیقه"}, "fifteenMinutes": {"message": "۱۵ دقیقه"}, "thirtyMinutes": {"message": "۳۰ دقیقه"}, "oneHour": {"message": "۱ ساعت"}, "fourHours": {"message": "4 ساعت"}, "onLocked": {"message": "در قفل سیستم"}, "onRestart": {"message": "هنگام راه اندازی مجدد"}, "never": {"message": "هر<PERSON><PERSON>"}, "security": {"message": "امنیت"}, "confirmMasterPassword": {"message": "Confirm master password"}, "masterPassword": {"message": "Master password"}, "masterPassImportant": {"message": "Your master password cannot be recovered if you forget it!"}, "masterPassHintLabel": {"message": "Master password hint"}, "errorOccurred": {"message": "خطایی رخ داده است"}, "emailRequired": {"message": "نشانی ایمیل ضروری است."}, "invalidEmail": {"message": "نشانی ایمیل نامعتبر است."}, "masterPasswordRequired": {"message": "کلمه عبور اصلی ضروری است."}, "confirmMasterPasswordRequired": {"message": "نوشتن مجدد کلمه عبور اصلی ضروری است."}, "masterPasswordMinlength": {"message": "طول کلمه عبور اصلی باید حداقل $VALUE$ کاراکتر باشد.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "کلمه عبور اصلی با تکرار آن مطابقت ندارد."}, "newAccountCreated": {"message": "حساب کاربری جدید شما ساخته شد! حالا می‌توانید وارد شوید."}, "newAccountCreated2": {"message": "Your new account has been created!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "youSuccessfullyLoggedIn": {"message": "شما با موفقیت وارد شدید"}, "youMayCloseThisWindow": {"message": "می‌توانید این پنجره را ببندید"}, "masterPassSent": {"message": "ما یک ایمیل همراه با راهنمای کلمه عبور اصلی برایتان ارسال کردیم."}, "verificationCodeRequired": {"message": "کد تأیید مورد نیاز است."}, "webauthnCancelOrTimeout": {"message": "The authentication was cancelled or took too long. Please try again."}, "invalidVerificationCode": {"message": "کد تأیید نامعتبر است"}, "valueCopied": {"message": " کپی شده", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "ناتوان در پر کردن خودکار مورد انتخاب شده در این صفحه. اطلاعات را کپی و جای‌گذاری کنید."}, "totpCaptureError": {"message": "Unable to scan QR code from the current webpage"}, "totpCaptureSuccess": {"message": "ک<PERSON>ید احراز هویت اضافه شد"}, "totpCapture": {"message": "Scan authenticator QR code from current webpage"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Copy Authenticator key (TOTP)"}, "loggedOut": {"message": "<PERSON>ا<PERSON><PERSON> شد"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "نشست ورود شما منقضی شده است."}, "logIn": {"message": "Log in"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "آیا مطمئنید که می‌خواهید خارج شوید؟"}, "yes": {"message": "بله"}, "no": {"message": "<PERSON><PERSON><PERSON>"}, "location": {"message": "Location"}, "unexpectedError": {"message": "یک خطای غیر منتظره رخ داده است."}, "nameRequired": {"message": "نام ضروری است."}, "addedFolder": {"message": "پوشه اضافه شد"}, "twoStepLoginConfirmation": {"message": "ورود دو مرحله ای باعث می‌شود که حساب کاربری شما با استفاده از یک دستگاه دیگر مانند کلید امنیتی، برنامه احراز هویت، پیامک، تماس تلفنی و یا ایمیل، اعتبار خود را با ایمنی بیشتر اثبات کند. ورود دو مرحله ای می تواند در bitwarden.com فعال شود. آیا می‌خواهید از سایت بازدید کنید؟"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "پوشه ذخیره شد"}, "deleteFolderConfirmation": {"message": "آیا از حذف این پوشه اطمینان دارید؟"}, "deletedFolder": {"message": "پوشه حذف شد"}, "gettingStartedTutorial": {"message": "آغاز نمودن آموزش"}, "gettingStartedTutorialVideo": {"message": "برای یادگیری بیشتر نحوه استفاده از افزونه مرورگر آموزش شروع به کار را تماشا کنید."}, "syncingComplete": {"message": "همگام‌سازی کامل شد"}, "syncingFailed": {"message": "همگام‌سازی شکست خورد"}, "passwordCopied": {"message": "کلمه عبور کپی شد"}, "uri": {"message": "نشانی اینترنتی"}, "uriPosition": {"message": "نشانی اینترنتی $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "نشانی اینترنتی جدید"}, "addDomain": {"message": "افزودن دامنه", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "مورد اضافه شد"}, "editedItem": {"message": "مورد ذخیره شد"}, "deleteItemConfirmation": {"message": "واقعاً می‌خواهید این آیتم را به سطل زباله ارسال کنید؟"}, "deletedItem": {"message": "مورد به زباله‌ها فرستاده شد"}, "overwritePassword": {"message": "بازنویسی کلمه عبور"}, "overwritePasswordConfirmation": {"message": "آیا از بازنویسی بر روی پسورد فعلی مطمئن هستید؟"}, "overwriteUsername": {"message": "بازنویسی نام کاربری"}, "overwriteUsernameConfirmation": {"message": "آیا از بازنویسی نام کاربری فعلی مطمئن هستید؟"}, "searchFolder": {"message": "جستجوی پوشه"}, "searchCollection": {"message": "جستجوی مجموعه"}, "searchType": {"message": "نوع جستجو"}, "noneFolder": {"message": "بدون پوشه", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "درخواست افزودن ورود به سیستم"}, "vaultSaveOptionsTitle": {"message": "Save to vault options"}, "addLoginNotificationDesc": {"message": "در صورتی که موردی در گاوصندوق شما یافت نشد، درخواست افزودن کنید."}, "addLoginNotificationDescAlt": {"message": "Ask to add an item if one isn't found in your vault. Applies to all logged in accounts."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "نمایش کارت‌ها در صفحه برگه"}, "showCardsCurrentTabDesc": {"message": "برای پر کردن خودکار آسان، موارد کارت را در صفحه برگه فهرست کن."}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "نشان دادن هویت در صفحه برگه"}, "showIdentitiesCurrentTabDesc": {"message": "موارد هویتی را در صفحه برگه برای پر کردن خودکار آسان فهرست کن."}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "پاکسازی کلیپ بورد", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "به صورت خودکار، مقادیر کپی شده را از کلیپ بورد پاک کن.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "آ<PERSON><PERSON> Bitwarden باید این کلمه عبور را برایتان بخاطر بسپارد؟"}, "notificationAddSave": {"message": "ذخیره"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "loginSaveConfirmation": {"message": "$ITEMNAME$ saved to Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is saved."}, "loginUpdatedConfirmation": {"message": "$ITEMNAME$ updated in Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is updated."}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "درخواست برای به‌روزرسانی ورود به سیستم موجود"}, "changedPasswordNotificationDesc": {"message": "هنگامی که تغییری در یک وب‌سایت شناسایی شد، درخواست به‌روزرسانی کلمه عبور ورود کن."}, "changedPasswordNotificationDescAlt": {"message": "Ask to update a login's password when a change is detected on a website. Applies to all logged in accounts."}, "enableUsePasskeys": {"message": "برای ذخیره و استفاده از passkey اجازه بگیر"}, "usePasskeysDesc": {"message": "Ask to save new passkeys or log in with passkeys stored in your vault. Applies to all logged in accounts."}, "notificationChangeDesc": {"message": "آیا مایل به به‌روزرسانی این کلمه عبور در Bitwarden هستید؟"}, "notificationChangeSave": {"message": "به‌روزرسانی"}, "notificationUnlockDesc": {"message": "برای پر کردن خودکار گاوصندوق Bitwarden خود را باز کنید."}, "notificationUnlock": {"message": "باز کردن قفل"}, "additionalOptions": {"message": "گزینه‌های اضافی"}, "enableContextMenuItem": {"message": "نمایش گزینه‌های منوی زمینه"}, "contextMenuItemDesc": {"message": "از یک کلیک ثانویه برای دسترسی به تولید کلمه عبور و ورودهای منطبق برای وب سایت استفاده کن."}, "contextMenuItemDescAlt": {"message": "Use a secondary click to access password generation and matching logins for the website. Applies to all logged in accounts."}, "defaultUriMatchDetection": {"message": "بررسی مطابقت نشانی اینترنتی پیش‌فرض", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "هنگام انجام دادن کارهایی مانند پر کردن خودکار، روش پیش‌فرضی را که برای شناسایی ورود نشانی اینترنتی انجام می‌شود انتخاب کنید."}, "theme": {"message": "پوسته"}, "themeDesc": {"message": "تغییر رنگ پوسته برنامه."}, "themeDescAlt": {"message": "Change the application's color theme. Applies to all logged in accounts."}, "dark": {"message": "تاریک", "description": "Dark color"}, "light": {"message": "روشن", "description": "Light color"}, "exportFrom": {"message": "صادرات از"}, "exportVault": {"message": "برون ریزی گاوصندوق"}, "fileFormat": {"message": "فرمت پرونده"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "رمز فایل"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "نوع صادرات"}, "accountRestricted": {"message": "حساب کاربری محدود شده است"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "عدم تطابق \"رمز فایل\" و \"تایید رمز فایل\" با یکدیگر."}, "warning": {"message": "اخطار", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "برون ریزی گاوصندوق را تأیید کنید"}, "exportWarningDesc": {"message": "این برون ریزی شامل داده‌های گاوصندوق در یک قالب رمزنگاری نشده است. شما نباید آن را از طریق یک راه ارتباطی نا امن (مثل ایمیل) ذخیره یا ارسال کنید. به محض اینکه کارتان با آن تمام شد، آن را حذف کنید."}, "encExportKeyWarningDesc": {"message": "این برون ریزی با استفاده از کلید رمزگذاری حساب شما، اطلاعاتتان را رمزگذاری می کند. اگر زمانی کلید رمزگذاری حساب خود را بچرخانید، باید دوباره خروجی بگیرید، چون قادر به رمزگشایی این پرونده برون ریزی نخواهید بود."}, "encExportAccountWarningDesc": {"message": "کلیدهای رمزگذاری حساب برای هر حساب کاربری Bitwarden منحصر به فرد است، بنابراین نمی‌توانید برون ریزی رمزگذاری شده را به حساب دیگری وارد کنید."}, "exportMasterPassword": {"message": "کلمه عبور اصلی خود را برای برون ریزی داده‌های گاوصندوقتان وارد کنید."}, "shared": {"message": "اشتراک گذاری شد"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "انتقال به سازمان"}, "movedItemToOrg": {"message": "$ITEMNAME$ منتقل شد به $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "سازمانی را انتخاب کنید که می‌خواهید این مورد را به آن منتقل کنید. انتقال به یک سازمان، مالکیت مورد را به آن سازمان منتقل می‌کند. پس از انتقال این مورد، دیگر مالک مستقیم آن نخواهید بود."}, "learnMore": {"message": "بیشتر بدانید"}, "authenticatorKeyTotp": {"message": "کلید احراز هویت (TOTP)"}, "verificationCodeTotp": {"message": "کد تأیید (TOTP)"}, "copyVerificationCode": {"message": "ک<PERSON>ی کد تأیید"}, "attachments": {"message": "پیوست ها"}, "deleteAttachment": {"message": "حذ<PERSON> پیوست"}, "deleteAttachmentConfirmation": {"message": "آیا از پاک کردن این پیوست مطمئن هستید؟"}, "deletedAttachment": {"message": "پیوست حذف شد"}, "newAttachment": {"message": "افزودن پیوست جدید"}, "noAttachments": {"message": "بدون پیوست."}, "attachmentSaved": {"message": "پیوست ذخیره شد"}, "file": {"message": "پرونده"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "ﺍﻧﺘﺨﺎﺏ یک ﭘﺮﻭﻧﺪﻩ"}, "maxFileSize": {"message": "بیشترین حجم پرونده ۵۰۰ مگابایت است."}, "featureUnavailable": {"message": "ویژگی موجود نیست"}, "encryptionKeyMigrationRequired": {"message": "انتقال کلید رمزگذاری مورد نیاز است. لطفاً از طریق گاوصندوق وب وارد شوید تا کلید رمزگذاری خود را به روز کنید."}, "premiumMembership": {"message": "عضویت پرمیوم"}, "premiumManage": {"message": "مدی<PERSON><PERSON>ت عضویت"}, "premiumManageAlert": {"message": "شما می‌توانید عضویت خود را در نسخه وب گاوصندوق در bitwarden.com مدیریت کنید. آیا مایل به دیدن وب‌سایت هستید؟"}, "premiumRefresh": {"message": "نوسازی عضویت"}, "premiumNotCurrentMember": {"message": "شما در حال حاظر کاربر پرمیوم نیستید."}, "premiumSignUpAndGet": {"message": "برای عضویت پرمیوم ثبت نام کنید و دریافت کنید:"}, "ppremiumSignUpStorage": {"message": "۱ گیگابایت فضای ذخیره سازی رمزگذاری شده برای پیوست های پرونده."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "گزینه های ورود اضافی دو مرحله ای مانند YubiKey و Duo."}, "ppremiumSignUpReports": {"message": "گزارش‌های بهداشت رمز عبور، سلامت حساب و نقض داده‌ها برای ایمن نگهداشتن گاوصندوق شما."}, "ppremiumSignUpTotp": {"message": "تولید کننده کد تأیید (2FA) از نوع TOTP برای ورودهای در گاوصندوقتان."}, "ppremiumSignUpSupport": {"message": "اولویت پشتیبانی از مشتری."}, "ppremiumSignUpFuture": {"message": "تمام ویژگی‌های پرمیوم آینده. به زودی بیشتر!"}, "premiumPurchase": {"message": "<PERSON><PERSON><PERSON><PERSON> پرمیوم"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "شما یک عضو پرمیوم هستید!"}, "premiumCurrentMemberThanks": {"message": "برای حمایتتان از Bitwarden سپاسگزاریم."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "تمامش فقط $PRICE$ در سال!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "نوسازی کامل شد"}, "enableAutoTotpCopy": {"message": "TOTP را به صورت خودکار کپی کن"}, "disableAutoTotpCopyDesc": {"message": "اگر یک ورود دارای یک کلید احراز هویت باشد، هنگام پر کردن خودکار ورود، کد تأیید TOTP را در کلیپ بورد خود کپی کن."}, "enableAutoBiometricsPrompt": {"message": "درخواست بیومتریک هنگام راه اندازی"}, "premiumRequired": {"message": "در نسخه پرمیوم کار می‌کند"}, "premiumRequiredDesc": {"message": "برای استفاده از این ویژگی عضویت پرمیوم لازم است."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "ایمیل تأیید به $EMAIL$ ارسال شد.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "کلید امنیتی خود را وارد پورت USB رایانه کنید، اگر دکمه ای دارد آن را بفشارید."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "تأیید اعتبار در WebAuthn"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "ورود به سیستم در دسترس نیست"}, "noTwoStepProviders": {"message": "ورود دو مرحله‌ای برای این حساب فعال است، با این حال، هیچ یک از ارائه‌دهندگان دو مرحله‌ای پیکربندی شده توسط این مرورگر وب پشتیبانی نمی‌شوند."}, "noTwoStepProviders2": {"message": "لطفاً از یک مرورگر وب پشتیبانی شده (مانند کروم) استفاده کنید و یا ارائه دهندگان اضافی را که از مرورگر وب بهتر پشتیانی می‌کنند را اضافه کنید (مانند یک برنامه احراز هویت)."}, "twoStepOptions": {"message": "گزینه‌های ورود دو مرحله‌ای"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "دسترسی به تمامی ارائه‌دهندگان دو مرحله‌ای را از دست داده‌اید؟ از کد بازیابی خود برای غیرفعال‌سازی ارائه‌دهندگان دو مرحله‌ای از حسابتان استفاده کنید."}, "recoveryCodeTitle": {"message": "کد بازیابی"}, "authenticatorAppTitle": {"message": "برنامه احراز هویت"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP Security Key"}, "yubiKeyDesc": {"message": "از یک YubiKey برای دسترسی به حسابتان استفاده کنید. همراه با دستگاه‌های YubiKey 4 ،4 Nano ،NEO کار می‌کند."}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "از Duo Security با استفاده از برنامه تلفن همراه، پیامک، تماس تلفنی یا کلید امنیتی U2F برای تأیید سازمان خود استفاده کنید.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn\n"}, "webAuthnDesc": {"message": "برای دسترسی به حساب خود از هر کلید امنیتی فعال شده WebAuthn استفاده کنید."}, "emailTitle": {"message": "ایمیل"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "selfHostedEnvironment": {"message": "<PERSON><PERSON><PERSON><PERSON> خود میزبان"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "م<PERSON><PERSON><PERSON> سفارشی"}, "baseUrl": {"message": "نشانی اینترنتی سرور"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "نشانی API سرور"}, "webVaultUrl": {"message": "نشانی اینترنتی سرور گاوصندوق وب"}, "identityUrl": {"message": "نشانی سرور شناسایی"}, "notificationsUrl": {"message": "نشانی سرور اعلان‌ها"}, "iconsUrl": {"message": "نشانی سرور آیکون ها"}, "environmentSaved": {"message": "نشانی‌های اینترنتی محیط ذخیره شد"}, "showAutoFillMenuOnFormFields": {"message": "Show autofill menu on form fields", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Turn off your browser's built in password manager settings to avoid conflicts."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "ویرایش تنظیمات مرورگر."}, "autofillOverlayVisibilityOff": {"message": "خاموش", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "When autofill icon is selected", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "پر کردن خودکار هنگام بارگذاری صفحه"}, "enableAutoFillOnPageLoadDesc": {"message": "اگر یک فرم ورودی شناسایی شد، وقتی صفحه وب بارگذاری شد، به صورت خودکار پر شود."}, "experimentalFeature": {"message": "وب‌سایت‌های در معرض خطر یا نامعتبر می‌توانند از پر کردن خودکار در بارگذاری صفحه سوء استفاده کنند."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "درباره پر کردن خودکار بیشتر بدانید"}, "defaultAutoFillOnPageLoad": {"message": "تنظیم تکمیل خودکار پیش‌فرض برای موارد ورود به سیستم"}, "defaultAutoFillOnPageLoadDesc": {"message": "می‌توانید پر کردن خودکار هنگام بارگیری صفحه را برای موارد ورود به سیستم از نمای ویرایش مورد خاموش کنید."}, "itemAutoFillOnPageLoad": {"message": "پر کردن خودکار بارگذاری صفحه (درصورت فعال بودن در گزینه ها)"}, "autoFillOnPageLoadUseDefault": {"message": "استفاده از تنظیمات پیش‌فرض"}, "autoFillOnPageLoadYes": {"message": "پر کردن خودکار هنگام بارگذاری صفحه"}, "autoFillOnPageLoadNo": {"message": "هنگام بارگذاری صفحه پر کردن خودکار انجام نده"}, "commandOpenPopup": {"message": "باز کردن پنجره گاوصندوق"}, "commandOpenSidebar": {"message": "باز کردن گاوصندوق در نوار کناری"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "یک کلمه عبور تصادفی جدید ایجاد کنید و آن را در کلیپ بورد کپی کنید"}, "commandLockVaultDesc": {"message": "قفل گاوصندوق"}, "customFields": {"message": "فیلدهای سفارشی"}, "copyValue": {"message": "کپی مقدار"}, "value": {"message": "مقدار"}, "newCustomField": {"message": "فی<PERSON><PERSON> سفار<PERSON>ی جدید"}, "dragToSort": {"message": "برای مرتب‌سازی بکشید"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "متن"}, "cfTypeHidden": {"message": "م<PERSON><PERSON><PERSON>"}, "cfTypeBoolean": {"message": "من<PERSON>قی"}, "cfTypeCheckbox": {"message": "Checkbox"}, "cfTypeLinked": {"message": "پیوند شده", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "مقدار پیوند شده", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "در خارج از پنجره پاپ آپ کلیک کنید که ایمیل‌تان بررسی شود برای کد تأییدیه که باعث می‌شود این پنجره بسته شود. آیا می‌خواهید این پاپ آپ را در یک پنجره جدید باز کنید تا آن را نبندید؟"}, "popupU2fCloseMessage": {"message": "این مرورگر نمی‌تواند درخواستهای U2F را در این پنجره پاپ آپ پردازش کند. آیا می‌خواهید این پنجره را در یک پنجره جدید باز کنید تا بتوانید با استفاده از U2F وارد شوید؟"}, "enableFavicon": {"message": "نمایش نمادهای وب‌سایت"}, "faviconDesc": {"message": "یک تصویر قابل تشخیص در کنار هر ورود نشان دهید."}, "faviconDescAlt": {"message": "Show a recognizable image next to each login. Applies to all logged in accounts."}, "enableBadgeCounter": {"message": "نمایش شمارنده نشان"}, "badgeCounterDesc": {"message": "تعداد ورود به صفحه وب فعلی را مشخص کن."}, "cardholderName": {"message": "نام صاحب کارت"}, "number": {"message": "شماره"}, "brand": {"message": "نام تجاری"}, "expirationMonth": {"message": "ماه انقضاء"}, "expirationYear": {"message": "سال انقضاء"}, "expiration": {"message": "انقضاء"}, "january": {"message": "ژانویه"}, "february": {"message": "فوریه"}, "march": {"message": "مارس"}, "april": {"message": "آوریل"}, "may": {"message": "مِی"}, "june": {"message": "ژوئن"}, "july": {"message": "جو<PERSON><PERSON><PERSON>"}, "august": {"message": "آگوست‌"}, "september": {"message": "سپتامبر"}, "october": {"message": "اکتبر"}, "november": {"message": "نوامبر"}, "december": {"message": "دسامبر"}, "securityCode": {"message": "کد امنیتی"}, "ex": {"message": "مثال."}, "title": {"message": "عنوان"}, "mr": {"message": "آقا"}, "mrs": {"message": "خانم"}, "ms": {"message": "بانو"}, "dr": {"message": "د<PERSON><PERSON>ر"}, "mx": {"message": "عنوان"}, "firstName": {"message": "نام"}, "middleName": {"message": "نام میانی"}, "lastName": {"message": "نام خانوادگی"}, "fullName": {"message": "نام کامل"}, "identityName": {"message": "نام هویت"}, "company": {"message": "شرکت"}, "ssn": {"message": "<PERSON><PERSON> مل<PERSON>"}, "passportNumber": {"message": "شماره گذرنامه"}, "licenseNumber": {"message": "شماره گواهی‌نامه"}, "email": {"message": "ایمیل"}, "phone": {"message": "تلفن"}, "address": {"message": "نشانی"}, "address1": {"message": "نشانی ۱"}, "address2": {"message": "نشانی ۲"}, "address3": {"message": "نشانی ۳"}, "cityTown": {"message": "شهر / شهرک"}, "stateProvince": {"message": "ایالت / استان"}, "zipPostalCode": {"message": "ک<PERSON> پستی"}, "country": {"message": "کشور"}, "type": {"message": "نوع"}, "typeLogin": {"message": "ورود"}, "typeLogins": {"message": "ورودها"}, "typeSecureNote": {"message": "یادداشت امن"}, "typeCard": {"message": "کارت"}, "typeIdentity": {"message": "هویت"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "$TYPE$ جدید", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "ویرایش $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "View $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "تاریخچه کلمه عبور"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "بازگشت"}, "collections": {"message": "مجموعه‌ها"}, "nCollections": {"message": "$COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "مورد علاقه‌ها"}, "popOutNewWindow": {"message": "به یک پنجره جدید پاپ بزن"}, "refresh": {"message": "تازه کردن"}, "cards": {"message": "کارت‌ها"}, "identities": {"message": "هویت‌ها"}, "logins": {"message": "ورودها"}, "secureNotes": {"message": "یادداشت‌های امن"}, "sshKeys": {"message": "SSH Keys"}, "clear": {"message": "پاک کردن", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "بررسی کنید که آیا کلمه عبور افشا شده است."}, "passwordExposed": {"message": "این کلمه عبور $VALUE$ بار در رخنه داده‌ها افشا شده است. باید آن را تغییر دهید.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "این کلمه عبور در هیچ رخنه داده ای شناخته نشده است. استفاده از آن باید ایمن باشد."}, "baseDomain": {"message": "دامنه پایه", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Base domain (recommended)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "نام دامنه", "description": "Domain name. Ex. website.com"}, "host": {"message": "میزبان", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "دق<PERSON><PERSON>"}, "startsWith": {"message": "شروع می‌شود با"}, "regEx": {"message": "عبارت منظم", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "تشخیص مطابقت", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "بررسی مطابقت پیش‌فرض", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "گزینه های تبدیل"}, "toggleCurrentUris": {"message": "تغییر وضعیت نشانی های اینترنتی فعلی", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "نشانی اینترنتی فعلی", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "سازمان", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "انواع"}, "allItems": {"message": "تمام موارد"}, "noPasswordsInList": {"message": "هیچ کلمه عبوری برای فهرست کردن وجود ندارد."}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "<PERSON><PERSON><PERSON>"}, "default": {"message": "پیش‌<PERSON><PERSON>ض"}, "dateUpdated": {"message": "به‌روزرسانی شد", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "ایج<PERSON> شد", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "کلمه عبور به‌روزرسانی شد", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "آیا جداً می‌خواهید از گزینه \"هرگز\" استفاده کنید؟ تنظیم کردن گزینه قفل به \"هرگز\" کلیدهای رمزنگاری گاوصندوقتان را بر روی دستگاه شما ذخیره خواهد کرد. اگر از این گزینه استفاده می‌کنید باید اطمینان داشته باشید که دستگاه شما کاملا محافظت شده است."}, "noOrganizationsList": {"message": "شما به هیچ سازمانی تعلق ندارید. سازمان‌ها به شما اجازه می‌دهند تا داده‌های خود را با کاربران دیگر به صورت امن به اشتراک بگذارید."}, "noCollectionsInList": {"message": "هیچ مجموعه ای برای لیست کردن وجود ندارد."}, "ownership": {"message": "مال<PERSON><PERSON>ت"}, "whoOwnsThisItem": {"message": "چه کسی مالک این مورد است؟"}, "strong": {"message": "قوی", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "<PERSON>و<PERSON>", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "ضعیف", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "کلمه عبور اصلی ضعیف"}, "weakMasterPasswordDesc": {"message": "کلمه عبور اصلی که شما انتخاب کرده اید ضعیف است. شما باید یک کلمه عبور اصلی قوی انتخاب کنید (یا یک عبارت عبور) تا به درستی از اکانت Bitwarden خود محافظت کنید. آیا مطمئن هستید می‌خواهید از این کلمه عبور اصلی استفاده کنید؟ "}, "pin": {"message": "پین", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "باز کردن با پین"}, "setYourPinTitle": {"message": "Set PIN"}, "setYourPinButton": {"message": "Set PIN"}, "setYourPinCode": {"message": "کد پین خود را برای باز کردن Bitwarden تنظیم کنید. اگر به طور کامل از برنامه خارج شوید، تنظیمات پین شما از بین می‌رود."}, "setYourPinCode1": {"message": "Your PIN will be used to unlock Bitwarden instead of your master password. Your PIN will reset if you ever fully log out of Bitwarden."}, "pinRequired": {"message": "کد پین الزامیست."}, "invalidPin": {"message": "کد پین معتبر نیست."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Too many invalid PIN entry attempts. Logging out."}, "unlockWithBiometrics": {"message": "با استفاده از بیومتریک باز کنید"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "awaitDesktop": {"message": "در انتظار تأیید از دسکتاپ"}, "awaitDesktopDesc": {"message": "لطفاً استفاده از بیومتریک را در برنامه دسکتاپ Bitwarden تأیید کنید تا بیومتریک را برای مرورگر فعال کنید."}, "lockWithMasterPassOnRestart": {"message": "در زمان شروع مجدد، با کلمه عبور اصلی قفل کن"}, "lockWithMasterPassOnRestart1": {"message": "Require master password on browser restart"}, "selectOneCollection": {"message": "شما باید حدا<PERSON><PERSON> یک مجموعه را انتخاب کنید."}, "cloneItem": {"message": "مور<PERSON> شبیه"}, "clone": {"message": "شبیه سازی"}, "passwordGenerator": {"message": "Password generator"}, "usernameGenerator": {"message": "Username generator"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "Use this password"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "عمل متوقف شدن گاو‌صندوق"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "newCustomizationOptionsCalloutTitle": {"message": "New customization options"}, "newCustomizationOptionsCalloutContent": {"message": "Customize your vault experience with quick copy actions, compact mode, and more!"}, "newCustomizationOptionsCalloutLink": {"message": "View all Appearance settings"}, "lock": {"message": "قفل", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "زباله‌ها", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "جستجوی زباله‌ها"}, "permanentlyDeleteItem": {"message": "حذف دائمی مورد"}, "permanentlyDeleteItemConfirmation": {"message": "مطمئن هستید که می‌خواهید این مورد را برای همیشه پاک کنید؟"}, "permanentlyDeletedItem": {"message": "مورد برای همیشه حذف شد"}, "restoreItem": {"message": "بازیا<PERSON>ی مورد"}, "restoredItem": {"message": "مورد بازیابی شد"}, "alreadyHaveAccount": {"message": "Already have an account?"}, "vaultTimeoutLogOutConfirmation": {"message": "خروج از سیستم، تمام دسترسی ها به گاو‌صندوق شما را از بین می‌برد و نیاز به احراز هویت آنلاین پس از مدت زمان توقف دارد. آیا مطمئن هستید که می‌خواهید از این تنظیمات استفاده کنید؟"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "ت<PERSON><PERSON><PERSON>د عمل توقف"}, "autoFillAndSave": {"message": "پر کردن خودکار و ذخیره"}, "fillAndSave": {"message": "پرکردن و ذخیره"}, "autoFillSuccessAndSavedUri": {"message": "مور<PERSON> خودکار پر شد و نشانی اینترنتی ذخیره شد"}, "autoFillSuccess": {"message": "مو<PERSON><PERSON> خودکار پر شد"}, "insecurePageWarning": {"message": "هشدار: این یک صفحه HTTP ناامن است و هر اطلاعاتی که ارسال می‌کنید می‌تواند توسط دیگران دیده شود و تغییر کند. این ورود در ابتدا در یک صفحه امن (HTTPS) ذخیره شد."}, "insecurePageWarningFillPrompt": {"message": "آیا هنوز می‌خواهید این ورود را پر کنید؟"}, "autofillIframeWarning": {"message": "فرم توسط دامنه ای متفاوت از نشانی اینترنتی ورود به سیستم ذخیره شده شما میزبانی می‌شود. به هر حال برای پر کردن خودکار، تأیید را انتخاب کنید یا برای توقف، لغو را انتخاب کنید."}, "autofillIframeWarningTip": {"message": "برای جلوگیری از این هشدار در آینده، این نشانی اینترنتی، $HOSTNAME$، را در مورد ورود Bitwarden خود برای این سایت ذخیره کنید.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "تنظیم کلمه عبور اصلی"}, "currentMasterPass": {"message": "کلمه عبور اصلی فعلی"}, "newMasterPass": {"message": "کلمه عبور اصلی جدید"}, "confirmNewMasterPass": {"message": "ت<PERSON><PERSON><PERSON>د کلمه عبور اصلی جدید"}, "masterPasswordPolicyInEffect": {"message": "یک یا چند سیاست سازمانی برای تأمین شرایط زیر به کلمه عبور اصلی شما احتیاج دارد:"}, "policyInEffectMinComplexity": {"message": "حداقل نمره پیچیدگی $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "حداقل طول $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "حاوی یک یا چند کاراکتر بزرگ"}, "policyInEffectLowercase": {"message": "حاوی یک یا چند کاراکتر کوچک"}, "policyInEffectNumbers": {"message": "حاوی یک یا چند عدد بیشتر"}, "policyInEffectSpecial": {"message": "حاوی یک یا چند کاراکتر خاص زیر است $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "کلمه عبور اصلی جدید شما از شرایط سیاست پیروی نمی‌کند."}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "لغو اشتراک"}, "atAnyTime": {"message": "در هر زمان."}, "byContinuingYouAgreeToThe": {"message": "با ادامه دادن، شما موافقت می‌کنید که"}, "and": {"message": "و"}, "acceptPolicies": {"message": "با علامت زدن این کادر با موارد زیر موافقت می‌کنید:"}, "acceptPoliciesRequired": {"message": "شرایط خدمات و سیاست حفظ حریم خصوصی تأیید نشده است."}, "termsOfService": {"message": "شرایط استفاده از خدمات"}, "privacyPolicy": {"message": "سیاست حفظ حریم خصوصی"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "اشاره به کلمه عبور شما نمی‌تواند همان کلمه عبور شما باشد."}, "ok": {"message": "تأیید"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "تأیید همگام‌سازی دسکتاپ"}, "desktopIntegrationVerificationText": {"message": "لطفاً تأیید کنید که برنامه دسکتاپ این اثر انگشت را نشان می‌دهد:"}, "desktopIntegrationDisabledTitle": {"message": "ادغام مرورگر فعال نیست"}, "desktopIntegrationDisabledDesc": {"message": "ادغام مرورگر در برنامه دسکتاپ Bitwarden فعال نیست. لطفاً آن را در تنظیمات موجود در برنامه دسکتاپ فعال کنید."}, "startDesktopTitle": {"message": "برنامه دسکتاپ Bitwarden را شروع کنید"}, "startDesktopDesc": {"message": "قبل از استفاده از این عملکرد، برنامه دسکتاپ Bitwarden باید شروع شود."}, "errorEnableBiometricTitle": {"message": "بیومتریک فعال نیست"}, "errorEnableBiometricDesc": {"message": "فعالیت توسط برنامه دسکتاپ لغو شد"}, "nativeMessagingInvalidEncryptionDesc": {"message": "برنامه دسکتاپ کانال ارتباط امن را نامعتبر کرد. لطفاً این عملیات را دوباره امتحان کنید"}, "nativeMessagingInvalidEncryptionTitle": {"message": "ارتباط دسکتاپ قطع شد"}, "nativeMessagingWrongUserDesc": {"message": "برنامه دسکتاپ به یک حساب دیگر وارد شده است. لطفاً اطمینان حاصل کنید که هر دو برنامه به یک حساب وارد شده اند."}, "nativeMessagingWrongUserTitle": {"message": "عدم مطاب<PERSON>ت حساب کاربری"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "بیومتریک برپا نشده"}, "biometricsNotEnabledDesc": {"message": "بیومتریک مرورگر ابتدا نیاز به فعالسازی بیومتریک دسکتاپ در تنظیمات دارد."}, "biometricsNotSupportedTitle": {"message": "بیومتریک پشتیبانی نمی‌شود"}, "biometricsNotSupportedDesc": {"message": "بیومتریک مرورگر در این دستگاه پشتیبانی نمی‌شود."}, "biometricsNotUnlockedTitle": {"message": "کار<PERSON>ر قفل یا خارج شد"}, "biometricsNotUnlockedDesc": {"message": "Please unlock this user in the desktop application and try again."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "زیست‌سنجی ناموفق بود"}, "biometricsFailedDesc": {"message": "زیست‌سنجی نمی‌تواند انجام شود، استفاده از کلمه عبور اصلی یا خروج را در نظر بگیرید. اگر این مشکل ادامه یافت لطفاً با پشتیبانی Bitwarden تماس بگیرید."}, "nativeMessaginPermissionErrorTitle": {"message": "مجوز ارائه نشده است"}, "nativeMessaginPermissionErrorDesc": {"message": "بدون اجازه برای ارتباط با برنامه دسکتاپ Bitwarden، نمی‌توانیم بیومتریک را در افزونه مرورگر ارائه دهیم. لطفاً دوباره تلاش کنید."}, "nativeMessaginPermissionSidebarTitle": {"message": "خطای درخواست مجوز"}, "nativeMessaginPermissionSidebarDesc": {"message": "این عمل را نمی‌توان در نوار کناری انجام داد، لطفاً اقدام را در پنجره بازشو بازخوانی کنید."}, "personalOwnershipSubmitError": {"message": "به دلیل سیاست پرمیوم، برای ذخیره موارد در گاوصندوق شخصی خود محدود شده اید. گزینه مالکیت را به یک سازمان تغییر دهید و مجموعه های موجود را انتخاب کنید."}, "personalOwnershipPolicyInEffect": {"message": "سیاست سازمانی بر تنظیمات مالکیت شما تأثیر می‌گذارد."}, "personalOwnershipPolicyInEffectImports": {"message": "An organization policy has blocked importing items into your individual vault."}, "domainsTitle": {"message": "Domains", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "دامنه های مستثنی"}, "excludedDomainsDesc": {"message": "Bitwarden برای ذخیره جزئیات ورود به سیستم این دامنه ها سوال نمی‌کند. برای اینکه تغییرات اعمال شود باید صفحه را تازه کنید."}, "excludedDomainsDescAlt": {"message": "Bitwarden will not ask to save login details for these domains for all logged in accounts. You must refresh the page for changes to take effect."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ دامنه معتبری نیست", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "ارسال", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "متن"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "پرونده"}, "allSends": {"message": "همه ارسال ها", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "منقضی شده"}, "passwordProtected": {"message": "محافظت ‌شده با کلمه عبور"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "پیوند ارسال را کپی کن", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "حذ<PERSON> کل<PERSON>ه عبور"}, "delete": {"message": "<PERSON><PERSON><PERSON>"}, "removedPassword": {"message": "کلمه عبور حذف شد"}, "deletedSend": {"message": "ارسال حذف شد", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "ارسال پیوند", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "غیرفعال شد"}, "removePasswordConfirmation": {"message": "مطمئنید که می‌خواهید کلمه عبور حذف شود؟"}, "deleteSend": {"message": "ارسال حذف شد", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "آیا مطمئن هستید که می‌خواهید این ارسال را حذف کنید؟", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "ویرایش ارسال", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "تاریخ حذف"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "تاريخ انقضاء"}, "oneDay": {"message": "۱ روز"}, "days": {"message": "$DAYS$ روز", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "سفار<PERSON>ی"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "ارسا<PERSON> جدید", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "کلمه عبور جدید"}, "sendDisabled": {"message": "ارسال حذف شد", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "به دلیل سیاست سازمانی، شما فقط می‌توانید ارسال موجود را حذف کنید.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "ارسال ساخته شد", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "ارسال ذخیره شد", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "برای انتخاب پرونده، پسوند را در نوار کناری باز کنید (در صورت امکان) یا با کلیک بر روی این بنر پنجره جدیدی باز کنید."}, "sendFirefoxFileWarning": {"message": "برای انتخاب یک پرونده با استفاده از Firefox، افزونه را در نوار کناری باز کنید یا با کلیک بر روی این بنر پنجره جدیدی باز کنید."}, "sendSafariFileWarning": {"message": "برای انتخاب پرونده ای با استفاده از Safari، با کلیک روی این بنر پنجره جدیدی باز کنید."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "قبل از اینکه شروع کنی"}, "expirationDateIsInvalid": {"message": "تاریخ انقضاء ارائه شده معتبر نیست."}, "deletionDateIsInvalid": {"message": "تاریخ حذف ارائه شده معتبر نیست."}, "expirationDateAndTimeRequired": {"message": "تاریخ انقضاء و زمان لازم است."}, "deletionDateAndTimeRequired": {"message": "تاریخ و زمان حذف لازم است."}, "dateParsingError": {"message": "هنگام ذخیره حذف و تاریخ انقضاء شما خطایی روی داد."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "درخواست مجدد کلمه عبور اصلی"}, "passwordConfirmation": {"message": "ت<PERSON><PERSON><PERSON>د کلمه عبور اصلی"}, "passwordConfirmationDesc": {"message": "این عمل محافظت می‌شود. برای ادامه، لطفاً کلمه عبور اصلی خود را دوباره وارد کنید تا هویت‌تان را تأیید کنید."}, "emailVerificationRequired": {"message": "ت<PERSON><PERSON>ید ایمیل لازم است"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerificationRequiredDesc": {"message": "برای استفاده از این ویژگی باید ایمیل خود را تأیید کنید. می‌توانید ایمیل خود را در گاوصندوق وب تأیید کنید."}, "updatedMasterPassword": {"message": "کلمه عبور اصلی به‌روز شد"}, "updateMasterPassword": {"message": "به‌روزرسانی کلمه عبور اصلی"}, "updateMasterPasswordWarning": {"message": "کلمه عبور اصلی شما اخیراً توسط سرپرست سازمان‌تان تغییر کرده است. برای دسترسی به گاوصندوق، باید همین حالا کلمه عبور اصلی خود را به‌روز کنید. در صورت ادامه، شما از نشست فعلی خود خارج می‌شوید و باید دوباره وارد سیستم شوید. نشست فعال در دستگاه های دیگر ممکن است تا یک ساعت همچنان فعال باقی بمانند."}, "updateWeakMasterPasswordWarning": {"message": "کلمه عبور اصلی شما با یک یا چند سیاست سازمان‌تان مطابقت ندارد. برای دسترسی به گاوصندوق، باید همین حالا کلمه عبور اصلی خود را به‌روز کنید. در صورت ادامه، شما از نشست فعلی خود خارج می‌شوید و باید دوباره وارد سیستم شوید. نشست فعال در دستگاه های دیگر ممکن است تا یک ساعت همچنان فعال باقی بمانند."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "ثبت نام خودکار"}, "resetPasswordAutoEnrollInviteWarning": {"message": "این سازمان دارای سیاست سازمانی ای است که به طور خودکار شما را در بازنشانی کلمه عبور ثبت نام می‌کند. این ثبت نام به مدیران سازمان اجازه می‌دهد تا کلمه عبور اصلی شما را تغییر دهند."}, "selectFolder": {"message": "پوشه را انتخاب کنید..."}, "noFoldersFound": {"message": "هیچ پوشه‌ای پیدا نشد", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "تا<PERSON>ید لازم است", "description": "Default title for the user verification dialog."}, "hours": {"message": "ساعت"}, "minutes": {"message": "دق<PERSON><PERSON>ه"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "سیاست‌های سازمانتان بر مهلت زمانی گاوصندوق شما تأثیر می‌گذارد. حداکثر زمان مجاز گاوصندوق $HOURS$ ساعت و $MINUTES$ دقیقه است", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "سیاست‌های سازمانتان بر مهلت زمانی گاوصندوق شما تأثیر می‌گذارد. حداکثر زمان مجاز گاوصندوق $HOURS$ ساعت و $MINUTES$ دقیقه است. عملگر مهلت زمانی گاوصندوق شما روی $ACTION$ تنظیم شده است.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "سباست‌های سازمان شما، عملگر زمان‌بندی گاوصندوق شما را روی $ACTION$ تنظیم کرده است.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "مهلت زمانی شما بیش از محدودیت های تعیین شده توسط سازمانتان است."}, "vaultExportDisabled": {"message": "برون ریزی گاوصندوق غیرفعال شده است"}, "personalVaultExportPolicyInEffect": {"message": "یک یا چند سیاست سازمان از برون ریزی گاوصندوق شخصی شما جلوگیری می‌کند."}, "copyCustomFieldNameInvalidElement": {"message": "شناسایی عنصر فرم معتبر امکان پذیر نیست. به جای آن سعی کنید HTML را بررسی کنید."}, "copyCustomFieldNameNotUnique": {"message": "شناسه منحصر به فردی یافت نشد."}, "convertOrganizationEncryptionDesc": {"message": "$ORGANIZATION$ در حال استفاده از SSO با یک سرور کلید خود میزبان است. برای ورود اعضای این سازمان دیگر نیازی به کلمه عبور اصلی نیست.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "leaveOrganization": {"message": "ترک سازمان"}, "removeMasterPassword": {"message": "حذ<PERSON> <PERSON><PERSON><PERSON>ه عبور اصلی"}, "removedMasterPassword": {"message": "کلمه عبور اصلی حذف شد"}, "leaveOrganizationConfirmation": {"message": "آيا مطمئن هستيد که می خواهيد سازمان های انتخاب شده را ترک کنيد؟"}, "leftOrganization": {"message": "شما از سازمان خارج شده اید."}, "toggleCharacterCount": {"message": "تغییر تعداد کاراکترها"}, "sessionTimeout": {"message": "زمان نشست شما به پایان رسید. لطفاً برگردید و دوباره وارد سیستم شوید."}, "exportingPersonalVaultTitle": {"message": "برون ریزی گاو‌صندوق شخصی"}, "exportingIndividualVaultDescription": {"message": "فقط موارد شخصی گاوصندوق مرتبط با $EMAIL$ برون ریزی خواهند شد. موارد گاوصندوق سازمان شامل نخواهد شد. فقط اطلاعات مورد گاوصندوق برون ریزی خواهد شد و شامل تاریخچه کلمه عبور مرتبط یا پیوست نمی‌شود.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "خطا"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "ایجاد نام کاربری"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "به علاوه نشانی ایمیل داده شده", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "از قابلیت های آدرس دهی فرعی ارائه دهنده ایمیل خود استفاده کنید."}, "catchallEmail": {"message": "دریافت همه ایمیل‌ها"}, "catchallEmailDesc": {"message": "از صندوق ورودی پیکربندی شده دامنه خود استفاده کنید."}, "random": {"message": "تصادفی"}, "randomWord": {"message": "کلمه تصادفی"}, "websiteName": {"message": "نام وب‌سایت"}, "service": {"message": "سرویس"}, "forwardedEmail": {"message": "نام مستعار ایمیل فوروارد شده"}, "forwardedEmailDesc": {"message": "یک نام مستعار ایمیل با یک سرویس ارسال خارجی ایجاد کنید."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generated by <PERSON><PERSON><PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "دامنه $SERVICENAME$ نامعتبر.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "آدرس $SERVICENAME$ نامعتبر.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "خطای $SERVICENAME$ نامعلومی رخ داد.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "نام میزبان", "description": "Part of a URL."}, "apiAccessToken": {"message": "توکن دسترسی API"}, "apiKey": {"message": "کلید API"}, "ssoKeyConnectorError": {"message": "خطای رابط کلید: مطم<PERSON>ن شوید که رابط کلید در دسترس است و به درستی کار می‌کند."}, "premiumSubcriptionRequired": {"message": "اشتراک پرمیوم نیاز است"}, "organizationIsDisabled": {"message": "سازمان از کار افتاده است."}, "disabledOrganizationFilterError": {"message": "موارد موجود در سازمان‌های غیرفعال، قابل دسترسی نیستند. برای دریافت کمک با مالک سازمان خود تماس بگیرید."}, "loggingInTo": {"message": "ورود به $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "نسخه سرور"}, "selfHostedServer": {"message": "<PERSON><PERSON><PERSON> میزبان"}, "thirdParty": {"message": "شخص ثالث"}, "thirdPartyServerMessage": {"message": "به اجرای سرور شخص ثالث، $SERVERNAME$ متصل شد. لطفاً اشکالات را با استفاده از سرور رسمی تأیید کنید یا آنها را به سرور شخص ثالث گزارش دهید.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "آخرین بار در $DATE$ دیده شده است", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "با کلمه عبور اصلی وارد شوید"}, "newAroundHere": {"message": "اینجا تازه واردی؟"}, "rememberEmail": {"message": "ایمیل را به خاطر بسپار"}, "loginWithDevice": {"message": "ورود با دستگاه"}, "fingerprintPhraseHeader": {"message": "عبارت اثر انگشت"}, "fingerprintMatchInfo": {"message": "لطفاً مطمئن شوید که قفل گاوصندوق شما باز است و عبارت اثر انگشت با دستگاه دیگر مطابقت دارد."}, "resendNotification": {"message": "ارسال مجدد اعلان"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "یک اعلان به دستگاه شما ارسال شده است."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "ورود به سیستم آغاز شد"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "کلمه عبور اصلی افشا شده"}, "exposedMasterPasswordDesc": {"message": "کلمه عبور در نقض داده پیدا شد. از یک کلمه عبور منحصر به فرد برای محافظت از حساب خود استفاده کنید. آیا مطمئنید که می‌خواهید از یک کلمه عبور افشا شده استفاده کنید؟"}, "weakAndExposedMasterPassword": {"message": "کلمه عبور اصلی ضعیف و افشا شده"}, "weakAndBreachedMasterPasswordDesc": {"message": "کلمه عبور ضعیف شناسایی و در یک نقض داده پیدا شد. از یک کلمه عبور قوی و منحصر به فرد برای محافظت از حساب خود استفاده کنید. آیا مطمئنید که می‌خواهید از این کلمه عبور استفاده کنید؟"}, "checkForBreaches": {"message": "نقض اطلاعات شناخته شده برای این کلمه عبور را بررسی کنید"}, "important": {"message": "مهم:"}, "masterPasswordHint": {"message": "کلمه عبور اصلی شما در صورت فراموشی قابل بازیابی نیست!"}, "characterMinimum": {"message": "حداقل کاراکتر $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "خط مشی‌های سازمان شما پر کردن خودکار هنگام بارگیری صفحه را روشن کرده است."}, "howToAutofill": {"message": "نحوه پر کردن خودکار"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "متوجه شدم"}, "autofillSettings": {"message": "تنظیمات پر کردن خودکار"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Manage shortcuts"}, "autofillShortcut": {"message": "میان<PERSON><PERSON> صف<PERSON>ه کلید پر کردن خودکار"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "میانبر پر کردن خودکار پیش‌فرض: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "در پنجره جدید باز می‌شود"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "تأیید دستگاه لازم است. یک روش تأیید انتخاب کنید:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "این دستگاه را به خاطر بسپار"}, "uncheckIfPublicDevice": {"message": "اگر از دستگاه عمومی استفاده می‌کنید علامت را بردارید"}, "approveFromYourOtherDevice": {"message": "تأیید با دستگاه دیگرتان"}, "requestAdminApproval": {"message": "درخواست تأیید مدیر"}, "ssoIdentifierRequired": {"message": "شناسه سازمان SSO مورد نیاز است."}, "creatingAccountOn": {"message": "در حال ساخت حساب روی"}, "checkYourEmail": {"message": "ایمیل خود را چک کنید"}, "followTheLinkInTheEmailSentTo": {"message": "دن<PERSON>ال کنید لینکی را که در ایمیل فرستاده شده به"}, "andContinueCreatingYourAccount": {"message": "و به ساختن حساب‌تان ادامه دهید."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "eu": {"message": "اروپا", "description": "European Union"}, "accessDenied": {"message": "دسترسی رد شد. شما اجازه مشاهده این صفحه را ندارید."}, "general": {"message": "عمومی"}, "display": {"message": "نمایش"}, "accountSuccessfullyCreated": {"message": "حساب کاربری با موفقیت ایجاد شد!"}, "adminApprovalRequested": {"message": "تأیید مدیر درخواست شد"}, "adminApprovalRequestSentToAdmins": {"message": "درخواست شما به مدیرتان فرستاده شد."}, "troubleLoggingIn": {"message": "در ورود مشکلی دارید؟"}, "loginApproved": {"message": "ورود تأیید شد"}, "userEmailMissing": {"message": "ایمیل کاربر وجود ندارد"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "دستگاه مورد اعتماد است"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsNoItemsTitle": {"message": "No active Sends", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsNoItemsMessage": {"message": "Use Send to securely share encrypted information with anyone.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "ورودی ضروری است."}, "required": {"message": "ضروری"}, "search": {"message": "جستجو"}, "inputMinLength": {"message": "ورودی باید حداقل $COUNT$ کاراکتر داشته باشد.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "طول ورودی نباید بیش از $COUNT$ کاراکتر باشد.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "کاراکترهای زیر مجاز نیستند: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "مقدار ورودی باید حداقل $MIN$ باشد.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "مقدار ورودی نباید از $MAX$ تجاوز کند.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "یک یا چند ایمیل نامعتبر است"}, "inputTrimValidator": {"message": "ورودی نباید فقط حاوی فضای خالی باشد.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "ورودی یک نشانی ایمیل نیست."}, "fieldsNeedAttention": {"message": "فیلد $COUNT$ در بالا به توجه شما نیاز دارد.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- انتخاب --"}, "multiSelectPlaceholder": {"message": "-- برای فیلتر تایپ کنید --"}, "multiSelectLoading": {"message": "در حال بازیابی گزینه‌ها..."}, "multiSelectNotFound": {"message": "موردی یافت نشد"}, "multiSelectClearAll": {"message": "پاک‌کردن همه"}, "plusNMore": {"message": "+ $QUANTITY$ بیشتر", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "زیرمنو"}, "toggleCollapse": {"message": "دکمه بستن", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "دامنه مستعار"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "موارد با درخواست مجدد کلمه عبور اصلی را نمی‌توان در بارگذاری صفحه به‌صورت خودکار پر کرد. پر کردن خودکار در بارگیری صفحه خاموش شد.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "پر کردن خودکار در بارگیری صفحه برای استفاده از تنظیمات پیش‌فرض تنظیم شده است.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "برای ویرایش این فیلد، درخواست مجدد کلمه عبور اصلی را خاموش کنید", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Skip to content"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden autofill menu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Unlock your account to view matching logins", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Unlock account", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Fill credentials for", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Partial username", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "موردی برای نمایش وجود ندارد", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "مو<PERSON><PERSON> جدید", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "افزودن موردی جدید به گاوصندوق", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "New identity", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "روشن کردن"}, "ignore": {"message": "نادیده گرفتن"}, "importData": {"message": "وارد کردن اطلاعات", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "خطای وارد کردن"}, "importErrorDesc": {"message": "مشکلی با داده‌هایی که سعی کردید وارد کنید وجود داشت. لطفاً خطاهای فهرست شده زیر را در فایل منبع خود برطرف کرده و دوباره امتحان کنید."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Resolve the errors below and try again."}, "description": {"message": "توضیحات"}, "importSuccess": {"message": "داده‌ها با موفقیت وارد شد"}, "importSuccessNumberOfItems": {"message": "در کل $AMOUNT$ مورد وارد شده است.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "دوباره سعی کنید"}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "تنظیم PIN"}, "verifyWithBiometrics": {"message": "تایید با استفاده از بیومتریک"}, "awaitingConfirmation": {"message": "در انتظار تایید"}, "couldNotCompleteBiometrics": {"message": "تکمیل بیومتریک ممکن نشد."}, "needADifferentMethod": {"message": "نیازمند روش دیگری هستید؟"}, "useMasterPassword": {"message": "استفاده از رمز عبور اصلی"}, "usePin": {"message": "استفاده از PIN"}, "useBiometrics": {"message": "استفاده از بیومتریک"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Resend code"}, "total": {"message": "مجموع"}, "importWarning": {"message": "You are importing data to $ORGANIZATION$. Your data may be shared with members of this organization. Do you want to proceed?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Duo two-step login is required for your account."}, "popoutExtension": {"message": "Popout extension"}, "launchDuo": {"message": "اجرای Duo"}, "importFormatError": {"message": "داده‌ها به درستی قالب‌بندی نشده‌اند. لطفا فایل وارد شده خود را بررسی و دوباره امتحان کنید."}, "importNothingError": {"message": "چیزی وارد نشد."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Learn about your import options"}, "selectImportFolder": {"message": "یک پوشه انتخاب کنید"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "confirmFilePassword": {"message": "Confirm file password"}, "exportSuccess": {"message": "Vault data exported"}, "typePasskey": {"message": "Passkey"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "ک<PERSON><PERSON>د عبور کپی نمی‌شود"}, "passkeyNotCopiedAlert": {"message": "کلید عبور در مورد شبیه سازی شده کپی نمی‌شود. آیا می‌خواهید به شبیه سازی این مورد ادامه دهید؟"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "تأیید توسط سایت آغازگر الزامی است. این ویژگی هنوز برای حساب‌های بدون کلمه عبور اصلی اجرا نشده است."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "یک کلید عبور از قبل برای این برنامه وجود دارد."}, "noPasskeysFoundForThisApplication": {"message": "هیچ کلمه عبوری برای این برنامه یافت نشد."}, "noMatchingPasskeyLogin": {"message": "شما هیچ ورود مشابهی برای این سایت ندارید."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "تأیید"}, "savePasskey": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> کلید عبور"}, "savePasskeyNewLogin": {"message": "کلید عبور را به عنوان ورود جدید ذخیره کن"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "مور<PERSON> کلید عبور"}, "overwritePasskey": {"message": "بازنویسی کلید عبور؟"}, "overwritePasskeyAlert": {"message": "این مورد از قبل دارای یک کلید عبور است. آیا مطمئنید که می‌خواهید کلمه عبور فعلی را بازنویسی کنید؟"}, "featureNotSupported": {"message": "ویژگی هنوز پشتیبانی نمی‌شود"}, "yourPasskeyIsLocked": {"message": "برای استفاده از کلید عبور، احراز هویت لازم است. برای ادامه، هویت خود را تأیید کنید."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "Incorrect username or password"}, "incorrectPassword": {"message": "Incorrect password"}, "incorrectCode": {"message": "Incorrect code"}, "incorrectPin": {"message": "Incorrect PIN"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "LastPass Email"}, "importingYourAccount": {"message": "Importing your account..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "LastPass master password"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Import directly from LastPass"}, "importFromCSV": {"message": "Import from CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Collection"}, "lastPassYubikeyDesc": {"message": "Insert the YubiKey associated with your LastPass account into your computer's USB port, then touch its button."}, "switchAccount": {"message": "Switch account"}, "switchAccounts": {"message": "Switch accounts"}, "switchToAccount": {"message": "Switch to account"}, "activeAccount": {"message": "Active account"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Available accounts"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "server"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Just once"}, "alwaysForThisSite": {"message": "Always for this site"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ added to excluded domains.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Make <PERSON><PERSON><PERSON> your default password manager?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ignoring this option may cause conflicts between Bitwarden autofill suggestions and your browser's.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Make Bitwarden your default password manager", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Unable to set <PERSON><PERSON><PERSON> as the default password manager", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "You must grant browser privacy permissions to Bitwarden to set it as the default password manager.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Make default", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Credentials saved successfully!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Credentials updated successfully!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Error saving credentials. Check console for details.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Success"}, "removePasskey": {"message": "Remove passkey"}, "passkeyRemoved": {"message": "Passkey removed"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "accountSecurity": {"message": "Account security"}, "notifications": {"message": "Notifications"}, "appearance": {"message": "Appearance"}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "New"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "Owner"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Additional information"}, "itemHistory": {"message": "Item history"}, "lastEdited": {"message": "Last edited"}, "ownerYou": {"message": "Owner: You"}, "linked": {"message": "Linked"}, "copySuccessful": {"message": "Copy Successful"}, "upload": {"message": "Upload"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "deleteAttachmentName": {"message": "Delete attachment $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Download $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "Filters"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "downloadAttachment": {"message": "Download - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Login credentials"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "Add account"}, "loading": {"message": "Loading"}, "data": {"message": "Data"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Add field"}, "add": {"message": "Add"}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Edit field"}, "editFieldLabel": {"message": "Edit $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Delete $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ added", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Item Location"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "Compact mode"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBody": {"message": "Autofill items for the current page\nFavorite items for easy access\nSearch your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}}