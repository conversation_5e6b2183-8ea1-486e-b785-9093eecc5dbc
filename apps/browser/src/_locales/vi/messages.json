{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "<PERSON><PERSON><PERSON><PERSON> quản lý mật kh<PERSON>u Bitwarden", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "Ở nh<PERSON>, ở cơ quan, hay trên đư<PERSON>ng đi, Bitwarden sẽ bảo mật tất cả mật khẩu, mã kho<PERSON>, và thông tin cá nhân của bạn", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "<PERSON><PERSON><PERSON> nhập hoặc tạo tài khoản mới để truy cập kho lưu trữ của bạn."}, "inviteAccepted": {"message": "<PERSON><PERSON><PERSON> mời đ<PERSON><PERSON><PERSON> chấ<PERSON>n"}, "createAccount": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "newToBitwarden": {"message": "New to Bitwarden?"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "Welcome back"}, "setAStrongPassword": {"message": "Đặt mật khẩu mạnh"}, "finishCreatingYourAccountBySettingAPassword": {"message": "<PERSON><PERSON><PERSON> thành việc tạo tài khoản của bạn bằng cách đặt mật khẩu"}, "enterpriseSingleSignOn": {"message": "<PERSON><PERSON><PERSON> nhập bằng tài khoản tổ chức"}, "cancel": {"message": "Hủy bỏ"}, "close": {"message": "Đ<PERSON><PERSON>"}, "submit": {"message": "<PERSON><PERSON><PERSON>"}, "emailAddress": {"message": "Đ<PERSON>a chỉ email"}, "masterPass": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>h"}, "masterPassDesc": {"message": "Mật khẩu chính là mật khẩu bạn dùng để truy cập vào kho lưu trữ của bạn. Mật khẩu này rất quan trọng và bạn đừng nên quên mật khẩu chính này. Bạn sẽ không thể khôi phục mật khẩu chính trong trường hợp bạn quên nó."}, "masterPassHintDesc": {"message": "Gợi ý mật khẩu chính có thể giúp bạn nhớ lại mật khẩu của mình nếu bạn quên nó."}, "masterPassHintText": {"message": "<PERSON><PERSON>u bạn quên mật khẩu, gợi ý mật khẩu có thể được gửi tới email của bạn. $CURRENT$/$MAXIMUM$ ký tự tối đa.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u ch<PERSON>h"}, "masterPassHint": {"message": "<PERSON><PERSON><PERSON> <PERSON> mật kh<PERSON>u ch<PERSON> (tù<PERSON> chọn)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "<PERSON>ham gia tổ chức"}, "joinOrganizationName": {"message": "Join $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "<PERSON><PERSON><PERSON> tất gia nhập tổ chức này bằng cách đặt một mật khẩu chính."}, "tab": {"message": "Tab"}, "vault": {"message": "<PERSON><PERSON> l<PERSON> trữ"}, "myVault": {"message": "<PERSON><PERSON> l<PERSON>u trữ của tôi"}, "allVaults": {"message": "<PERSON><PERSON><PERSON> cả kho lưu trữ"}, "tools": {"message": "<PERSON><PERSON><PERSON> cụ"}, "settings": {"message": "Cài đặt"}, "currentTab": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> tại"}, "copyPassword": {"message": "<PERSON><PERSON> ch<PERSON><PERSON> mật kh<PERSON>u"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "Sao ch<PERSON>p ghi chú"}, "copyUri": {"message": "Sao chép URI"}, "copyUsername": {"message": "<PERSON><PERSON> chép tên ng<PERSON><PERSON>i dùng"}, "copyNumber": {"message": "Sao chép số"}, "copySecurityCode": {"message": "<PERSON>o ch<PERSON>p mã b<PERSON>o mật"}, "copyName": {"message": "<PERSON><PERSON> ch<PERSON>p tên"}, "copyCompany": {"message": "Copy company"}, "copySSN": {"message": "<PERSON><PERSON> b<PERSON>o hiểm xã hội"}, "copyPassportNumber": {"message": "<PERSON>o ch<PERSON>p số hộ chiếu"}, "copyLicenseNumber": {"message": "Sao chép số giấy phép"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "Copy notes"}, "copy": {"message": "Copy", "description": "Copy to clipboard"}, "fill": {"message": "Fill", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Tự động điền"}, "autoFillLogin": {"message": "Tự động điền thông tin đăng nhập"}, "autoFillCard": {"message": "Tự động điền thẻ"}, "autoFillIdentity": {"message": "Tự động điền danh t<PERSON>h"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON><PERSON><PERSON> mật k<PERSON> (đã sao chép)"}, "copyElementIdentifier": {"message": "<PERSON><PERSON> chép tên trường tùy chỉnh"}, "noMatchingLogins": {"message": "<PERSON><PERSON><PERSON><PERSON> có thông tin đăng nhập phù hợp."}, "noCards": {"message": "<PERSON><PERSON><PERSON>ng có thẻ"}, "noIdentities": {"message": "<PERSON><PERSON><PERSON><PERSON> có danh tính"}, "addLoginMenu": {"message": "<PERSON><PERSON><PERSON><PERSON> thông tin đăng nhập"}, "addCardMenu": {"message": "Thê<PERSON> thẻ"}, "addIdentityMenu": {"message": "<PERSON><PERSON><PERSON><PERSON> danh t<PERSON>h"}, "unlockVaultMenu": {"message": "Mở khoá kho lưu trữ của bạn"}, "loginToVaultMenu": {"message": "<PERSON><PERSON><PERSON> nhập vào kho lưu trữ của bạn"}, "autoFillInfo": {"message": "<PERSON>h<PERSON>ng có thông tin đăng nhập nào sẵn có để tự động điền vào tab hiện tại."}, "addLogin": {"message": "<PERSON><PERSON><PERSON><PERSON> một đăng nhập"}, "addItem": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "<PERSON>hận gợi ý mật khẩu ch<PERSON>h"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendVerificationCode": {"message": "Gửi mã xác minh tới email của bạn"}, "sendCode": {"message": "<PERSON><PERSON><PERSON> mã"}, "codeSent": {"message": "Đã gửi mã"}, "verificationCode": {"message": "<PERSON><PERSON> x<PERSON>n"}, "confirmIdentity": {"message": "<PERSON><PERSON><PERSON> nhận danh tính của bạn để tiếp tục."}, "changeMasterPassword": {"message": "<PERSON>hay đ<PERSON>i mật kh<PERSON>u ch<PERSON>h"}, "continueToWebApp": {"message": "Ti<PERSON><PERSON> tục tới ứng dụng web?"}, "continueToWebAppDesc": {"message": "<PERSON>h<PERSON>m phá thêm các tính năng của tài khoản Bitwarden của bạn trên bản web."}, "continueToHelpCenter": {"message": "Tiế<PERSON> tục tới Trung tâm trợ giúp?"}, "continueToHelpCenterDesc": {"message": "T<PERSON>m hiểu thêm về cách sử dụng Bitwarden trong Trung tâm trợ giúp."}, "continueToBrowserExtensionStore": {"message": "Ti<PERSON><PERSON> tục tới cửa hàng tiện ích mở rộng của trình duyệt?"}, "continueToBrowserExtensionStoreDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> người khác tìm hiểu xem Bitwarden có phù hợp với họ không. H<PERSON><PERSON> truy cập cửa hàng tiện ích mở rộng trên trình duyệt của bạn và đánh giá ngay bây giờ."}, "changeMasterPasswordOnWebConfirmation": {"message": "Bạn có thể thay đổi mật khẩu ch<PERSON>h của mình trên Bitwarden bản web."}, "fingerprintPhrase": {"message": "Cụm vân tay", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "<PERSON><PERSON><PERSON> vân tay của tài k<PERSON>n của bạn", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "<PERSON><PERSON><PERSON> n<PERSON><PERSON>p hai b<PERSON>"}, "logOut": {"message": "<PERSON><PERSON><PERSON> xu<PERSON>"}, "aboutBitwarden": {"message": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> về Bitwarden"}, "about": {"message": "Thông tin"}, "moreFromBitwarden": {"message": "Thông tin khác từ Bitwarden"}, "continueToBitwardenDotCom": {"message": "T<PERSON><PERSON><PERSON> tục tới bitwarden.com?"}, "bitwardenForBusiness": {"message": "<PERSON><PERSON><PERSON> dành cho <PERSON>"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "Ứng dụng Bitwarden Authenticator cho phép bạn lưu trữ khóa xác thực và tạo mã TOTP cho quy trình xác minh hai bước. Tìm hiểu thêm trên trang web bitwarden.com"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "<PERSON><PERSON><PERSON> trữ bả<PERSON> mật, quản lý và chia sẻ bí mật của nhà phát triển với Bitwarden Secrets Manager. T<PERSON>y cập bitwarden.com để biết thêm chi tiết."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "T<PERSON>o trải nghiệm đăng nhập mượt mà và an toàn không cần mật khẩu truyền thống với Passwordless.dev. Tìm hiểu thêm trên trang web bitwarden.com."}, "freeBitwardenFamilies": {"message": "<PERSON><PERSON><PERSON>nh <PERSON> phí của <PERSON>en"}, "freeBitwardenFamiliesPageDesc": {"message": "Bạn đủ điều kiện cho Gói Gia đình <PERSON> phí của Bitwarden. Hãy nhận ưu đãi này ngay hôm nay trên ứng dụng web."}, "version": {"message": "<PERSON><PERSON><PERSON>"}, "save": {"message": "<PERSON><PERSON><PERSON>"}, "move": {"message": "<PERSON>"}, "addFolder": {"message": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> mục"}, "name": {"message": "<PERSON><PERSON><PERSON>"}, "editFolder": {"message": "Chỉnh sửa thư mục"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "<PERSON><PERSON><PERSON> m<PERSON> mới"}, "folderName": {"message": "<PERSON><PERSON><PERSON> th<PERSON> mục"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "<PERSON><PERSON><PERSON> th<PERSON> mục"}, "folders": {"message": "<PERSON><PERSON><PERSON>"}, "noFolders": {"message": "<PERSON><PERSON><PERSON><PERSON> có thư mục để liệt kê."}, "helpFeedback": {"message": "<PERSON><PERSON><PERSON> g<PERSON> & p<PERSON><PERSON><PERSON> hồi"}, "helpCenter": {"message": "Trung tâm hỗ trợ <PERSON>warden"}, "communityForums": {"message": "<PERSON><PERSON><PERSON><PERSON> phá diễn đàn cộng đồng Bitwarden"}, "contactSupport": {"message": "<PERSON><PERSON><PERSON> hệ với bộ phận hỗ trợ <PERSON>ward<PERSON>"}, "sync": {"message": "<PERSON><PERSON><PERSON> bộ"}, "syncVaultNow": {"message": "<PERSON><PERSON><PERSON> bộ kho lưu trữ ngay"}, "lastSync": {"message": "<PERSON><PERSON>ng bộ lần cuối:"}, "passGen": {"message": "<PERSON><PERSON><PERSON><PERSON> tạo mật kh<PERSON>u"}, "generator": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>o", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Tự động tạo mật khẩu mạnh mẽ, độc nhất cho đăng nhập của bạn."}, "bitWebVaultApp": {"message": "Ứng dụng <PERSON> bản web"}, "importItems": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "select": {"message": "<PERSON><PERSON><PERSON>"}, "generatePassword": {"message": "<PERSON><PERSON><PERSON> mật k<PERSON>u"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "<PERSON><PERSON><PERSON> lại mật kh<PERSON>u"}, "options": {"message": "<PERSON><PERSON><PERSON>"}, "length": {"message": "<PERSON><PERSON> dài"}, "include": {"message": "<PERSON><PERSON>", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "<PERSON><PERSON><PERSON> cả số", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Bao gồm các ký tự đặc biệt", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "Số từ"}, "wordSeparator": {"message": "Word Separator"}, "capitalize": {"message": "<PERSON><PERSON><PERSON><PERSON> hoa", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "<PERSON><PERSON><PERSON> cả số"}, "minNumbers": {"message": "Số kí tự tối thiểu"}, "minSpecial": {"message": "<PERSON><PERSON> kí tự đặc biệt tối thiểu"}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "<PERSON><PERSON><PERSON> kiếm trong kho lưu trữ"}, "edit": {"message": "<PERSON><PERSON><PERSON>"}, "view": {"message": "Xem"}, "noItemsInList": {"message": "<PERSON><PERSON><PERSON><PERSON> có mục nào để liệt kê."}, "itemInformation": {"message": "<PERSON><PERSON><PERSON> thông tin"}, "username": {"message": "<PERSON><PERSON><PERSON> dùng"}, "password": {"message": "<PERSON><PERSON><PERSON>"}, "totp": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c thực"}, "passphrase": {"message": "<PERSON><PERSON><PERSON> từ mật khẩu"}, "favorite": {"message": "<PERSON><PERSON><PERSON>ch"}, "unfavorite": {"message": "Bỏ thích"}, "itemAddedToFavorites": {"message": "<PERSON><PERSON> thêm vào yêu thích"}, "itemRemovedFromFavorites": {"message": "<PERSON>ã xóa khỏi yêu thích"}, "notes": {"message": "<PERSON><PERSON><PERSON>"}, "privateNote": {"message": "Private note"}, "note": {"message": "<PERSON><PERSON><PERSON>"}, "editItem": {"message": "Chỉnh sửa mục"}, "folder": {"message": "<PERSON><PERSON><PERSON>"}, "deleteItem": {"message": "<PERSON><PERSON><PERSON>"}, "viewItem": {"message": "<PERSON><PERSON>"}, "launch": {"message": "Khởi chạy"}, "launchWebsite": {"message": "Mở trang web"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Trang web"}, "toggleVisibility": {"message": "Bật/tắt khả năng hiển thị"}, "manage": {"message": "<PERSON><PERSON><PERSON><PERSON> lý"}, "other": {"message": "K<PERSON><PERSON><PERSON>"}, "unlockMethods": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>n mở khóa"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> lập phư<PERSON><PERSON> thức mở khóa để thay đổi hành động hết thời gian chờ của vault."}, "unlockMethodNeeded": {"message": "<PERSON><PERSON><PERSON><PERSON> lập phương pháp mở khóa trong Cài đặt"}, "sessionTimeoutHeader": {"message": "<PERSON>h<PERSON>i gian chờ của phiên"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "<PERSON><PERSON><PERSON>"}, "rateExtension": {"message": "<PERSON><PERSON><PERSON> giá tiện ích mở rộng"}, "browserNotSupportClipboard": {"message": "Tr<PERSON><PERSON> web của bạn không hỗ trợ dễ dàng sao chép bộ nhớ tạm. Bạn có thể sao chép nó theo cách thủ công để thay thế."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "<PERSON>ho của bạn đã bị khóa. <PERSON><PERSON><PERSON> minh danh tính của bạn để mở khoá."}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "Mở khóa"}, "loggedInAsOn": {"message": "<PERSON>ã đăng nhập là $EMAIL$ trên $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> ch<PERSON>h không hợp lệ"}, "vaultTimeout": {"message": "<PERSON>h<PERSON>i gian chờ của kho"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "<PERSON><PERSON><PERSON><PERSON> ngay"}, "lockAll": {"message": "<PERSON><PERSON><PERSON><PERSON> tất cả"}, "immediately": {"message": "<PERSON><PERSON> l<PERSON> t<PERSON>c"}, "tenSeconds": {"message": "10 giây"}, "twentySeconds": {"message": "20 giây"}, "thirtySeconds": {"message": "30 giây"}, "oneMinute": {"message": "1 phút"}, "twoMinutes": {"message": "2 phút"}, "fiveMinutes": {"message": "5 phút"}, "fifteenMinutes": {"message": "15 phút"}, "thirtyMinutes": {"message": "30 phút"}, "oneHour": {"message": "1 giờ"}, "fourHours": {"message": "4 giờ"}, "onLocked": {"message": "Mỗi khi khóa"}, "onRestart": {"message": "Mỗi khi khởi động lại trình duy<PERSON>t"}, "never": {"message": "<PERSON><PERSON><PERSON><PERSON> bao giờ"}, "security": {"message": "<PERSON><PERSON><PERSON>"}, "confirmMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u ch<PERSON>h"}, "masterPassword": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>h"}, "masterPassImportant": {"message": "<PERSON><PERSON><PERSON> khẩu ch<PERSON>h của bạn không thể phục hồi nếu bạn quên nó!"}, "masterPassHintLabel": {"message": "Gợ<PERSON> ý mật khẩu ch<PERSON>h"}, "errorOccurred": {"message": "Đ<PERSON> xảy ra lỗi"}, "emailRequired": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u địa chỉ email."}, "invalidEmail": {"message": "Địa chỉ email kh<PERSON><PERSON> hợp lệ."}, "masterPasswordRequired": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u mật kh<PERSON>u ch<PERSON>h."}, "confirmMasterPasswordRequired": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u nhập lại mật kh<PERSON>u ch<PERSON>h."}, "masterPasswordMinlength": {"message": "Mật khẩu ch<PERSON>h phải có ít nhất $VALUE$ kí tự.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>n mật kh<PERSON>u ch<PERSON>h không khớp."}, "newAccountCreated": {"message": "Tài khoản mới của bạn đã được tạo! Bạn có thể đăng nhập từ bây giờ."}, "newAccountCreated2": {"message": "T<PERSON><PERSON> khoản của bạn đã đư<PERSON>c tạo thành công!"}, "youHaveBeenLoggedIn": {"message": "Bạn đã đăng nhập thành công!"}, "youSuccessfullyLoggedIn": {"message": "Bạn đã đăng nhập thành công"}, "youMayCloseThisWindow": {"message": "<PERSON><PERSON>n có thể đóng cửa sổ này"}, "masterPassSent": {"message": "<PERSON><PERSON>g tôi đã gửi cho bạn email có chứa gợi ý mật khẩu chính của bạn."}, "verificationCodeRequired": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u mã x<PERSON>c n<PERSON>n."}, "webauthnCancelOrTimeout": {"message": "Quá trình xác thực đã bị hủy hoặc mất quá nhiều thời gian. <PERSON><PERSON> lòng thử lại."}, "invalidVerificationCode": {"message": "<PERSON><PERSON> xác minh không đúng"}, "valueCopied": {"message": "Đã sao chép $VALUE$", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "<PERSON><PERSON><PERSON>ng thể tự động điền mục đã chọn trên trang này. <PERSON><PERSON><PERSON> thực hiện sao chép và dán thông tin một cách thủ công."}, "totpCaptureError": {"message": "<PERSON><PERSON><PERSON><PERSON> thể quét mã QR từ trang web hiện tại"}, "totpCaptureSuccess": {"message": "<PERSON><PERSON> thêm khóa xác thực"}, "totpCapture": {"message": "<PERSON><PERSON><PERSON> mã QR xác thực từ trang web hiện tại"}, "totpHelperTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> hiện xác minh hai b<PERSON><PERSON>c liền mạch"}, "totpHelper": {"message": "Bitwarden có thể lưu trữ và điền mã xác minh 2 bước. Sao chép và dán khóa vào trường này."}, "totpHelperWithCapture": {"message": "Bitwarden có thể lưu trữ và điền mã xác minh 2 bước. Hãy chọn biểu tượng máy ảnh để chụp mã QR xác thực của trang web, hoặc sao chép và dán khoá vào ô này."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Sao chép khóa Authenticator (TOTP)"}, "loggedOut": {"message": "<PERSON><PERSON> đăng xuất"}, "loggedOutDesc": {"message": "Bạn đã đăng xuất khỏi tài khoản của mình."}, "loginExpired": {"message": "<PERSON><PERSON><PERSON> đăng nhập của bạn đã hết hạn."}, "logIn": {"message": "<PERSON><PERSON><PERSON>"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "<PERSON><PERSON><PERSON>n hành đăng ký lại"}, "expiredLink": {"message": "<PERSON><PERSON><PERSON> kết đã hết hạn"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "<PERSON>ui lòng đăng ký lại hoặc thử đăng nhập."}, "youMayAlreadyHaveAnAccount": {"message": "Bạn có thể đã có tài k<PERSON>ản"}, "logOutConfirmation": {"message": "Bạn có chắc chắn muốn đăng xuất không?"}, "yes": {"message": "<PERSON><PERSON>"}, "no": {"message": "K<PERSON>ô<PERSON>"}, "location": {"message": "Location"}, "unexpectedError": {"message": "Một lỗi bất ngờ đã xảy ra."}, "nameRequired": {"message": "<PERSON><PERSON><PERSON> l<PERSON> b<PERSON> bu<PERSON>."}, "addedFolder": {"message": "<PERSON><PERSON> thêm thư mục"}, "twoStepLoginConfirmation": {"message": "<PERSON><PERSON><PERSON> thực hai lớp giúp cho tài khoản của bạn an toàn hơn bằng cách yêu cầu bạn xác minh thông tin đăng nhập của bạn bằng một thiết bị khác nh<PERSON> kh<PERSON><PERSON> b<PERSON><PERSON> mậ<PERSON>, <PERSON>ng dụng x<PERSON><PERSON> thực, SMS, cuộc gọi điện thoại hoặc email. Bạn có thể bật xác thực hai lớp trong kho bitwarden nền web. Bạn có muốn ghé thăm trang web bây giờ?"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "<PERSON><PERSON> lưu thư mục"}, "deleteFolderConfirmation": {"message": "Bạn có chắc chắn muốn xóa thư mục này không?"}, "deletedFolder": {"message": "<PERSON><PERSON> x<PERSON>a thư mục"}, "gettingStartedTutorial": {"message": "Hướng dẫn Bắt đầu"}, "gettingStartedTutorialVideo": {"message": "<PERSON><PERSON> hướng dẫn bắt đầu của chúng tôi để tìm hiểu cách tận dụng tối đa tiện ích mở rộng của trình duyệt."}, "syncingComplete": {"message": "Đồng bộ hoàn tất"}, "syncingFailed": {"message": "Đồng bộ thất bại"}, "passwordCopied": {"message": "Đã sao chép mật kh<PERSON>u"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "URI mới"}, "addDomain": {"message": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON>n", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "<PERSON><PERSON> thêm mục"}, "editedItem": {"message": "<PERSON><PERSON><PERSON> đ<PERSON> chỉnh sửa"}, "deleteItemConfirmation": {"message": "Bạn có chắc bạn muốn xóa mục này?"}, "deletedItem": {"message": "Đã xóa mục"}, "overwritePassword": {"message": "<PERSON><PERSON> đ<PERSON> mật kh<PERSON>u"}, "overwritePasswordConfirmation": {"message": "Bạn có chắc chắn muốn ghi đè mật khẩu hiện tại không?"}, "overwriteUsername": {"message": "<PERSON><PERSON> đè tên người dùng"}, "overwriteUsernameConfirmation": {"message": "Bạn có chắc chắn muốn ghi đè tên người dùng hiện tại không?"}, "searchFolder": {"message": "<PERSON><PERSON><PERSON> ki<PERSON>m thư mục"}, "searchCollection": {"message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>m bộ sưu tập"}, "searchType": {"message": "<PERSON><PERSON><PERSON>"}, "noneFolder": {"message": "<PERSON><PERSON><PERSON><PERSON> có thư mục", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "Hỏi để thêm đăng nhập"}, "vaultSaveOptionsTitle": {"message": "<PERSON><PERSON><PERSON> v<PERSON>o các tùy chọn kho"}, "addLoginNotificationDesc": {"message": "'Thông báo Thêm đăng nhập' sẽ tự động nhắc bạn lưu các đăng nhập mới vào hầm an toàn của bạn bất cứ khi nào bạn đăng nhập trang web lần đầu tiên."}, "addLoginNotificationDescAlt": {"message": "Đưa ra lựa chọn để thêm một mục nếu không tìm thấy mục đó trong hòm của bạn. Áp dụng với mọi tài khoản đăng nhập trên thiết bị."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "<PERSON><PERSON>n thị thẻ trên trang <PERSON>b"}, "showCardsCurrentTabDesc": {"message": "Liệt kê các mục thẻ trên trang Tab để dễ dàng tự động điền."}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "<PERSON><PERSON><PERSON> thị danh tính trên trang <PERSON>b"}, "showIdentitiesCurrentTabDesc": {"message": "Liệt kê các mục danh tính trên trang Tab để dễ dàng tự động điền."}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "Dọn dẹp khay nhớ tạm", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Tự động dọn dẹp giá trị được sao chép khỏi khay nhớ tạm của bạn.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Bạn có cần Bitwarden nhớ giúp bạn mật khẩu này không?"}, "notificationAddSave": {"message": "<PERSON><PERSON><PERSON>"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "loginSaveConfirmation": {"message": "$ITEMNAME$ saved to Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is saved."}, "loginUpdatedConfirmation": {"message": "$ITEMNAME$ updated in Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is updated."}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Hỏi để cập nhật đăng nhập hiện có"}, "changedPasswordNotificationDesc": {"message": "<PERSON><PERSON><PERSON> cầu cập nhật mật khẩu đăng nhập khi phát hiện thay đổi trên trang web."}, "changedPasswordNotificationDescAlt": {"message": "Đưa ra lựa chọn để cập nhật mật khẩu khi phát hiện có sự thay đổi trên trang web. Áp dụng với mọi tài khoản đăng nhập trên thiết bị."}, "enableUsePasskeys": {"message": "Đưa ra lựa chọn để lưu và sử dụng mã khoá"}, "usePasskeysDesc": {"message": "Đưa ra lựa chọn để lưu mã khoá mới hoặc đăng nhập bằng mã khoá đã lưu trong kho. Áp dụng với mọi tài khoản đăng nhập trên thiết bị."}, "notificationChangeDesc": {"message": "Bạn có muốn cập nhật mật khẩu này trên <PERSON> không?"}, "notificationChangeSave": {"message": "<PERSON><PERSON><PERSON>"}, "notificationUnlockDesc": {"message": "<PERSON><PERSON> lòng mở khóa Kho Bitwarden của bạn để hoàn thành quá trình tự động điền."}, "notificationUnlock": {"message": "Mở khóa"}, "additionalOptions": {"message": "<PERSON><PERSON><PERSON> b<PERSON> sung"}, "enableContextMenuItem": {"message": "<PERSON><PERSON><PERSON> thị tuỳ chọn menu ngữ cảnh"}, "contextMenuItemDesc": {"message": "Sử dụng một đúp chuột để truy cập vào việc tạo mật khẩu và thông tin đăng nhập phù hợp cho trang web. "}, "contextMenuItemDescAlt": {"message": "<PERSON><PERSON><PERSON> cập trình khởi tạo mật khẩu và các mục đăng nhập đã lưu của trang web bằng cách nhấn đúp chuột. Áp dụng với mọi tài khoản đăng nhập trên thiết bị."}, "defaultUriMatchDetection": {"message": "<PERSON><PERSON><PERSON><PERSON> thức kiểm tra URI mặc định", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "<PERSON><PERSON><PERSON> phương thức mặc định để kiểm tra so sánh URI cho các đăng nhập khi xử lí các hành động như là tự động điền."}, "theme": {"message": "Chủ đề"}, "themeDesc": {"message": "<PERSON>hay đổi màu sắc <PERSON>ng dụng."}, "themeDescAlt": {"message": "Thay đổi tông màu giao diện của ứng dụng. Áp dụng với mọi tài khoản đăng nhập trên thiết bị."}, "dark": {"message": "<PERSON><PERSON><PERSON>", "description": "Dark color"}, "light": {"message": "<PERSON><PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "<PERSON><PERSON><PERSON> từ"}, "exportVault": {"message": "<PERSON><PERSON><PERSON> kho"}, "fileFormat": {"message": "<PERSON><PERSON><PERSON> dạng tập tin"}, "fileEncryptedExportWarningDesc": {"message": "Tập tin xuất này sẽ được bảo vệ bằng mật khẩu và yêu cầu mật khẩu để giải mã."}, "filePassword": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>u tập tin"}, "exportPasswordDescription": {"message": "<PERSON><PERSON>t khẩu này sẽ được sử dụng để xuất và nhập tập tin này"}, "accountRestrictedOptionDescription": {"message": "Sử dụng khóa mã hóa tài khoản của bạn, <PERSON><PERSON><PERSON><PERSON> tạo từ tên người dùng và mật khẩu chính của bạn để mã hóa tệp xuất và giới hạn việc nhập chỉ cho tài khoản Bitwarden hiện tại."}, "passwordProtectedOptionDescription": {"message": "Thiết lập mật khẩu cho tệp để mã hóa dữ liệu xuất và nhập nó vào bất kỳ tài khoản Bitwarden nào bằng cách sử dụng mật khẩu đó để giải mã."}, "exportTypeHeading": {"message": "<PERSON><PERSON><PERSON> xu<PERSON>t"}, "accountRestricted": {"message": "<PERSON><PERSON><PERSON>n bị hạn chế"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“<PERSON><PERSON>t khẩu tập tin” và “<PERSON><PERSON><PERSON><PERSON> lại mật khẩu tập tin” không khớp."}, "warning": {"message": "CẢNH BÁO", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "<PERSON><PERSON><PERSON> n<PERSON>n xuất kho"}, "exportWarningDesc": {"message": "<PERSON><PERSON>n xuất này chứa dữ liệu kho bạn và không được mã hóa. Bạn không nên lưu trữ hay gửi tập tin đã xuất thông qua phương thức rủi ro (như email). Vui lòng xóa nó ngay lập tức khi bạn đã sử dụng xong."}, "encExportKeyWarningDesc": {"message": "Quá trình xuất này sẽ mã hóa dữ liệu của bạn bằng khóa mã hóa của tài khoản. Nếu bạn từng xoay khóa mã hóa tài khoản của mình, bạn nên xuất lại vì bạn sẽ không thể giải mã tập tin xuất này."}, "encExportAccountWarningDesc": {"message": "Khóa mã hóa tài khoản là duy nhất cho mỗi tài khoản Bitwarden, vì vậy bạn không thể nhập tệp xuất được mã hóa vào một tài khoản khác."}, "exportMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> mật khẩu ch<PERSON>h để xuất kho của bạn."}, "shared": {"message": "Đã chia sẻ"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden cho <PERSON><PERSON> cho phép bạn chia sẻ các mục trong kho mật khẩu với người khác bằng cách tạo một tổ chức. Tìm hiểu thêm trên bitwarden.com."}, "moveToOrganization": {"message": "<PERSON> chuyển đến tổ chức"}, "movedItemToOrg": {"message": "$ITEMNAME$ đã được di chuyển đến $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Chọn một tổ chức mà bạn muốn chuyển mục này tới. Việc di chuyển đến một tổ chức sẽ chuyển quyền sở hữu của mụ<PERSON> sang tổ chức mà bạn chọn. Bạn sẽ không còn là chủ sở hữu trực tiếp của mục này một khi nó đã được chuyển."}, "learnMore": {"message": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm"}, "authenticatorKeyTotp": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON> th<PERSON>c (TOTP)"}, "verificationCodeTotp": {"message": "<PERSON><PERSON> x<PERSON>c thực (TOTP)"}, "copyVerificationCode": {"message": "Sao chép Mã xác thực"}, "attachments": {"message": "<PERSON><PERSON><PERSON>"}, "deleteAttachment": {"message": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> đ<PERSON>"}, "deleteAttachmentConfirmation": {"message": "Bạn có muốn xóa tệp đ<PERSON>h kèm này không?"}, "deletedAttachment": {"message": "<PERSON><PERSON> xoá tệp đ<PERSON> k<PERSON>m"}, "newAttachment": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> đ<PERSON>h kèm mới"}, "noAttachments": {"message": "<PERSON><PERSON><PERSON><PERSON> có tệp đ<PERSON> k<PERSON>."}, "attachmentSaved": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>h kèm đã đ<PERSON><PERSON><PERSON> l<PERSON>."}, "file": {"message": "<PERSON><PERSON><PERSON> tin"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "<PERSON><PERSON><PERSON> tập tin"}, "maxFileSize": {"message": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tối đa của tập tin là 500MB."}, "featureUnavailable": {"message": "<PERSON><PERSON><PERSON> năng không có sẵn"}, "encryptionKeyMigrationRequired": {"message": "Cần di chuyển khóa mã hóa. <PERSON><PERSON> lòng đăng nhập trang web Bitwaden để cập nhật khóa mã hóa của bạn."}, "premiumMembership": {"message": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "premiumManage": {"message": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> viên"}, "premiumManageAlert": {"message": "Bạn có thể quản lí tư cách thành viên của mình trên trang web kho lưu trữ bitwarden.com. Bạn có muốn truy cập trang web ngay bây giờ không?"}, "premiumRefresh": {"message": "<PERSON><PERSON><PERSON> mới thành viên"}, "premiumNotCurrentMember": {"message": "Bạn hiện không phải là một thành viên cao cấp."}, "premiumSignUpAndGet": {"message": "<PERSON><PERSON><PERSON> ký làm thành viên cao cấp và nhận được:"}, "ppremiumSignUpStorage": {"message": "1GB bộ nhớ lưu trữ được mã hóa cho các tệp đ<PERSON>h k<PERSON>."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "<PERSON><PERSON><PERSON> tùy chọn xác minh hai b<PERSON><PERSON><PERSON> nh<PERSON>K<PERSON> và Duo."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON> l<PERSON><PERSON> mật kh<PERSON>, ki<PERSON><PERSON> tra an toàn tài khoản và các báo cáo rò rĩ dữ liệu là để giữ cho kho của bạn an toàn."}, "ppremiumSignUpTotp": {"message": "Tr<PERSON><PERSON> tạo mã xác nhận TOTP (2FA) để đăng nhập vào kho lưu trữ của bạn."}, "ppremiumSignUpSupport": {"message": "Ưu tiên hỗ trợ khách hàng."}, "ppremiumSignUpFuture": {"message": "Tất cả các tính năng cao cấp trong tương lai. Nó sẽ sớm xuất hiện!"}, "premiumPurchase": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "premiumPurchaseAlertV2": {"message": "Bạn có thể mua gói Premium từ cài đặt tài khoản trên trang Bitwarden."}, "premiumCurrentMember": {"message": "Bạn là một thành viên cao cấp!"}, "premiumCurrentMemberThanks": {"message": "<PERSON><PERSON>m ơn bạn vì đã hỗ trợ Bitwarden."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "Tất cả chỉ với $PRICE$/năm!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "Tất cả chỉ với $PRICE$ /năm!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "<PERSON><PERSON><PERSON> mới hoàn tất"}, "enableAutoTotpCopy": {"message": "Tự động sao chép TOTP"}, "disableAutoTotpCopyDesc": {"message": "Nếu đăng nhập của bạn có một khóa xác thực gắn liền với nó, mã xác nhận TOTP sẽ được tự động sao chép vào bộ nhớ tạm của bạn bất cứ khi nào bạn tự động điền thông tin đăng nhập."}, "enableAutoBiometricsPrompt": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u sinh trắc học khi khởi chạy"}, "premiumRequired": {"message": "<PERSON><PERSON><PERSON> có tài kho<PERSON>n cao cấp"}, "premiumRequiredDesc": {"message": "<PERSON><PERSON><PERSON> là thành viên cao cấp để sử dụng tính năng này."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "Email xác minh đã được gửi tới $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "Lắp kh<PERSON>a bảo mật vào cổng USB máy tính của bạn. N<PERSON>u nó có một nút, hãy nhấn vào nó."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "<PERSON><PERSON><PERSON> th<PERSON><PERSON>"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "<PERSON><PERSON><PERSON> nhập không có sẵn"}, "noTwoStepProviders": {"message": "Tà<PERSON> kho<PERSON>n này đã kích hoạt xác thực hai lớp, tuy <PERSON>, trình duy<PERSON>t này không hỗ trợ cấu hình dịch vụ xác thực hai lớp đang sử dụng."}, "noTwoStepProviders2": {"message": "Hãy sử dụng trình duyệt web được hỗ trợ (chẳng hạn như Chrome) và/hoặc thêm dịch vụ bổ sung được hỗ trợ tốt hơn trên các trình duyệt web (chẳng hạn như một ứng dụng xác thực)."}, "twoStepOptions": {"message": "<PERSON><PERSON><PERSON> chọn xác thực hai lớp"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "Bạn mất quyền truy cập vào tất cả các dịch vụ xác thực 2 lớp? Sử dụng mã phục hồi của bạn để vô hiệu hóa tất cả các dịch vụ xác thực hai lớp trong tài khoản của bạn."}, "recoveryCodeTitle": {"message": "<PERSON><PERSON> p<PERSON><PERSON> h<PERSON>"}, "authenticatorAppTitle": {"message": "Ứng dụng Authenticator"}, "authenticatorAppDescV2": {"message": "<PERSON><PERSON><PERSON><PERSON> mã được tạo bởi ứng dụng xác thực như Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> mật OTP Yubico"}, "yubiKeyDesc": {"message": "<PERSON>ử dụng <PERSON> để truy cập tà<PERSON> k<PERSON> của bạn. <PERSON>ạt động với thiết bị <PERSON> 4, 4 <PERSON><PERSON>, 4C và NEO."}, "duoDescV2": {"message": "<PERSON><PERSON><PERSON><PERSON> mã được tạo bởi Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "<PERSON><PERSON><PERSON> <PERSON>h với Duo Security cho tổ chức của bạn sử dụng ứng dụng Duo Mobile, SMS, cuộc gọi điện tho<PERSON><PERSON>, hoặc kho<PERSON> bảo mật U2F.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "<PERSON><PERSON> dụng bất kỳ kh<PERSON>a bảo mật tương thích với WebAuthn nào để truy cập vào tài khoản của bạn."}, "emailTitle": {"message": "Email"}, "emailDescV2": {"message": "<PERSON><PERSON><PERSON><PERSON> mã đ<PERSON><PERSON><PERSON> g<PERSON> về email của bạn."}, "selfHostedEnvironment": {"message": "Môi trường tự lưu trữ"}, "selfHostedBaseUrlHint": {"message": "<PERSON><PERSON><PERSON><PERSON> địa chỉ cơ sở của bản cài đặt Bitwarden được lưu trữ tại máy chủ của bạn. Ví dụ: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "<PERSON><PERSON>i với cấu hình nâng cao. <PERSON>ạn có thể chỉ định địa chỉ cơ sở của mỗi dịch vụ một cách độc lập."}, "selfHostedEnvFormInvalid": {"message": "Bạn phải thêm địa chỉ máy chủ cơ sở hoặc ít nhất một môi trường tùy chỉnh."}, "customEnvironment": {"message": "<PERSON><PERSON>i trường tùy chỉnh"}, "baseUrl": {"message": "URL máy chủ"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "Địa chỉ API máy chủ"}, "webVaultUrl": {"message": "URL máy chủ của trang web kho lưu trữ"}, "identityUrl": {"message": "URL máy chủ nhận dạng"}, "notificationsUrl": {"message": "Notifications Server URL"}, "iconsUrl": {"message": "<PERSON><PERSON><PERSON><PERSON> tượng địa chỉ máy chủ"}, "environmentSaved": {"message": "Địa chỉ môi trường đã đư<PERSON><PERSON> lư<PERSON>."}, "showAutoFillMenuOnFormFields": {"message": "Hiển thị menu tự động điền trên các trường biểu mẫu", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "<PERSON><PERSON><PERSON> gợi ý điền tự động"}, "showInlineMenuLabel": {"message": "Hiển thị các gợi ý tự động điền trên các trường biểu mẫu"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Hiện gợi ý khi nhấp vào biểu tượng"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "<PERSON><PERSON> dụng cho tất cả tài khoản đã đăng nhập."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Tắt cài đặt trình quản lý mật khẩu tích hợp trong trình duyệt của bạn để tránh xung đột."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Thay đổi cài đặt của trình duy<PERSON>t."}, "autofillOverlayVisibilityOff": {"message": "Tắt", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "<PERSON>hi trườ<PERSON> đ<PERSON><PERSON><PERSON> (khi bấm vào)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "<PERSON><PERSON> chọn biểu tượng tự động điền", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Tự động điền khi tải trang"}, "enableAutoFillOnPageLoad": {"message": "Tự động điền khi tải trang"}, "enableAutoFillOnPageLoadDesc": {"message": "<PERSON><PERSON><PERSON> phát hiện biểu mẫu đăng nhập, thực hiện tự động điền khi trang web tải xong."}, "experimentalFeature": {"message": "<PERSON><PERSON><PERSON> trang web bị xâm phạm hoặc không đáng tin cậy có thể khai thác tính năng tự động điền khi tải trang."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "<PERSON><PERSON>m hiểu thêm về rủi ro"}, "learnMoreAboutAutofill": {"message": "<PERSON><PERSON>m hiểu thêm về tự động điền"}, "defaultAutoFillOnPageLoad": {"message": "<PERSON>ài đặt tự động điền mặc định cho mục đăng nhập"}, "defaultAutoFillOnPageLoadDesc": {"message": "Bạn có thể tắt tự động điền khi tải trang cho mục đăng nhập riêng lẻ từ chế độ xem Chỉnh sửa mục."}, "itemAutoFillOnPageLoad": {"message": "Tự động điền khi tải trang (n<PERSON><PERSON> đ<PERSON><PERSON><PERSON> thiết lập trong T<PERSON> chọn)"}, "autoFillOnPageLoadUseDefault": {"message": "<PERSON><PERSON> dụng thiết lập mặc định"}, "autoFillOnPageLoadYes": {"message": "Tự động điền khi tải trang"}, "autoFillOnPageLoadNo": {"message": "<PERSON><PERSON>ông tự động điền khi tải trang"}, "commandOpenPopup": {"message": "Mở popup kho"}, "commandOpenSidebar": {"message": "Mở kho ở thanh bên"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "Tạo và sao chép một mật khẩu ngẫu nhiên mới vào khay nhớ tạm"}, "commandLockVaultDesc": {"message": "<PERSON><PERSON><PERSON> kho"}, "customFields": {"message": "Trường tùy chỉnh"}, "copyValue": {"message": "Sao chép giá trị"}, "value": {"message": "<PERSON><PERSON><PERSON> trị"}, "newCustomField": {"message": "Trường tùy chỉnh mới"}, "dragToSort": {"message": "<PERSON><PERSON><PERSON> để sắp xếp"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "<PERSON><PERSON><PERSON>"}, "cfTypeHidden": {"message": "<PERSON><PERSON> <PERSON>n đi"}, "cfTypeBoolean": {"message": "Đúng/Sai"}, "cfTypeCheckbox": {"message": "Ô tích chọn"}, "cfTypeLinked": {"message": "<PERSON><PERSON> liên kết", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "<PERSON><PERSON><PERSON> trị liên kết", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "<PERSON><PERSON><PERSON><PERSON> bên ngoài popup để xem mã xác thực trong email của bạn sẽ làm cho popup này đóng lại. Bạn có muốn mở popup này trong một cửa sổ mới để nó không bị đóng?"}, "popupU2fCloseMessage": {"message": "Trình duyệt này không thể xử lý các yêu cầu U2F trong cửa sổ popup này. Bạn có muốn mở popup này trong cửa sổ mới để bạn có thể đăng nhập thông qua U2F?"}, "enableFavicon": {"message": "<PERSON><PERSON>n thị biểu tượng trang web"}, "faviconDesc": {"message": "<PERSON><PERSON><PERSON> thị một ảnh nhận dạng bên cạnh mỗi lần đăng nhập."}, "faviconDescAlt": {"message": "Hiển thị một biểu tượng dễ nhận dạng bên cạnh mỗi mục đăng nhập. Áp dụng với mọi tài khoản đăng nhập trên thiết bị."}, "enableBadgeCounter": {"message": "<PERSON><PERSON>n thị biểu tượng bộ đếm"}, "badgeCounterDesc": {"message": "<PERSON> biết bạn có bao nhiêu thông tin đăng nhập cho trang web hiện tại."}, "cardholderName": {"message": "Tên chủ thẻ"}, "number": {"message": "Số"}, "brand": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>"}, "expirationMonth": {"message": "<PERSON><PERSON><PERSON><PERSON> hế<PERSON> hạn"}, "expirationYear": {"message": "<PERSON><PERSON><PERSON> hế<PERSON> hạn"}, "expiration": {"message": "<PERSON><PERSON><PERSON>"}, "january": {"message": "Tháng 1"}, "february": {"message": "Tháng 2"}, "march": {"message": "Tháng 3"}, "april": {"message": "Tháng 4"}, "may": {"message": "Tháng 5"}, "june": {"message": "Tháng 6"}, "july": {"message": "Tháng 7"}, "august": {"message": "Tháng 8"}, "september": {"message": "Tháng 9"}, "october": {"message": "Tháng 10"}, "november": {"message": "Tháng 11"}, "december": {"message": "Tháng 12"}, "securityCode": {"message": "<PERSON><PERSON> b<PERSON>o mật"}, "ex": {"message": "<PERSON><PERSON> dụ:"}, "title": {"message": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "mr": {"message": "Ông"}, "mrs": {"message": "Bà"}, "ms": {"message": "Chị"}, "dr": {"message": "<PERSON><PERSON><PERSON>"}, "mx": {"message": "Mx"}, "firstName": {"message": "<PERSON><PERSON><PERSON>"}, "middleName": {"message": "<PERSON><PERSON><PERSON>"}, "lastName": {"message": "Họ"}, "fullName": {"message": "Họ và tên"}, "identityName": {"message": "<PERSON><PERSON><PERSON> nhận dạng"}, "company": {"message": "<PERSON><PERSON>ng ty"}, "ssn": {"message": "<PERSON><PERSON> b<PERSON>o hiểm xã hội"}, "passportNumber": {"message": "<PERSON><PERSON> hộ chiếu"}, "licenseNumber": {"message": "Số gi<PERSON>y ph<PERSON>p"}, "email": {"message": "Email"}, "phone": {"message": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "address": {"message": "Địa chỉ"}, "address1": {"message": "Địa chỉ 1"}, "address2": {"message": "Địa chỉ 2"}, "address3": {"message": "Địa chỉ 3"}, "cityTown": {"message": "Q<PERSON>ận/Huyện/<PERSON>h<PERSON> trấn"}, "stateProvince": {"message": "Tỉnh/Thành Phố"}, "zipPostalCode": {"message": "<PERSON><PERSON> b<PERSON><PERSON>"}, "country": {"message": "Quốc gia"}, "type": {"message": "<PERSON><PERSON><PERSON>"}, "typeLogin": {"message": "<PERSON><PERSON><PERSON>"}, "typeLogins": {"message": "<PERSON><PERSON><PERSON>"}, "typeSecureNote": {"message": "<PERSON><PERSON> ch<PERSON> b<PERSON>o mật"}, "typeCard": {"message": "Thẻ"}, "typeIdentity": {"message": "<PERSON><PERSON> t<PERSON>h"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "$TYPE$ mới", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Chỉnh sửa $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "Xem $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "<PERSON><PERSON><PERSON> sử mật kh<PERSON>u"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "Quay lại"}, "collections": {"message": "<PERSON><PERSON> s<PERSON>u tập"}, "nCollections": {"message": "$COUNT$ bộ sưu tập", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "<PERSON><PERSON><PERSON>ch"}, "popOutNewWindow": {"message": "Mở trong cửa sổ mới"}, "refresh": {"message": "<PERSON><PERSON><PERSON>"}, "cards": {"message": "Thẻ"}, "identities": {"message": "<PERSON><PERSON> t<PERSON>h"}, "logins": {"message": "<PERSON><PERSON><PERSON>"}, "secureNotes": {"message": "<PERSON><PERSON> ch<PERSON> b<PERSON>o mật"}, "sshKeys": {"message": "SSH Keys"}, "clear": {"message": "Xoá", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "<PERSON><PERSON><PERSON> tra xem mật khẩu có bị lộ không."}, "passwordExposed": {"message": "Mật khẩu này đã bị lộ $VALUE$ lần trong các báo cáo lộ lọt dữ liệu. Bạn nên thay đổi nó.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "<PERSON><PERSON>t khẩu này không được tìm thấy trong bất kỳ báo cáo lộ lọt dữ liệu nào được biết đến. Bạn có thể tiếp tục sử dụng nó."}, "baseDomain": {"message": "<PERSON><PERSON><PERSON> miền c<PERSON> sở", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Base domain (recommended)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "<PERSON><PERSON><PERSON>", "description": "Domain name. Ex. website.com"}, "host": {"message": "<PERSON><PERSON><PERSON>", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "<PERSON><PERSON><PERSON>"}, "startsWith": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>u với"}, "regEx": {"message": "<PERSON><PERSON><PERSON><PERSON> thức ch<PERSON>h quy", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "<PERSON><PERSON> phù hợp", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "<PERSON><PERSON> phù hợp mặc định", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Bật/tắt tùy chọn"}, "toggleCurrentUris": {"message": "Bật/tắt URI hiện tại", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "URI hiện tại", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "<PERSON><PERSON> chức", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "<PERSON><PERSON><PERSON>"}, "allItems": {"message": "<PERSON><PERSON><PERSON> c<PERSON> các mục"}, "noPasswordsInList": {"message": "<PERSON><PERSON><PERSON><PERSON> có mật khẩu để liệt kê."}, "clearHistory": {"message": "<PERSON><PERSON><PERSON> l<PERSON>ch sử"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "Xoá"}, "default": {"message": "Mặc định"}, "dateUpdated": {"message": "<PERSON><PERSON><PERSON> c<PERSON>", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "<PERSON><PERSON> cập nhật mật khẩu", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "Bạn có chắc chắn muốn chọn \"<PERSON>h<PERSON><PERSON> bao giờ\" không? Lựa chọn này sẽ lưu khóa mã hóa kho của bạn trực tiếp trên thiết bị. Hãy nhớ bảo vệ thiết bị của bạn thật cẩn thận nếu bạn chọn tùy chọn này."}, "noOrganizationsList": {"message": "You do not belong to any organizations. Organizations allow you to securely share items with other users."}, "noCollectionsInList": {"message": "<PERSON><PERSON><PERSON><PERSON> có bộ sưu tập nào để liệt kê."}, "ownership": {"message": "<PERSON><PERSON>ền sở hữu"}, "whoOwnsThisItem": {"message": "Ai sở hữu mục này?"}, "strong": {"message": "Mạnh", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h y<PERSON>u"}, "weakMasterPasswordDesc": {"message": "Mật khẩu chính bạn vừa chọn có vẻ yếu. Bạn nên chọn mật khẩu chính (hoặc cụm từ mật khẩu) mạnh để bảo vệ đúng cách tài khoản Bitwarden của bạn. Bạn có thực sự muốn dùng mật khẩu chính này?"}, "pin": {"message": "Mã PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Mở khóa bằng mã PIN"}, "setYourPinTitle": {"message": "Set PIN"}, "setYourPinButton": {"message": "Set PIN"}, "setYourPinCode": {"message": "Đặt mã PIN của bạn để mở khóa Bitwarden. Cài đặt mã PIN của bạn sẽ bị xóa nếu bạn hoàn toàn đăng xuất khỏi ứng dụng."}, "setYourPinCode1": {"message": "Your PIN will be used to unlock Bitwarden instead of your master password. Your PIN will reset if you ever fully log out of Bitwarden."}, "pinRequired": {"message": "Mã PIN là bắt buộc."}, "invalidPin": {"message": "Mã PIN không hợp lệ."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Mã PIN bị gõ sai quá nhiều lần. <PERSON><PERSON> đ<PERSON>ng xu<PERSON>."}, "unlockWithBiometrics": {"message": "Mở khóa bằng sinh trắc học"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "awaitDesktop": {"message": "<PERSON><PERSON><PERSON> x<PERSON>c nhận từ máy tính"}, "awaitDesktopDesc": {"message": "<PERSON><PERSON> lòng xác nhận sử dụng sinh trắc học với ứng dụng Bitwarden trên máy t<PERSON>h."}, "lockWithMasterPassOnRestart": {"message": "<PERSON><PERSON><PERSON><PERSON> với mật khẩu chính khi trình duyệt khởi động lại"}, "lockWithMasterPassOnRestart1": {"message": "Require master password on browser restart"}, "selectOneCollection": {"message": "Bạn phải chọn ít nhất một bộ sưu tập."}, "cloneItem": {"message": "<PERSON><PERSON><PERSON> bản sao của mục"}, "clone": {"message": "T<PERSON><PERSON> bản sao"}, "passwordGenerator": {"message": "<PERSON><PERSON><PERSON><PERSON> tạo mật kh<PERSON>u"}, "usernameGenerator": {"message": "<PERSON><PERSON> tạo tên người dùng"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "Use this password"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "<PERSON><PERSON><PERSON> động khi hết thời gian chờ của kho lưu trữ"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "newCustomizationOptionsCalloutTitle": {"message": "New customization options"}, "newCustomizationOptionsCalloutContent": {"message": "Customize your vault experience with quick copy actions, compact mode, and more!"}, "newCustomizationOptionsCalloutLink": {"message": "View all Appearance settings"}, "lock": {"message": "Khóa", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "<PERSON><PERSON><PERSON><PERSON>c", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "<PERSON><PERSON><PERSON> kiếm thùng r<PERSON>c"}, "permanentlyDeleteItem": {"message": "Xoá vĩnh v<PERSON><PERSON><PERSON>"}, "permanentlyDeleteItemConfirmation": {"message": "Bạn có chắc chắn muốn xóa vĩnh viễn mục này không?"}, "permanentlyDeletedItem": {"message": "Đã x<PERSON>a v<PERSON><PERSON> v<PERSON><PERSON><PERSON>"}, "restoreItem": {"message": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> mục"}, "restoredItem": {"message": "<PERSON><PERSON><PERSON> đã đư<PERSON><PERSON> khôi phục"}, "alreadyHaveAccount": {"message": "Bạn đã có tài k<PERSON>n?"}, "vaultTimeoutLogOutConfirmation": {"message": "Đăng xuất sẽ xóa tất cả quyền truy cập vào kho của bạn và yêu cầu xác minh trực tuyến sau khi hết thời gian chờ. Bạn có chắc chắn muốn sử dụng cài đặt này không?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "<PERSON><PERSON><PERSON> n<PERSON>ận hành động khi hết thời gian chờ"}, "autoFillAndSave": {"message": "Tự động điền và <PERSON>u"}, "fillAndSave": {"message": "Điền và lưu"}, "autoFillSuccessAndSavedUri": {"message": "Đã tự động điền mục và lưu U<PERSON>"}, "autoFillSuccess": {"message": "<PERSON><PERSON> tự động điền mục "}, "insecurePageWarning": {"message": "Cảnh báo: <PERSON><PERSON><PERSON> là một trang HTTP không an toàn, và mọi thông tin bạn nhập ở đây có khả năng được xem & thay đổi bởi người khác. Thông tin Đăng nhập này ban đầu được lưu ở một trang an toàn (HTTPS)."}, "insecurePageWarningFillPrompt": {"message": "Bạn vẫn muốn điền thông tin đăng nhập?"}, "autofillIframeWarning": {"message": "Mẫu điền thông tin này được lưu tại một tên miền khác với URI lưu tại thông tin đăng nhập của bạn. H<PERSON><PERSON> chọn OK để tiếp tục tự động điền, hoặc Hủy bỏ để dừng lại."}, "autofillIframeWarningTip": {"message": "<PERSON><PERSON> chặn cảnh báo này trong tư<PERSON><PERSON> lai, hã<PERSON> lưu URI này, $HOSTNAME$, vào thông tin đăng nhập của bạn cho trang này ở Kho Bitwarden.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "Đặt mật kh<PERSON>u ch<PERSON>h"}, "currentMasterPass": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ch<PERSON>h hiện tại"}, "newMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h mới"}, "confirmNewMasterPass": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>n mật kh<PERSON>u ch<PERSON>h mới"}, "masterPasswordPolicyInEffect": {"message": "Tổ chức của bạn yêu cầu mật khẩu chính của bạn phải đáp <PERSON>ng các yêu cầu sau:"}, "policyInEffectMinComplexity": {"message": "<PERSON><PERSON><PERSON><PERSON> phức tạp tối thiểu của $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Độ dài tối thiểu là $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Chứa chữ cái in hoa"}, "policyInEffectLowercase": {"message": "<PERSON><PERSON><PERSON> một hoặc nhiều kí tự viết thường"}, "policyInEffectNumbers": {"message": "<PERSON><PERSON><PERSON> một hoặc nhiều chữ số"}, "policyInEffectSpecial": {"message": "Chứa ký tự đặc biệt $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "<PERSON><PERSON><PERSON> khẩu ch<PERSON>h bạn chọn không đáp ứng yêu cầu."}, "receiveMarketingEmailsV2": {"message": "<PERSON><PERSON><PERSON><PERSON> đề xuất, thông báo và cơ hội nghiên cứu từ Bitwarden trong hộp thư đến của bạn."}, "unsubscribe": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký"}, "atAnyTime": {"message": "b<PERSON>t c<PERSON> lúc n<PERSON>o."}, "byContinuingYouAgreeToThe": {"message": "<PERSON><PERSON><PERSON> t<PERSON>, bạn đồng <PERSON>"}, "and": {"message": "và"}, "acceptPolicies": {"message": "Bạn đồng ý với những điều sau khi nhấn chọn ô này:"}, "acceptPoliciesRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> khoản sử dụng và <PERSON> sách quyền riêng tư chưa đư<PERSON><PERSON> đồng <PERSON>."}, "termsOfService": {"message": "<PERSON><PERSON><PERSON><PERSON>n sử dụng"}, "privacyPolicy": {"message": "<PERSON><PERSON><PERSON> s<PERSON>ch quyền riêng tư"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "<PERSON><PERSON><PERSON> nhắc mật khẩu không đượ<PERSON> giống mật khẩu của bạn"}, "ok": {"message": "Ok"}, "errorRefreshingAccessToken": {"message": "Lỗi làm mới khoá truy cập"}, "errorRefreshingAccessTokenDesc": {"message": "<PERSON><PERSON><PERSON> có thể đã bị đăng xuất. <PERSON><PERSON> lòng đăng xuất và đăng nhập lại."}, "desktopSyncVerificationTitle": {"message": "<PERSON><PERSON><PERSON>h đồng bộ máy t<PERSON>h"}, "desktopIntegrationVerificationText": {"message": "<PERSON>ui lòng xác minh ứng dụng trên máy tính hiển thị cụm vân tay này: "}, "desktopIntegrationDisabledTitle": {"message": "<PERSON><PERSON><PERSON> hợ<PERSON> trình du<PERSON> ch<PERSON>a đ<PERSON><PERSON><PERSON> kích ho<PERSON>t"}, "desktopIntegrationDisabledDesc": {"message": "<PERSON><PERSON><PERSON> hợp trình duyệt không đư<PERSON><PERSON> thiết lập trong ứng dụng máy tính để bàn Bitwarden. <PERSON>ui lòng thiết lập nó trong cài đặt trong ứng dụng máy tính để bàn."}, "startDesktopTitle": {"message": "Mở ứng dụng Bitwarden trên máy t<PERSON>h"}, "startDesktopDesc": {"message": "Ứng dụng máy tính để bàn Bitwarden cần được khởi động trước khi có thể sử dụng tính năng mở khóa bằng sinh trắc học."}, "errorEnableBiometricTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thể bật nhận dạng sinh trắc học"}, "errorEnableBiometricDesc": {"message": "Hành động đã bị hủy bởi ứng dụng máy tính để bàn"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Ứng dụng máy tính để bàn đã vô hiệu hóa kênh liên lạc an toàn. <PERSON>ui lòng thử lại thao tác này"}, "nativeMessagingInvalidEncryptionTitle": {"message": "<PERSON><PERSON><PERSON> tiế<PERSON> máy t<PERSON>h để bàn bị gián đoạn"}, "nativeMessagingWrongUserDesc": {"message": "Ứng dụng máy tính để bàn được đăng nhập vào một tài khoản khác. H<PERSON>y đảm bảo cả hai ứng dụng được đăng nhập vào cùng một tài khoản."}, "nativeMessagingWrongUserTitle": {"message": "<PERSON><PERSON><PERSON> k<PERSON>n không đ<PERSON>g"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "<PERSON><PERSON> tr<PERSON>c học chưa đư<PERSON><PERSON> cài đặt"}, "biometricsNotEnabledDesc": {"message": "<PERSON>h trắc học trên trình duyệt yêu cầu sinh trắc học trên máy tính phải được cài đặt trước."}, "biometricsNotSupportedTitle": {"message": "<PERSON>hậ<PERSON> dạng sinh trắc học không được hỗ trợ"}, "biometricsNotSupportedDesc": {"message": "Nhận dạng sinh trắc học trên trình duyệt không được hỗ trợ trên thiết bị này"}, "biometricsNotUnlockedTitle": {"message": "Người dùng đã khoá hoặc đã đăng xuất"}, "biometricsNotUnlockedDesc": {"message": "<PERSON><PERSON> lòng mở khóa người dùng này trong ứng dụng máy tính và thử lại."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "<PERSON><PERSON> tr<PERSON><PERSON> học không thành công"}, "biometricsFailedDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> thể hoàn thành sinh trắc học, h<PERSON><PERSON> cân nhắc sử dụng mật khẩu chính hoặc đăng xuất. <PERSON><PERSON><PERSON> sự cố vẫn tiế<PERSON> di<PERSON>, vui lòng liên hệ bộ phận hỗ trợ của Bitwarden."}, "nativeMessaginPermissionErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON> đ<PERSON><PERSON><PERSON> cấp"}, "nativeMessaginPermissionErrorDesc": {"message": "<PERSON><PERSON><PERSON> không đượ<PERSON> phép giao tiếp với Ứng dụng máy tính để bàn <PERSON>, chúng tôi không thể cung cấp sinh trắc học trong tiện ích mở rộng trình duyệt. <PERSON><PERSON> lòng thử lại."}, "nativeMessaginPermissionSidebarTitle": {"message": "Lỗi yêu cầu quyền"}, "nativeMessaginPermissionSidebarDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện hành động này trong thanh bên, vui lòng thử lại hành động trong cửa sổ bật lên hoặc cửa sổ bật ra."}, "personalOwnershipSubmitError": {"message": "<PERSON> Ch<PERSON>h sách do<PERSON>, bạn bị hạn chế lưu các mục vào kho tiền cá nhân của mình. Thay đổi tùy chọn Quyền sở hữu thành một tổ chức và chọn từ các bộ sưu tập có sẵn."}, "personalOwnershipPolicyInEffect": {"message": "<PERSON><PERSON><PERSON> s<PERSON>ch của tổ chức đang ảnh hưởng đến các tùy chọn quyền sở hữu của bạn."}, "personalOwnershipPolicyInEffectImports": {"message": "<PERSON><PERSON><PERSON> sách của tổ chức đã chặn việc nhập các mục vào kho cá nhân của bạn."}, "domainsTitle": {"message": "<PERSON><PERSON><PERSON> tên <PERSON>n", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "<PERSON>ên miền đã loại trừ"}, "excludedDomainsDesc": {"message": "Bitwarden sẽ không yêu cầu lưu thông tin đăng nhập cho các miền này. Bạn phải làm mới trang để các thay đổi có hiệu lực."}, "excludedDomainsDescAlt": {"message": "Bitwarden sẽ không yêu cầu lưu thông tin đăng nhập cho các miền này. Bạn phải làm mới trang để các thay đổi có hiệu lực."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Trang Web $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ không phải là tên miền hợp lệ", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "<PERSON><PERSON><PERSON> thay đổi tên miền loại trừ đã đư<PERSON><PERSON> lưu"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "<PERSON><PERSON><PERSON>"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "<PERSON><PERSON><PERSON> tin"}, "allSends": {"message": "<PERSON><PERSON><PERSON> c<PERSON> mụ<PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "<PERSON><PERSON> hết hạn"}, "passwordProtected": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>u đã đ<PERSON><PERSON><PERSON> bảo vệ"}, "copyLink": {"message": "<PERSON><PERSON> ch<PERSON><PERSON> liên kết"}, "copySendLink": {"message": "<PERSON><PERSON> ch<PERSON><PERSON> liên kết mụ<PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "<PERSON><PERSON><PERSON> mật <PERSON>"}, "delete": {"message": "Xóa"}, "removedPassword": {"message": "Đ<PERSON> xóa mật kh<PERSON>u"}, "deletedSend": {"message": "Đ<PERSON> x<PERSON>a mụ<PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "Đã tắt"}, "removePasswordConfirmation": {"message": "Bạn có chắc chắn muốn xóa mật khẩu này không?"}, "deleteSend": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "Bạn có chắc muốn mục <PERSON> nà<PERSON>?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "<PERSON><PERSON><PERSON>"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "<PERSON><PERSON><PERSON> h<PERSON> hạn"}, "oneDay": {"message": "1 ngày"}, "days": {"message": "$DAYS$ ngày", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "<PERSON><PERSON><PERSON> chỉnh"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "Send mới", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "<PERSON><PERSON><PERSON> mới"}, "sendDisabled": {"message": "Đã loại bỏ Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "<PERSON> ch<PERSON>h s<PERSON>ch do<PERSON>, bạn chỉ có thể xóa những mục <PERSON> hiện có.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "<PERSON><PERSON> tạo m<PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "<PERSON><PERSON> l<PERSON> m<PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "<PERSON><PERSON> chọn tập tin, mở tiện ích mở rộng trong thanh bên (nếu có thể) hoặc mở ra cửa sổ mới bằng cách nhấp vào biểu ngữ này."}, "sendFirefoxFileWarning": {"message": "<PERSON><PERSON> chọn tập tin bằng Firefox, mở tiện ích mở rộng trong thanh bên hoặc mở ra cửa sổ mới bằng cách nhấp vào biểu ngữ này."}, "sendSafariFileWarning": {"message": "<PERSON><PERSON> chọn tập tin bằng Safari, mở ra cửa sổ mới bằng cách nhấp vào biểu ngữ này."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> khi bạn b<PERSON>t đầu"}, "expirationDateIsInvalid": {"message": "<PERSON><PERSON><PERSON> hết hạn bạn nhập không hợp lệ."}, "deletionDateIsInvalid": {"message": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> bạn nh<PERSON>p không hợp lệ."}, "expirationDateAndTimeRequired": {"message": "<PERSON><PERSON><PERSON> và giờ hết hạn là bắt buộc."}, "deletionDateAndTimeRequired": {"message": "Ngày và giờ xóa là bắt buộc."}, "dateParsingError": {"message": "<PERSON><PERSON> xảy ra lỗi khi lưu ngày xoá và ngày hết hạn của bạn."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u ch<PERSON>h"}, "passwordConfirmation": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>n mật kh<PERSON>u ch<PERSON>h"}, "passwordConfirmationDesc": {"message": "<PERSON><PERSON><PERSON> động này đư<PERSON> bảo vệ. <PERSON><PERSON> tiế<PERSON> tụ<PERSON>, h<PERSON><PERSON> nhập lại mật khẩu chính của bạn để xác minh danh tính."}, "emailVerificationRequired": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u xác nhận danh t<PERSON>h qua email"}, "emailVerifiedV2": {"message": "<PERSON><PERSON> đ<PERSON> x<PERSON>c <PERSON>h"}, "emailVerificationRequiredDesc": {"message": "Bạn phải xác nhận email để sử dụng tính năng này. Bạn có thể xác minh email trên web."}, "updatedMasterPassword": {"message": "<PERSON><PERSON> cập nhật mật kh<PERSON>u ch<PERSON>h"}, "updateMasterPassword": {"message": "<PERSON><PERSON><PERSON> nhật mật kh<PERSON>u ch<PERSON>h"}, "updateMasterPasswordWarning": {"message": "M<PERSON>t khẩu chính của bạn gần đây đã được thay đổi bởi người quản trị trong tổ chức của bạn. <PERSON><PERSON> truy cập kho, bạn phải cập nhật nó ngay bây giờ. Việc tiếp tục sẽ đăng xuất khỏi kho và bạn sẽ cần đăng nhập lại. Ứng dụng Bitwaden trên các thiết bị khác có thể tiếp tục hoạt động trong tối đa một giờ sau đó sẽ bị đăng xuất."}, "updateWeakMasterPasswordWarning": {"message": "<PERSON><PERSON><PERSON> khẩu chính của bạn không đáp ứng chính sách tổ chức của bạn. <PERSON><PERSON> truy cập kho, bạn phải cập nhật mật khẩu chính của mình ngay bây giờ. Việc tiếp tục sẽ đăng xuất bạn khỏi phiên hiện tại và bắt buộc đăng nhập lại. <PERSON><PERSON><PERSON> phiên hoạt động trên các thiết bị khác có thể tiếp tục duy trì hoạt động trong tối đa một giờ."}, "tdeDisabledMasterPasswordRequired": {"message": "Tổ chức của bạn đã vô hiệu hóa mã hóa bằng thiết bị đáng tin cậy. <PERSON><PERSON> lòng đặt mật khẩu chính để truy cập <PERSON>ho của bạn."}, "resetPasswordPolicyAutoEnroll": {"message": "<PERSON><PERSON>ng ký tự động"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Tổ chức này có chính sách doanh nghiệp sẽ tự động đặt lại mật khẩu chính cho bạn. Đ<PERSON>ng ký sẽ cho phép quản trị viên tổ chức thay đổi mật khẩu chính của bạn."}, "selectFolder": {"message": "<PERSON><PERSON><PERSON> thư mục..."}, "noFoldersFound": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thư mục nào", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "<PERSON>uyền tổ chức của bạn đã đượ<PERSON> cập nh<PERSON>t, yêu cầu bạn đặt mật khẩu ch<PERSON>h.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "<PERSON><PERSON> chức của bạn yêu cầu bạn đặt mật khẩu ch<PERSON>h.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>h", "description": "Default title for the user verification dialog."}, "hours": {"message": "Giờ"}, "minutes": {"message": "<PERSON><PERSON><PERSON>"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "Tổ chức của bạn đã đặt thời gian mở kho tối đa là $HOURS$ giờ và $MINUTES$ phút.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Tổ chức của bạn đang ảnh hưởng đến thời gian mở kho. Thời gian mở kho tối đa là $HOURS$ giờ và $MINUTES$ phút. Kho sẽ $ACTION$ sau khi hết thời gian mở kho.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Tổ chức của bạn sẽ $ACTION$ sau khi hết thời gian mở kho.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Thời gian mở kho vượt quá giới hạn do tổ chức của bạn đặt ra."}, "vaultExportDisabled": {"message": "<PERSON><PERSON>t kho không có sẵn"}, "personalVaultExportPolicyInEffect": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>h sách của tổ chức ngăn cản bạn xuất kho lưu trữ cá nhân của mình."}, "copyCustomFieldNameInvalidElement": {"message": "<PERSON>h<PERSON>ng thể xác định được phần tử biểu mẫu hợp lệ. Thay vào đó hãy thử kiểm tra trong HTML."}, "copyCustomFieldNameNotUnique": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh tính duy nhất."}, "convertOrganizationEncryptionDesc": {"message": "$ORGANIZATION$ đang sử dụng SSO với khóa máy chủ tự lưu trữ. Mật khẩu chính không còn cần để đăng nhập cho các thành viên của tổ chức này.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "leaveOrganization": {"message": "<PERSON><PERSON><PERSON> tổ chức"}, "removeMasterPassword": {"message": "<PERSON><PERSON><PERSON> mật kh<PERSON> ch<PERSON>h"}, "removedMasterPassword": {"message": "Đ<PERSON> xóa mật kh<PERSON>u ch<PERSON>h"}, "leaveOrganizationConfirmation": {"message": "Bạn có chắc chắn muốn rời tổ chức này không?"}, "leftOrganization": {"message": "Bạn đã rời khỏi tổ chức."}, "toggleCharacterCount": {"message": "<PERSON><PERSON><PERSON> tắt đếm kí tự"}, "sessionTimeout": {"message": "<PERSON><PERSON><PERSON> đăng nhập của bạn đã hết hạn. <PERSON>ui lòng quay trở lại và thử đăng nhập lại."}, "exportingPersonalVaultTitle": {"message": "<PERSON><PERSON> xuất dữ liệu kho cá nhân"}, "exportingIndividualVaultDescription": {"message": "Chỉ dữ liệu trong kho cá nhân liên kết với $EMAIL$ mới được xuất. Không bao gồm \ncác dữ liệu trong kho tổ chức. Chỉ thông tin mục kho mới được xuất, sẽ không có các tệp đính kèm.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "<PERSON><PERSON> xuất dữ liệu kho tổ chức"}, "exportingOrganizationVaultDesc": {"message": "Chỉ dữ liệu trong kho tổ chức $ORGANIZATION$ mới được xuất. C<PERSON><PERSON> kho cá nhân hoặc của tổ chức khác sẽ không được bao gồm.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Lỗi"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "<PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> dùng"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Địa chỉ email có hậu tố", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Sử dụng khả năng địa chỉ phụ của nhà cung cấp dịch vụ mail của bạn."}, "catchallEmail": {"message": "Email Catch-all"}, "catchallEmailDesc": {"message": "<PERSON><PERSON> dụng hộp thư bạn đã thiết lập để nhận tất cả email gửi đến tên miền của bạn."}, "random": {"message": "Ngẫu nhiên"}, "randomWord": {"message": "Từ ngẫu nhiên"}, "websiteName": {"message": "Tên website"}, "service": {"message": "<PERSON><PERSON><PERSON> v<PERSON>"}, "forwardedEmail": {"message": "<PERSON><PERSON> chuyển tiếp b<PERSON> danh email"}, "forwardedEmailDesc": {"message": "<PERSON><PERSON><PERSON> b<PERSON> danh email v<PERSON>i dịch vụ chuyển tiếp bên ngo<PERSON>i."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "Lỗi $SERVICENAME$: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "<PERSON><PERSON><PERSON><PERSON> tạo bởi Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Trang web: $WEBSITE$. <PERSON><PERSON><PERSON><PERSON> tạo bởi Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Khoá API $SERVICENAME$ không hợp lệ", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Khoá API $SERVICENAME$ không hợp lệ: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "<PERSON><PERSON><PERSON><PERSON> thể lấy ID tà<PERSON> k<PERSON><PERSON>n email ẩn từ $SERVICENAME$.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Tên miền $SERVICENAME$ không hợp lệ.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Địa chỉ $SERVICENAME$ không hợp lệ.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "$SERVICENAME$ đã xảy ra lỗi không xác định.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "<PERSON><PERSON><PERSON><PERSON> chuyển tiếp không xác đ<PERSON>nh: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "<PERSON><PERSON><PERSON> m<PERSON> chủ", "description": "Part of a URL."}, "apiAccessToken": {"message": "<PERSON><PERSON> thông báo truy cập <PERSON>"}, "apiKey": {"message": "Khóa API"}, "ssoKeyConnectorError": {"message": "Lỗi kết nối khóa: hã<PERSON> đảm bảo kết nối khóa khả dụng và hoạt động chính xác."}, "premiumSubcriptionRequired": {"message": "<PERSON><PERSON><PERSON> cầu đăng ký gói Premium"}, "organizationIsDisabled": {"message": "<PERSON><PERSON> chức đã ngưng hoạt động."}, "disabledOrganizationFilterError": {"message": "<PERSON><PERSON><PERSON><PERSON> thể truy cập các mục trong tổ chức đã ngưng hoạt động. <PERSON><PERSON><PERSON> liên hệ với chủ sở hữu tổ chức để được hỗ trợ."}, "loggingInTo": {"message": "<PERSON><PERSON> đăng nhập vào $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "<PERSON><PERSON><PERSON> b<PERSON>n m<PERSON> chủ"}, "selfHostedServer": {"message": "tự lưu trữ"}, "thirdParty": {"message": "<PERSON><PERSON><PERSON>ứ ba"}, "thirdPartyServerMessage": {"message": "Bạn đang kết nối đến máy chủ $SERVERNAME$ của bên thứ ba. <PERSON><PERSON> lòng kiểm tra lỗi bằng cách sử dụng máy chủ chính thức hoặc báo lỗi cho bên thứ ba.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "nh<PERSON>n thấy lần cuối: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "<PERSON><PERSON><PERSON> nhập bằng mật kh<PERSON>u ch<PERSON>h"}, "newAroundHere": {"message": "Bạn mới tới đây sao?"}, "rememberEmail": {"message": "Ghi nhớ email"}, "loginWithDevice": {"message": "<PERSON><PERSON><PERSON> nhập bằng thiết bị"}, "fingerprintPhraseHeader": {"message": "Cụm vân tay"}, "fingerprintMatchInfo": {"message": "<PERSON><PERSON> lòng đảm bảo rằng bạn đã mở khoá kho và cụm vân tay khớp trên thiết bị khác."}, "resendNotification": {"message": "<PERSON><PERSON><PERSON> lại thông báo"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "<PERSON><PERSON><PERSON> thông báo đã đư<PERSON><PERSON> gửi đến thiết bị của bạn."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "<PERSON><PERSON><PERSON> đầu đăng nh<PERSON>p"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ch<PERSON>h bị lộ"}, "exposedMasterPasswordDesc": {"message": "Mật khẩu này đã bị rò rỉ trong một vụ tấn công dữ liệu. Dùng mật khẩu mới và an toàn để bảo vệ tài khoản bạn. Bạn có chắc muốn sử dụng mật khẩu đã bị rò rỉ?"}, "weakAndExposedMasterPassword": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>u ch<PERSON>h yếu và bị lộ"}, "weakAndBreachedMasterPasswordDesc": {"message": "Mật khẩu yếu này đã bị rò rỉ trong một vụ tấn công dữ liệu. Dùng mật khẩu mới và an toàn để bảo vệ tài khoản bạn. Bạn có chắc muốn sử dụng mật khẩu đã bị rò rỉ?"}, "checkForBreaches": {"message": "<PERSON><PERSON><PERSON> tra mật khẩu có lộ trong các vụ rò rỉ dữ liệu hay không"}, "important": {"message": "<PERSON><PERSON> trọng:"}, "masterPasswordHint": {"message": "<PERSON><PERSON><PERSON> khẩu ch<PERSON>h của bạn không thể phục hồi nếu bạn quên nó!"}, "characterMinimum": {"message": "$LENGTH$ ký tự tối thiểu", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "<PERSON><PERSON><PERSON> s<PERSON>ch quản lí của bạn đã bật chức năng tự động điền khi tải trang."}, "howToAutofill": {"message": "<PERSON><PERSON><PERSON> tự đồng điền"}, "autofillSelectInfoWithCommand": {"message": "<PERSON>ọn một mục từ màn hình nà<PERSON>, sử dụng phím tắt $COMMAND$, hoặc khám phá các tùy chọn khác trong cài đặt.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "<PERSON>ọ<PERSON> một mục từ màn hình này, hoặc khám phá các tùy chọn khác trong cài đặt."}, "gotIt": {"message": "<PERSON><PERSON> hiểu"}, "autofillSettings": {"message": "<PERSON>ài đặt tự động điền"}, "autofillKeyboardShortcutSectionTitle": {"message": "<PERSON><PERSON><PERSON> tắt tự động điền"}, "autofillKeyboardShortcutUpdateLabel": {"message": "<PERSON>hay đổi phím tắt"}, "autofillKeyboardManagerShortcutsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> lý c<PERSON>c lố<PERSON> t<PERSON>t"}, "autofillShortcut": {"message": "<PERSON><PERSON><PERSON> tắt tự động điền"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "<PERSON><PERSON><PERSON> tắt mặc định cho chức năng tự động điền: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Mở trong cửa sổ mới"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "<PERSON><PERSON><PERSON> cầu phê duyệt thiết bị. <PERSON><PERSON><PERSON> một tuỳ chọn phê duyệt bên dưới:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "<PERSON><PERSON><PERSON> bị n<PERSON>y"}, "uncheckIfPublicDevice": {"message": "Bỏ chọn nếu sử dụng thiết bị công cộng"}, "approveFromYourOtherDevice": {"message": "<PERSON><PERSON> du<PERSON> bằng thiết bị khác"}, "requestAdminApproval": {"message": "<PERSON><PERSON><PERSON> cầu quản trị viên phê duyệt"}, "ssoIdentifierRequired": {"message": "<PERSON><PERSON><PERSON> có mã định danh SSO của tổ chức."}, "creatingAccountOn": {"message": "<PERSON><PERSON> tạo tài k<PERSON>n trên"}, "checkYourEmail": {"message": "<PERSON><PERSON><PERSON> tra <PERSON> c<PERSON><PERSON> b<PERSON>n"}, "followTheLinkInTheEmailSentTo": {"message": "<PERSON><PERSON><PERSON><PERSON> và<PERSON> liên kết trong email đ<PERSON><PERSON><PERSON> gửi đến"}, "andContinueCreatingYourAccount": {"message": "và tiếp tục tạo tài k<PERSON>n của bạn."}, "noEmail": {"message": "Không có email?"}, "goBack": {"message": "Quay lại"}, "toEditYourEmailAddress": {"message": "để chỉnh sửa địa chỉ email của bạn."}, "eu": {"message": "Châu Âu", "description": "European Union"}, "accessDenied": {"message": "<PERSON><PERSON><PERSON> cậ<PERSON> bị từ chối. <PERSON><PERSON><PERSON> không có quyền xem trang này."}, "general": {"message": "<PERSON>"}, "display": {"message": "<PERSON><PERSON><PERSON> thị"}, "accountSuccessfullyCreated": {"message": "Tạo tài khoản thành công!"}, "adminApprovalRequested": {"message": "<PERSON><PERSON><PERSON> cầu quản trị viên phê duyệt"}, "adminApprovalRequestSentToAdmins": {"message": "<PERSON><PERSON><PERSON> cầu của bạn đã được gửi đến quản trị viên."}, "troubleLoggingIn": {"message": "Không thể đăng nhập?"}, "loginApproved": {"message": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>ng nhập đã du<PERSON>"}, "userEmailMissing": {"message": "<PERSON><PERSON><PERSON><PERSON> email người dùng"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "<PERSON><PERSON><PERSON><PERSON> bị tin cậy"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsNoItemsTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> có mục <PERSON>i nào đang hoạt động", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsNoItemsMessage": {"message": "Sử dụng Gửi để chia sẻ thông tin mã hóa một cách an toàn với bất kỳ ai.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "<PERSON>rư<PERSON><PERSON> này là b<PERSON> bu<PERSON>."}, "required": {"message": "bắt buộc"}, "search": {"message": "<PERSON><PERSON><PERSON>"}, "inputMinLength": {"message": "G<PERSON><PERSON> trị nhập vào phải ít nhất $COUNT$ ký tự.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "<PERSON><PERSON><PERSON> trị nhập vào không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá $COUNT$ ký tự.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "<PERSON><PERSON><PERSON> ký tự sau không đ<PERSON><PERSON><PERSON> phép sử dụng: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "<PERSON><PERSON><PERSON> trị nhập vào phải ít nhất $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "<PERSON><PERSON><PERSON> trị nhập vào không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "<PERSON><PERSON> <PERSON>t nhất 1 địa chỉ email không hợp lệ"}, "inputTrimValidator": {"message": "<PERSON><PERSON><PERSON> trị nhập vào không được chỉ có khoảng trắng.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "<PERSON><PERSON><PERSON> trị nhập vào không phải là địa chỉ email."}, "fieldsNeedAttention": {"message": "Có $COUNT$ trường cần bạn xem xét ở trên.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- <PERSON><PERSON><PERSON> --"}, "multiSelectPlaceholder": {"message": "-- <PERSON><PERSON><PERSON><PERSON> để lọc --"}, "multiSelectLoading": {"message": "<PERSON><PERSON> tải các tu<PERSON> chọn..."}, "multiSelectNotFound": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mục nào"}, "multiSelectClearAll": {"message": "<PERSON><PERSON><PERSON> tất cả"}, "plusNMore": {"message": "+ $QUANTITY$ nhiều hơn", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "<PERSON>u con"}, "toggleCollapse": {"message": "B<PERSON>t/tắt thu gọn", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "<PERSON><PERSON><PERSON> miền thay thế"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "<PERSON><PERSON><PERSON> mục yêu cầu nhập lại mật khẩu chính không thể tự động điền khi tải trang. Tự động điền khi tải trang đã tắt.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Tự động điền khi tải trang được đặt thành mặc định trong cài đặt.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Tắt yêu cầu nhập lại mật khẩu chính để chỉnh sửa trường này", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Ẩn/hiện thanh điều hướng bên"}, "skipToContent": {"message": "<PERSON><PERSON><PERSON><PERSON> đến nội dung"}, "bitwardenOverlayButton": {"message": "Nút menu tự động điền Bitwarden", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Bật/tắt menu tự động điền Bitwarden", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "<PERSON><PERSON> tự động điền Bitwarden", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Mở khóa tài khoản của bạn để xem các thông tin đăng nhập phù hợp", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Mở khóa tài k<PERSON>n", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "<PERSON><PERSON><PERSON><PERSON> thông tin đăng nhập cho", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i dùng từng phần", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "<PERSON><PERSON><PERSON><PERSON> có mục nào để hiển thị", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "<PERSON><PERSON><PERSON>", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "<PERSON><PERSON><PERSON><PERSON> mục mới", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "New identity", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "<PERSON><PERSON> sách tự động điền của Bitwarden sẵn sàng. Sử dụng phím mũi tên xuống để chọn.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "<PERSON><PERSON><PERSON>"}, "ignore": {"message": "Bỏ qua"}, "importData": {"message": "<PERSON><PERSON><PERSON><PERSON> liệu", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Lỗi nhập"}, "importErrorDesc": {"message": "<PERSON><PERSON> vấn đề với dữ liệu bạn cố gắng nhập. Vui lòng khắc phục các lỗi được liệt kê bên dưới trong tập tin nguồn của bạn và thử lại."}, "resolveTheErrorsBelowAndTryAgain": {"message": "<PERSON><PERSON><PERSON><PERSON> quyết các lỗi bên dưới và thử lại."}, "description": {"message": "<PERSON><PERSON>"}, "importSuccess": {"message": "<PERSON><PERSON> liệu đã đư<PERSON><PERSON> nhập thành công"}, "importSuccessNumberOfItems": {"message": "<PERSON><PERSON> nhập tổng cộng $AMOUNT$ mục.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "<PERSON><PERSON><PERSON> lại"}, "verificationRequiredForActionSetPinToContinue": {"message": "<PERSON><PERSON><PERSON> x<PERSON>c minh cho thao tác này. Hãy đặt mã PIN để tiếp tục."}, "setPin": {"message": "<PERSON><PERSON><PERSON><PERSON> lập mã PIN"}, "verifyWithBiometrics": {"message": "<PERSON><PERSON><PERSON> thực bằng sinh trắc học"}, "awaitingConfirmation": {"message": "<PERSON><PERSON> chờ x<PERSON>c <PERSON>n"}, "couldNotCompleteBiometrics": {"message": "<PERSON><PERSON><PERSON><PERSON> thể hoàn tất sinh trắc học."}, "needADifferentMethod": {"message": "<PERSON><PERSON><PERSON> một ph<PERSON><PERSON>ng pháp kh<PERSON>c?"}, "useMasterPassword": {"message": "<PERSON><PERSON><PERSON> mật kh<PERSON>u ch<PERSON>h"}, "usePin": {"message": "Dùng mã PIN"}, "useBiometrics": {"message": "<PERSON><PERSON><PERSON> sinh tr<PERSON><PERSON> h<PERSON>c"}, "enterVerificationCodeSentToEmail": {"message": "<PERSON><PERSON><PERSON><PERSON> mã x<PERSON>c minh đ<PERSON><PERSON><PERSON> g<PERSON>i đến email củ<PERSON> bạn."}, "resendCode": {"message": "<PERSON><PERSON><PERSON> lại mã"}, "total": {"message": "Tổng"}, "importWarning": {"message": "Bạn đang nhập dữ liệu vào $ORGANIZATION$. Dữ liệu của bạn có thể được chia sẻ với các thành viên của tổ chức này. Bạn có muốn tiếp tục không?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Lỗi kết nối với dịch vụ Duo. Sử dụng phương thức đăng nhập hai bước khác hoặc liên hệ với Duo để được hỗ trợ."}, "duoRequiredForAccount": {"message": "<PERSON><PERSON><PERSON> kho<PERSON>n của bạn yêu cầu xác minh hai bước với Duo."}, "popoutExtension": {"message": "Tiện ích mở rộng dạng cửa sổ bật lên"}, "launchDuo": {"message": "Khởi chạy Dou"}, "importFormatError": {"message": "<PERSON><PERSON> liệu không được định dạng đúng. <PERSON><PERSON> lòng kiểm tra tập tin nhập và thử lại."}, "importNothingError": {"message": "<PERSON><PERSON><PERSON><PERSON> có gì đư<PERSON> nhập."}, "importEncKeyError": {"message": "Lỗi giải mã tập tin đã xuất. Khóa mã hóa của bạn không khớp với khóa mã hóa được sử dụng để xuất dữ liệu."}, "invalidFilePassword": {"message": "<PERSON><PERSON><PERSON> khẩu tập tin không hợp lệ, vui lòng sử dụng mật khẩu bạn đã nhập khi xuất tập tin."}, "destination": {"message": "<PERSON><PERSON><PERSON>"}, "learnAboutImportOptions": {"message": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON> các tu<PERSON> chọn nhập của bạn"}, "selectImportFolder": {"message": "<PERSON><PERSON><PERSON> th<PERSON> mục"}, "selectImportCollection": {"message": "<PERSON><PERSON><PERSON> bộ s<PERSON>u tập"}, "importTargetHint": {"message": "Chọn tùy chọn này để di chuyển nội dung tập tin đã được nhập đến $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "<PERSON><PERSON><PERSON> tin chứa các mục không xác định."}, "selectFormat": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>nh dạng tập tin nhập"}, "selectImportFile": {"message": "<PERSON><PERSON><PERSON> tập tin nhập"}, "chooseFile": {"message": "<PERSON><PERSON><PERSON> tập tin"}, "noFileChosen": {"message": "<PERSON><PERSON><PERSON> chọn tập tin nào"}, "orCopyPasteFileContents": {"message": "hoặc sao chép/dán nội dung của tập tin nhập"}, "instructionsFor": {"message": "Hướng dẫn dùng $NAME$", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "<PERSON><PERSON><PERSON> nh<PERSON> nh<PERSON>p kho"}, "confirmVaultImportDesc": {"message": "Tập tin này được bảo vệ bằng mật khẩu. <PERSON><PERSON> lòng nhập mật khẩu để nhập dữ liệu."}, "confirmFilePassword": {"message": "<PERSON><PERSON><PERSON><PERSON> lại mật khẩu tập tin"}, "exportSuccess": {"message": "<PERSON><PERSON> xuất dữ liệu kho của bạn"}, "typePasskey": {"message": "Mã khoá"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "<PERSON><PERSON><PERSON>ng thể sao chép mã khoá"}, "passkeyNotCopiedAlert": {"message": "Bản sao sẽ không bao gồm mã khoá. Bạn có muốn tiếp tục tạo bản sao mục này?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Trang web yêu cầu xác minh. <PERSON><PERSON><PERSON> năng này hiện chưa được hỗ trợ cho tài khoản không có mật khẩu chính."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "Ứng dụng này đã có mã khoá."}, "noPasskeysFoundForThisApplication": {"message": "<PERSON><PERSON><PERSON>ng có mã khoá cho ứng dụng này."}, "noMatchingPasskeyLogin": {"message": "<PERSON><PERSON>n không có thông tin đăng nhập phù hợp cho trang web này."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "<PERSON><PERSON><PERSON>"}, "savePasskey": {"message": "<PERSON><PERSON><PERSON> mã kho<PERSON>"}, "savePasskeyNewLogin": {"message": "<PERSON><PERSON><PERSON> mã khoá như đăng nhập mới"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "Mục mã kho<PERSON>"}, "overwritePasskey": {"message": "Ghi đè mã khoá?"}, "overwritePasskeyAlert": {"message": "<PERSON><PERSON>c này đã chứa mã khoá. Bạn có chắc muốn ghi đè mã khoá hiện tại không?"}, "featureNotSupported": {"message": "Chưa hỗ trợ tính năng này"}, "yourPasskeyIsLocked": {"message": "<PERSON><PERSON><PERSON> cầu xác thực để sử dụng mã khoá. <PERSON><PERSON><PERSON> minh danh tính của bạn để tiếp tục."}, "multifactorAuthenticationCancelled": {"message": "<PERSON><PERSON> hủy xác thực đa yếu tố"}, "noLastPassDataFound": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu LastPass"}, "incorrectUsernameOrPassword": {"message": "<PERSON>ên người dùng hoặc mật khẩu không đúng"}, "incorrectPassword": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng"}, "incorrectCode": {"message": "<PERSON><PERSON> không đúng"}, "incorrectPin": {"message": "Mã PIN không đúng"}, "multifactorAuthenticationFailed": {"message": "<PERSON><PERSON><PERSON> thực đa yếu tố thất bại"}, "includeSharedFolders": {"message": "<PERSON><PERSON> g<PERSON><PERSON> các thư mục đư<PERSON>c chia sẻ"}, "lastPassEmail": {"message": "<PERSON><PERSON>"}, "importingYourAccount": {"message": "<PERSON><PERSON> nhập tà<PERSON> của bạn..."}, "lastPassMFARequired": {"message": "<PERSON><PERSON><PERSON> cầu xác thực đa yếu tố LastPass"}, "lastPassMFADesc": {"message": "<PERSON><PERSON><PERSON><PERSON> mã OTP từ ứng dụng xác thực của bạn"}, "lastPassOOBDesc": {"message": "<PERSON><PERSON> duyệt yêu cầu đăng nhập trên ứng dụng xác thực của bạn hoặc nhập mã OTP."}, "passcode": {"message": "<PERSON><PERSON><PERSON> mã"}, "lastPassMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON>h <PERSON>"}, "lastPassAuthRequired": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>c thực <PERSON>"}, "awaitingSSO": {"message": "<PERSON><PERSON> chờ xác thực SSO"}, "awaitingSSODesc": {"message": "<PERSON><PERSON> lòng tiếp tục đăng nhập bằng thông tin đăng nhập của công ty bạn."}, "seeDetailedInstructions": {"message": "<PERSON><PERSON> h<PERSON> dẫn chi tiết trên trang trợ giúp của chúng tôi tại", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp từ LastPass"}, "importFromCSV": {"message": "<PERSON><PERSON><PERSON><PERSON> từ CSV"}, "lastPassTryAgainCheckEmail": {"message": "Thử lại hoặc tìm email từ LastPass để xác minh đó là bạn."}, "collection": {"message": "<PERSON><PERSON> <PERSON>"}, "lastPassYubikeyDesc": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>a <PERSON> đư<PERSON>c liên kết với tài khoản LastPass của bạn vào cổng USB của máy t<PERSON>, sau đó nhấn nút trên <PERSON>."}, "switchAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> tà<PERSON>n"}, "switchAccounts": {"message": "Chuyển đổi tài k<PERSON>n"}, "switchToAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> sang tài k<PERSON>n"}, "activeAccount": {"message": "<PERSON><PERSON><PERSON> k<PERSON>n đang hoạt động"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "<PERSON><PERSON><PERSON> tà<PERSON> k<PERSON>n kh<PERSON> dụng"}, "accountLimitReached": {"message": "Số lượng tài khoản đã đạt giới hạn. Đăng xuất khỏi một tài khoản để thêm tài khoản khác."}, "active": {"message": "<PERSON><PERSON><PERSON> động"}, "locked": {"message": "<PERSON><PERSON> kh<PERSON>a"}, "unlocked": {"message": "đã mở khóa"}, "server": {"message": "m<PERSON><PERSON> chủ"}, "hostedAt": {"message": "<PERSON><PERSON><PERSON><PERSON> lưu trữ tại"}, "useDeviceOrHardwareKey": {"message": "Sử dụng thiết bị hoặc khóa phần cứng của bạn"}, "justOnce": {"message": "Chỉ một lần"}, "alwaysForThisSite": {"message": "<PERSON><PERSON><PERSON> cho trang này"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ đã đư<PERSON>c thêm vào danh sách các tên miền loại trừ.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "<PERSON><PERSON><PERSON> d<PERSON>ng chung", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Tiế<PERSON> tục tới <PERSON>ài đặt trình du<PERSON>t?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Tiế<PERSON> tục tới Trung tâm trợ giúp?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Thay đổi cài đặt tự động điền và quản lý mật khẩu của trình duyệt của bạn.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "Bạn có thể xem và đặt các phím tắt của tiện ích mở rộng trong phần cài đặt trình duyệt.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Thay đổi cài đặt tự động điền và quản lý mật khẩu của trình duyệt của bạn.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "Bạn có thể xem và thiết lập các phím tắt của tiện ích mở rộng trong phần cài đặt trình duyệt.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Đặt Bitwarden làm trình quản lý mật khẩu mặc định của bạn?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Bỏ qua tùy chọn này có thể gây ra xung đột giữa các đề xuất tự động điền của Bitwarden và trình duyệt của bạn.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Bitwarden làm trình quản lý mật khẩu mặc định", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thể đặt Bit<PERSON>en làm trình quản lý mật khẩu mặc định", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "Bạn phải cấp quyền riêng tư của trình duyệt cho Bitwarden để đặt nó làm trình quản lý mật khẩu mặc định.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Đặt làm mặc định", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Thông tin đăng nhập đã lưu thành công!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Thông tin đăng nhập đã được cập nhật thành công!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "<PERSON><PERSON><PERSON> ra lỗi trong quá trình lưu thông tin đăng nhập. <PERSON><PERSON><PERSON> tra bảng điều khiển để biết thêm chi tiết.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "<PERSON><PERSON><PERSON><PERSON> công"}, "removePasskey": {"message": "Xóa mã khoá"}, "passkeyRemoved": {"message": "Đã xóa mã khoá"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "<PERSON><PERSON><PERSON> thông tin đăng nhập cho trang này để tự động điền"}, "yourVaultIsEmpty": {"message": "<PERSON><PERSON> c<PERSON>a bạn trống"}, "noItemsMatchSearch": {"message": "<PERSON><PERSON><PERSON><PERSON> có kết quả nào phù hợp với tìm kiếm của bạn"}, "clearFiltersOrTryAnother": {"message": "<PERSON><PERSON><PERSON> bộ lọc hoặc thử từ khóa tìm kiếm khác"}, "copyInfoTitle": {"message": "Sao chép thông tin - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Sao chép ghi chú - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> ch<PERSON>, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tùy chọn - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "<PERSON>em m<PERSON> - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Tự động điền - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "<PERSON><PERSON><PERSON><PERSON> có giá trị để sao chép"}, "assignToCollections": {"message": "<PERSON><PERSON> vào bộ sưu tập"}, "copyEmail": {"message": "Sao chép email"}, "copyPhone": {"message": "<PERSON><PERSON> ch<PERSON>p số điện thoại"}, "copyAddress": {"message": "<PERSON>o ch<PERSON>p địa chỉ"}, "adminConsole": {"message": "<PERSON><PERSON><PERSON> điều khiển dành cho quản trị viên"}, "accountSecurity": {"message": "<PERSON><PERSON><PERSON> mật tà<PERSON>n"}, "notifications": {"message": "<PERSON><PERSON><PERSON><PERSON> báo"}, "appearance": {"message": "<PERSON><PERSON><PERSON>"}, "errorAssigningTargetCollection": {"message": "Lỗi khi gán vào bộ sưu tập chỉ định."}, "errorAssigningTargetFolder": {"message": "Lỗi khi gán vào thư mục chỉ định."}, "viewItemsIn": {"message": "Xem các mục trong $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Quay lại $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "<PERSON><PERSON><PERSON>"}, "removeItem": {"message": "Xoá $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "<PERSON><PERSON><PERSON> mục không đ<PERSON><PERSON><PERSON> phân lo<PERSON>i"}, "itemDetails": {"message": "<PERSON> ti<PERSON>t mục"}, "itemName": {"message": "<PERSON><PERSON><PERSON>"}, "organizationIsDeactivated": {"message": "<PERSON><PERSON> chức không còn hoạt động"}, "owner": {"message": "Ch<PERSON> sở hữu"}, "selfOwnershipLabel": {"message": "Bạn", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "<PERSON><PERSON><PERSON><PERSON> thể truy cập các mục trong tổ chức đã ngưng hoạt động. <PERSON><PERSON><PERSON> liên hệ với chủ sở hữu tổ chức để được hỗ trợ."}, "additionalInformation": {"message": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung"}, "itemHistory": {"message": "<PERSON><PERSON><PERSON> sử mục"}, "lastEdited": {"message": "Chỉnh sửa lần cuối"}, "ownerYou": {"message": "Chủ sở hữu: Bạn"}, "linked": {"message": "<PERSON><PERSON> liên kết"}, "copySuccessful": {"message": "<PERSON><PERSON> chép thành công"}, "upload": {"message": "<PERSON><PERSON><PERSON>"}, "addAttachment": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> đ<PERSON>m"}, "maxFileSizeSansPunctuation": {"message": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tối đa của tập tin là 500MB"}, "deleteAttachmentName": {"message": "Xoá tệp đ<PERSON>h kèm $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Tải xuống $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Bạn có chắc chắn muốn xóa vĩnh viễn tệp đ<PERSON>h kèm này không?"}, "premium": {"message": "<PERSON> cấp"}, "freeOrgsCannotUseAttachments": {"message": "<PERSON><PERSON><PERSON> tổ chức miễn phí không thể sử dụng tệp đ<PERSON>h kèm"}, "filters": {"message": "<PERSON><PERSON> lọc"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Thông tin cá nhân"}, "identification": {"message": "ID"}, "contactInfo": {"message": "<PERSON>h<PERSON>ng tin liên hệ"}, "downloadAttachment": {"message": "T<PERSON>i x<PERSON> - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Th<PERSON>ng tin đăng nhập"}, "authenticatorKey": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c thực"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Thông tin thẻ"}, "cardBrandDetails": {"message": "Chi tiết $BRAND$", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "loading": {"message": "<PERSON><PERSON> t<PERSON>"}, "data": {"message": "<PERSON><PERSON> liệu"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Gán"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Chỉ những thành viên của tổ chức có quyền truy cập vào các bộ sưu tập này mới có thể xem mục này."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Chỉ những thành viên của tổ chức có quyền truy cập vào các bộ sưu tập này mới có thể xem các mục này."}, "bulkCollectionAssignmentWarning": {"message": "Bạn đã chọn $TOTAL_COUNT$ mục. Bạn không thể cập nhật $READONLY_COUNT$ mục vì bạn không có quyền chỉnh sửa.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON>"}, "add": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fieldType": {"message": "Loại trường"}, "fieldLabel": {"message": "Ti<PERSON><PERSON> đề trường"}, "textHelpText": {"message": "<PERSON><PERSON> dụng các trường nhập liệu văn bản cho dữ liệu như câu hỏi bảo mật"}, "hiddenHelpText": {"message": "<PERSON><PERSON> dụng các trư<PERSON><PERSON> nhập li<PERSON>u <PERSON>n cho thông tin nhạy cảm nh<PERSON> mật kh<PERSON>u"}, "checkBoxHelpText": {"message": "<PERSON><PERSON><PERSON> các ô tích chọn nếu bạn muốn tự động điền vào ô tích chọn của biểu mẫu, chẳng hạn như ghi nhớ email"}, "linkedHelpText": {"message": "Sử dụng trường nhập liệu đã liên kết khi bạn gặp vấn đề với việc tự động điền trên một trang web cụ thể."}, "linkedLabelHelpText": {"message": "<PERSON><PERSON><PERSON><PERSON> thông tin định danh của trường như id, name, aria-label hoặc placeholder."}, "editField": {"message": "Chỉnh sửa trường"}, "editFieldLabel": {"message": "Chỉnh sửa $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Xoá $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "Đã thêm $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Sắp xếp lại $LABEL$. Sử dụng phím mũi tên để di chuyển mục lên hoặc xuống.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ đã di chuyển lên vị trí $INDEX$ / $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "<PERSON><PERSON><PERSON> bộ sưu tập để gán"}, "personalItemTransferWarningSingular": {"message": "1 mục sẽ được chuyển vĩnh viễn đến tổ chức đã chọn. Bạn sẽ không còn sở hữu mục này nữa."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ mục sẽ được chuyển vĩnh viễn đến tổ chức đã chọn. Bạn sẽ không còn sở hữu các mục này nữa.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 mục sẽ được chuyển vĩnh viễn đến $ORG$. Bạn sẽ không còn sở hữu mục này nữa.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ mục sẽ được chuyển vĩnh viễn đến $ORG$. Bạn sẽ không còn sở hữu các mục này nữa.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Đ<PERSON> gán vào bộ sưu tập thành công"}, "nothingSelected": {"message": "Bạn chưa chọn g<PERSON>."}, "itemsMovedToOrg": {"message": "<PERSON><PERSON><PERSON> mục đã đư<PERSON>c chuyển tới $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "M<PERSON>c đã đư<PERSON><PERSON> chuyển tới $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ đã di chuyển xuống vị trí $INDEX$ / $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "<PERSON><PERSON> trí mục"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "Compact mode"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "Bạn không thể xóa các bộ sưu tập với quyền chỉ xem: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBody": {"message": "Autofill items for the current page\nFavorite items for easy access\nSearch your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}}