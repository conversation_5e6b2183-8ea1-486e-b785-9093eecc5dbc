{"appName": {"message": "bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "बिटवार्डन पासवर्ड मैनेजर", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "घर पर, काम पर या चलते-फिरते, बिटवर्डन आपके सभी पासवर्ड, पासकी और संवेदनशील जानकारी को आसानी से सुरक्षित रखता है", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "अपनी सुरक्षित तिजोरी में प्रवेश करने के लिए नया खाता बनाएं या लॉग इन करें।"}, "inviteAccepted": {"message": "आमंत्रण स्वीकृत"}, "createAccount": {"message": "Create Account"}, "newToBitwarden": {"message": "बिटवार्डन का परिचय"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "सिंगल साइन-ऑन प्रयोग करें"}, "welcomeBack": {"message": "आपका पुन: स्वागत है!"}, "setAStrongPassword": {"message": "मजबूत पासवर्ड सेट करें"}, "finishCreatingYourAccountBySettingAPassword": {"message": "पासवर्ड सेट करके अपना खाता निर्माण पूरा करें"}, "enterpriseSingleSignOn": {"message": "उद्यम एकल साइन-ऑन"}, "cancel": {"message": "रद्<PERSON> करें"}, "close": {"message": "ब<PERSON><PERSON> करें"}, "submit": {"message": "<PERSON><PERSON><PERSON> करें"}, "emailAddress": {"message": "Email Address"}, "masterPass": {"message": "Master Password"}, "masterPassDesc": {"message": "मास्टर पासवर्ड वह पासवर्ड है जो तिजोरी में प्रवेश के लिए प्रयोग होता है। आप मास्टर पासवर्ड ना भूले, यह अतिआवश्यक है। भूलने की अवस्था में पासवर्ड को दोबारा पाना संभव नहीं होगा।"}, "masterPassHintDesc": {"message": "मास्टर पासवर्ड संकेत आपको भूल जाने की अवस्था में पासवर्ड को याद करने में सहायता करता है।"}, "masterPassHintText": {"message": "If you forget your password, the password hint can be sent to your email. $CURRENT$/$MAXIMUM$ character maximum.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Re-type Master Password"}, "masterPassHint": {"message": "Master Password Hint (optional)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Join organization"}, "joinOrganizationName": {"message": "Join $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Finish joining this organization by setting a master password."}, "tab": {"message": "टैब"}, "vault": {"message": "तिजोरी"}, "myVault": {"message": "My Vault"}, "allVaults": {"message": "सभी तिजोरियाँ"}, "tools": {"message": "उपकरण"}, "settings": {"message": "सेटिंग्स"}, "currentTab": {"message": "Current Tab"}, "copyPassword": {"message": "Copy Password"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "Copy Note"}, "copyUri": {"message": "U<PERSON> को कॉपी करें"}, "copyUsername": {"message": "<PERSON><PERSON>"}, "copyNumber": {"message": "Copy Number"}, "copySecurityCode": {"message": "Copy Security Code"}, "copyName": {"message": "नाम कॉपी करें"}, "copyCompany": {"message": "कंपनी के नाम को कॉपी करें"}, "copySSN": {"message": "Copy Social Security number"}, "copyPassportNumber": {"message": "Copy passport number"}, "copyLicenseNumber": {"message": "Copy license number"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "Copy notes"}, "copy": {"message": "Copy", "description": "Copy to clipboard"}, "fill": {"message": "Fill", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "स्वत:भरण"}, "autoFillLogin": {"message": "स्वचालित लॉगिन विवरण"}, "autoFillCard": {"message": "स्वचालित कार्ड विवरण"}, "autoFillIdentity": {"message": "स्वचालित पहचान विवरण"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "Generate Password (copied)"}, "copyElementIdentifier": {"message": "कस्टम फील्ड नाम कॉपी करें"}, "noMatchingLogins": {"message": "कोई मेल-मिला लॉगिन नहीं |"}, "noCards": {"message": "कोई कार्ड उपलब्ध नहीं"}, "noIdentities": {"message": "कोई पहचान उपलब्ध नहीं"}, "addLoginMenu": {"message": "लॉगिन जोड़ें"}, "addCardMenu": {"message": "कार्ड जोड़ें"}, "addIdentityMenu": {"message": "पहचान जोड़ें"}, "unlockVaultMenu": {"message": "आपकी तिजोरी का ताला खोलें"}, "loginToVaultMenu": {"message": "अपने अकाउंट में लॉगिन करें"}, "autoFillInfo": {"message": "इस ब्राउज़र टैब के लिए स्वत: भरण लॉगिन उपलब्ध नहीं है।"}, "addLogin": {"message": "Add a Login"}, "addItem": {"message": "Add Item"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "मास्टर पासवर्ड संकेत प्राप्त करें"}, "continue": {"message": "जारी रखें"}, "sendVerificationCode": {"message": "एक सत्यापन कोड अपने ईमेल पर भेजें"}, "sendCode": {"message": "कोड भेजें"}, "codeSent": {"message": "कोड भेजा गया है"}, "verificationCode": {"message": "Verification Code"}, "confirmIdentity": {"message": "आगे बढ़ने के लिए अपने पहचान की पुष्टि करें"}, "changeMasterPassword": {"message": "Change Master Password"}, "continueToWebApp": {"message": "Continue to web app?"}, "continueToWebAppDesc": {"message": "Explore more features of your Bitwarden account on the web app."}, "continueToHelpCenter": {"message": "Continue to Help Center?"}, "continueToHelpCenterDesc": {"message": "Learn more about how to use Bitwarden on the Help Center."}, "continueToBrowserExtensionStore": {"message": "Continue to browser extension store?"}, "continueToBrowserExtensionStoreDesc": {"message": "Help others find out if <PERSON><PERSON><PERSON> is right for them. Visit your browser's extension store and leave a rating now."}, "changeMasterPasswordOnWebConfirmation": {"message": "You can change your master password on the Bitwarden web app."}, "fingerprintPhrase": {"message": "Fingerprint Phrase", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "आपके खाते का फिंगरप्रिंट वाक्यांश", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Two-step <PERSON><PERSON>"}, "logOut": {"message": "Log Out"}, "aboutBitwarden": {"message": "बिटवार्डन का परिचय"}, "about": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ी"}, "moreFromBitwarden": {"message": "More from Bitwarden"}, "continueToBitwardenDotCom": {"message": "Continue to bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden for Business"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden Authenticator allows you to store authenticator keys and generate TOTP codes for 2-step verification flows. Learn more on the bitwarden.com website"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Securely store, manage, and share developer secrets with Bitwarden Secrets Manager. Learn more on the bitwarden.com website."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Create smooth and secure login experiences free from traditional passwords with Passwordless.dev. Learn more on the bitwarden.com website."}, "freeBitwardenFamilies": {"message": "Free Bitwarden Families"}, "freeBitwardenFamiliesPageDesc": {"message": "You are eligible for Free Bitwarden Families. Redeem this offer today in the web app."}, "version": {"message": "संस्करण"}, "save": {"message": "सेव करें"}, "move": {"message": "ले जाएं"}, "addFolder": {"message": "Add Folder"}, "name": {"message": "नाम"}, "editFolder": {"message": "Edit <PERSON>"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "New folder"}, "folderName": {"message": "Folder name"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "Delete Folder"}, "folders": {"message": "फ़ोल्डर्स"}, "noFolders": {"message": "सूचीबद्ध करने के लिए कोई फ़ोल्डर नहीं हैं।"}, "helpFeedback": {"message": "Help & Feedback"}, "helpCenter": {"message": "बिटवॉर्डेन सहायता केंद्र"}, "communityForums": {"message": "Bitwarden सामुदायिक मंचों का अन्वेषण करें"}, "contactSupport": {"message": "बिटवॉर्डेन सहायता से संपर्क करें"}, "sync": {"message": "सिंक"}, "syncVaultNow": {"message": "Sync Vault Now"}, "lastSync": {"message": "Last Sync:"}, "passGen": {"message": "Password Generator"}, "generator": {"message": "उत्पन्न करें!", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "स्वचालित रूप से अपने लॉगिन के लिए मजबूत, अद्वितीय पासवर्ड उत्पन्न करते हैं।"}, "bitWebVaultApp": {"message": "Bitwarden web app"}, "importItems": {"message": "Import Items"}, "select": {"message": "चयन करें"}, "generatePassword": {"message": "Generate Password"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "Regenerate Password"}, "options": {"message": "विकल्प"}, "length": {"message": "लंबाई"}, "include": {"message": "Include", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Include numbers", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Include special characters", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "Number of Words"}, "wordSeparator": {"message": "Word Separator"}, "capitalize": {"message": "कैपिटलाइज़ करें", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "नंबर शामिल करें"}, "minNumbers": {"message": "Minimum Numbers"}, "minSpecial": {"message": "Minimum Special"}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "एंटरप्राइज़ नीति आवश्यकताएँ आपके जनरेटर विकल्पों पर लागू की गई हैं.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "वॉल्ट खोजे"}, "edit": {"message": "संपादन करें"}, "view": {"message": "देखें"}, "noItemsInList": {"message": "सूचीबद्ध करने के लिए कोई आइटम नहीं हैं।"}, "itemInformation": {"message": "Item Information"}, "username": {"message": "उपयोगकर्ता नाम"}, "password": {"message": "पासवर्ड"}, "totp": {"message": "Authenticator secret"}, "passphrase": {"message": "पासफ़्रेज़"}, "favorite": {"message": "Favorite"}, "unfavorite": {"message": "Unfavorite"}, "itemAddedToFavorites": {"message": "Item added to favorites"}, "itemRemovedFromFavorites": {"message": "<PERSON><PERSON> removed from favorites"}, "notes": {"message": "नोट्स"}, "privateNote": {"message": "Private note"}, "note": {"message": "नोट:"}, "editItem": {"message": "<PERSON>em"}, "folder": {"message": "फ़ोल्डर"}, "deleteItem": {"message": "Delete Item"}, "viewItem": {"message": "View Item"}, "launch": {"message": "खोलें"}, "launchWebsite": {"message": "Launch website"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "वेबसाइट"}, "toggleVisibility": {"message": "Toggle Visibility"}, "manage": {"message": "प्रबंधित करना"}, "other": {"message": "अन्य"}, "unlockMethods": {"message": "Unlock options"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Set up an unlock method to change your vault timeout action."}, "unlockMethodNeeded": {"message": "Set up an unlock method in Settings"}, "sessionTimeoutHeader": {"message": "Session timeout"}, "vaultTimeoutHeader": {"message": "तिजोरी टाइमआउट"}, "otherOptions": {"message": "Other options"}, "rateExtension": {"message": "Rate the Extension"}, "browserNotSupportClipboard": {"message": "आपका वेब ब्राउज़र आसान क्लिपबोर्ड कॉपीिंग का समर्थन नहीं करता है। इसके बजाय इसे मैन्युअल रूप से कॉपी करें।"}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "आपकी वॉल्ट लॉक हो गई है। जारी रखने के लिए अपने मास्टर पासवर्ड को सत्यापित करें।"}, "yourVaultIsLockedV2": {"message": "तिजोरी पर लॉक लगा है"}, "yourAccountIsLocked": {"message": "आपका खाता बंद है."}, "or": {"message": "या"}, "unlock": {"message": "ताला खोलें"}, "loggedInAsOn": {"message": "$HOSTNAME$ पर $EMAIL$ के रूप में लॉग इन किया है।", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "अमान्य मास्टर पासवर्ड"}, "vaultTimeout": {"message": "वॉल्ट मध्यांतर"}, "vaultTimeout1": {"message": "टाइमआउट"}, "lockNow": {"message": "Lock Now"}, "lockAll": {"message": "Lock all"}, "immediately": {"message": "तत्‍काल"}, "tenSeconds": {"message": "10 सेकंड"}, "twentySeconds": {"message": "20 सेकंड"}, "thirtySeconds": {"message": "30 सेकंड"}, "oneMinute": {"message": "1 मिनट"}, "twoMinutes": {"message": "2 मिनट"}, "fiveMinutes": {"message": "5 मिनट"}, "fifteenMinutes": {"message": "15 मिनट"}, "thirtyMinutes": {"message": "30 मिनट"}, "oneHour": {"message": "1 घंटा"}, "fourHours": {"message": "4 घंटे"}, "onLocked": {"message": "On Locked"}, "onRestart": {"message": "On Restart"}, "never": {"message": "कभी नहीं"}, "security": {"message": "सुरक्षा"}, "confirmMasterPassword": {"message": "Confirm master password"}, "masterPassword": {"message": "Master password"}, "masterPassImportant": {"message": "Your master password cannot be recovered if you forget it!"}, "masterPassHintLabel": {"message": "Master password hint"}, "errorOccurred": {"message": "कोई ग़लती हुई।"}, "emailRequired": {"message": "ई-मेल पते की आवश्यकता है।"}, "invalidEmail": {"message": "अमान्य ई-मेल |"}, "masterPasswordRequired": {"message": "मास्टर पासवर्ड की आवश्यकता है।"}, "confirmMasterPasswordRequired": {"message": "मास्टर पासवर्ड पुनः डालने की आवश्यकता है।"}, "masterPasswordMinlength": {"message": "मास्टर पासवर्ड कम से कम $VALUE$ अक्षर लंबा होना चाहिए।", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "मास्टर पासवर्ड पुष्टि मेल नहीं खाती है।"}, "newAccountCreated": {"message": "आपका नया खाता बनाया गया है! अब आप लॉग इन कर सकते हैं।"}, "newAccountCreated2": {"message": "Your new account has been created!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "youSuccessfullyLoggedIn": {"message": "You successfully logged in"}, "youMayCloseThisWindow": {"message": "You may close this window"}, "masterPassSent": {"message": "हमने आपको अपने मास्टर पासवर्ड संकेत के साथ एक ईमेल भेजा है।"}, "verificationCodeRequired": {"message": "सत्यापन टोकन आवश्यक है"}, "webauthnCancelOrTimeout": {"message": "The authentication was cancelled or took too long. Please try again."}, "invalidVerificationCode": {"message": "सत्यापन कोड अवैध है"}, "valueCopied": {"message": "$VALUE$ कॉपी हो गया है।", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Unable to auto-fill the selected login on this page. Copy/paste your username and/or password instead."}, "totpCaptureError": {"message": "Unable to scan QR code from the current webpage"}, "totpCaptureSuccess": {"message": "Authenticator key added"}, "totpCapture": {"message": "Scan authenticator QR code from current webpage"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Copy Authenticator key (TOTP)"}, "loggedOut": {"message": "लॉग आउट"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "अपने लॉगिन सत्र समाप्त हो गया है।"}, "logIn": {"message": "Log in"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "आपके ईमेल पर भेजा गया कोड भरें"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "अपने प्रमाणक ऐप से कोड डालें"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "क्या आप वाकई लॉग आउट करना चाहते हैं?"}, "yes": {"message": "हाँ"}, "no": {"message": "नहीं"}, "location": {"message": "Location"}, "unexpectedError": {"message": "An unexpected error has occured."}, "nameRequired": {"message": "नाम आवश्यक है"}, "addedFolder": {"message": "जोड़ा गया फ़ोल्डर"}, "twoStepLoginConfirmation": {"message": "Two-step login makes your account more secure by requiring you to enter a security code from an authenticator app whenever you log in. Two-step login can be enabled on the bitwarden.com web vault. Do you want to visit the website now?"}, "twoStepLoginConfirmationContent": {"message": "बिटवर्डन वेब ऐप में दो-चरणीय लॉगिन सेट करके अपने खाते को अधिक सुरक्षित बनाएं।"}, "twoStepLoginConfirmationTitle": {"message": "वेब ऐप पर जाएं?"}, "editedFolder": {"message": "Edited Folder"}, "deleteFolderConfirmation": {"message": "क्या आप वाकई इस फ़ोल्डर को हटाना चाहते हैं?"}, "deletedFolder": {"message": "हटाए गए फ़ोल्डर"}, "gettingStartedTutorial": {"message": "Getting Started Tutorial"}, "gettingStartedTutorialVideo": {"message": "ब्राउज़र एक्सटेंशन का सबसे अधिक जानने के लिए हमारे शुरू ट्यूटोरियल देखें।"}, "syncingComplete": {"message": "सिंकिंग पूर्ण"}, "syncingFailed": {"message": "सिंकिंग असफल।"}, "passwordCopied": {"message": "कूटशब्द की नकल हुइ"}, "uri": {"message": "यूआरआइ"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "नया URI"}, "addDomain": {"message": "Add domain", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "जोड़ा गया आइटम"}, "editedItem": {"message": "संपादित आइटम "}, "deleteItemConfirmation": {"message": "क्या आप वास्तव में थ्रैश में भेजना चाहते हैं?"}, "deletedItem": {"message": "थ्रैश में भेजे"}, "overwritePassword": {"message": "Overwrite Password"}, "overwritePasswordConfirmation": {"message": "क्या आप सुनिश्चित हैं कि आप वर्तमान पासवर्ड को ओवरराइट करना चाहते हैं?"}, "overwriteUsername": {"message": "उपयोगकर्ता नाम अधिलेखित करें"}, "overwriteUsernameConfirmation": {"message": "क्या आप वाकई वर्तमान उपयोगकर्ता नाम को अधिलेखित करना चाहते हैं?"}, "searchFolder": {"message": "फोल्डर में खोजें"}, "searchCollection": {"message": "श्रेणी खोजें"}, "searchType": {"message": "तलाश की विधि"}, "noneFolder": {"message": "No Folder", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "लॉगिन जोड़ने के लिए कहें"}, "vaultSaveOptionsTitle": {"message": "Save to vault options"}, "addLoginNotificationDesc": {"message": "The \"Add Login Notification\" automatically prompts you to save new logins to your vault whenever you log into them for the first time."}, "addLoginNotificationDescAlt": {"message": "Ask to add an item if one isn't found in your vault. Applies to all logged in accounts."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "टैब पेज पर कार्ड दिखाएं"}, "showCardsCurrentTabDesc": {"message": "आसान ऑटो-फिल के लिए टैब पेज पर कार्ड आइटम सूचीबद्ध करें।"}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "टैब पेज पर पहचान दिखाएं"}, "showIdentitiesCurrentTabDesc": {"message": "आसान ऑटो-फिल के लिए टैब पेज पर कार्ड आइटम सूचीबद्ध करें।"}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "क्लिपबोर्ड खाली करें", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "स्वचालित रूप से अपने क्लिपबोर्ड से कॉपी की गई मानों को साफ़ करें।", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Should bitwarden remember this password for you?"}, "notificationAddSave": {"message": "Yes, Save Now"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "loginSaveConfirmation": {"message": "$ITEMNAME$ saved to Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is saved."}, "loginUpdatedConfirmation": {"message": "$ITEMNAME$ updated in Bitwarden.", "placeholders": {"itemName": {"content": "$1"}}, "description": "Shown to user after item is updated."}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "मौजूदा लॉगिन को अपडेट करने के लिए कहें"}, "changedPasswordNotificationDesc": {"message": "किसी वेबसाइट पर परिवर्तन का पता चलने पर लॉगिन का पासवर्ड अपडेट करने के लिए कहें।"}, "changedPasswordNotificationDescAlt": {"message": "Ask to update a login's password when a change is detected on a website. Applies to all logged in accounts."}, "enableUsePasskeys": {"message": "पासकी सहेजने और उपयोग करने के लिए पूछें"}, "usePasskeysDesc": {"message": "नई पासकी सहेजने के लिए कहें या अपनी तिजोरी में संग्रहीत पासकी से लॉग इन करें। सभी लॉग इन किए गए खातों पर लागू होता है।"}, "notificationChangeDesc": {"message": "क्या आप बिटवर्डन में इस पासवर्ड को अपडेट करना चाहते हैं?"}, "notificationChangeSave": {"message": "Yes, Update Now"}, "notificationUnlockDesc": {"message": "Unlock your Bitwarden vault to complete the autofill request."}, "notificationUnlock": {"message": "Unlock"}, "additionalOptions": {"message": "Additional options"}, "enableContextMenuItem": {"message": "संदर्भ मेनू विकल्प दिखाएं"}, "contextMenuItemDesc": {"message": "वेबसाइट के लिए पासवर्ड जेनरेशन और मैचिंग लॉग इन तक पहुंचने के लिए सेकेंडरी क्लिक का उपयोग करें। "}, "contextMenuItemDescAlt": {"message": "Use a secondary click to access password generation and matching logins for the website. Applies to all logged in accounts."}, "defaultUriMatchDetection": {"message": "डिफॉल्ट URI मैच डिटेक्शन", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "ऑटो-फिल जैसे कार्यों को करते समय लॉगिन के लिए URI मैच डिटेक्शन को संभाले जाने का डिफ़ॉल्ट तरीका चुनें। "}, "theme": {"message": "थीम"}, "themeDesc": {"message": "Change the application's color theme."}, "themeDescAlt": {"message": "Change the application's color theme. Applies to all logged in accounts."}, "dark": {"message": "अंधेरा", "description": "Dark color"}, "light": {"message": "प्रक<PERSON>श", "description": "Light color"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "Export <PERSON>"}, "fileFormat": {"message": "File Format"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "File password"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "warning": {"message": "चेतावनी", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "वॉल्ट निर्यात की पुष्टि करें"}, "exportWarningDesc": {"message": "This export contains your vault data in an unencrypted format. You should not store or send the exported file over unsecure channels (such as email). Delete it immediately after you are done using it."}, "encExportKeyWarningDesc": {"message": "यह आपके खाते की एन्क्रिप्शन कुंजी का उपयोग करके आपके डेटा को एन्क्रिप्ट करता है।यदि आप कभी भी अपने खाते की एन्क्रिप्शन कुंजी को खोते हैं तो आपको फिर से निर्यात करना चाहिए क्योंकि आप इस निर्यात फ़ाइल को डिक्रिप्ट करने में सक्षम नहीं होंगे।"}, "encExportAccountWarningDesc": {"message": "खाता एन्क्रिप्शन कुंजी प्रत्येक बिटवर्डन उपयोगकर्ता खाते के लिए अद्वितीय हैं, इसलिए आप एन्क्रिप्टेड निर्यात को किसी अन्य खाते में आयात नहीं कर सकते हैं।"}, "exportMasterPassword": {"message": "अपने वॉल्ट डेटा को निर्यात करने के लिए अपना मास्टर पासवर्ड दर्ज करें।"}, "shared": {"message": "साझा किया गया"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "संगठन में ले जाएँ"}, "movedItemToOrg": {"message": "$ITEMNAME$ $ORGNAME$ गया ", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "एक संगठन चुनें जिसे आप इस आइटम को स्थानांतरित करना चाहते हैं।किसी संगठन में जाने से उस संगठन को आइटम का स्वामित्व हस्तांतरित होता है।एक बार इसे स्थानांतरित करने के बाद आप अब इस आइटम के प्रत्यक्ष स्वामी नहीं होंगे।"}, "learnMore": {"message": "अधिक जानें"}, "authenticatorKeyTotp": {"message": "Authenticator Key (TOTP)"}, "verificationCodeTotp": {"message": "Verification Code (TOTP)"}, "copyVerificationCode": {"message": "Copy Verification Code"}, "attachments": {"message": "अटॅचमेंट्स"}, "deleteAttachment": {"message": "अटैचमेंट हटाएं"}, "deleteAttachmentConfirmation": {"message": "क्या आप वाकई इस अटैचमेंट को हटाना चाहते हैं?"}, "deletedAttachment": {"message": "हटाए गए अटैचमेंट"}, "newAttachment": {"message": "Add New Attachment"}, "noAttachments": {"message": "कोई अटैचमेंट नहीं।"}, "attachmentSaved": {"message": "अटैचमेंट बच गया है।"}, "file": {"message": "फ़ाइल"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "फ़ाइल का चयन करें।"}, "maxFileSize": {"message": "अधिकतम फाइल आकार 500 MB है।"}, "featureUnavailable": {"message": "Feature Unavailable"}, "encryptionKeyMigrationRequired": {"message": "Encryption key migration required. Please login through the web vault to update your encryption key."}, "premiumMembership": {"message": "Premium Membership"}, "premiumManage": {"message": "Manage Membership"}, "premiumManageAlert": {"message": "आप वेब वॉल्ट bitwarden.com पर अपनी सदस्यता का प्रबंधन कर सकते हैं।क्या आप अब वेबसाइट पर जाना चाहते हैं?"}, "premiumRefresh": {"message": "Refresh Membership"}, "premiumNotCurrentMember": {"message": "आप वर्तमान में प्रीमियम सदस्य नहीं हैं।"}, "premiumSignUpAndGet": {"message": "प्रीमियम सदस्यता के लिए साइन अप करें और प्राप्त करें:"}, "ppremiumSignUpStorage": {"message": "1 GB of encrypted file storage."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "Proprietary two-step login options such as YubiKey and Duo."}, "ppremiumSignUpReports": {"message": "अपनी वॉल्ट को सुरक्षित रखने के लिए पासवर्ड स्वच्छता, खाता स्वास्थ्य और डेटा उल्लंघन रिपोर्ट।"}, "ppremiumSignUpTotp": {"message": "अपनी तिजोरी में लॉगिन के लिए TOTP सत्यापन कोड (2FA) जनरेटर।"}, "ppremiumSignUpSupport": {"message": "प्राथमिकता ग्राहक सहायता"}, "ppremiumSignUpFuture": {"message": "भविष्य के सभी प्रीमियम फीचर्स। और जल्द ही आ रहा है!"}, "premiumPurchase": {"message": "Purchase Premium"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "आप एक प्रीमियम सदस्य हैं!"}, "premiumCurrentMemberThanks": {"message": "Thank you for supporting bitwarden."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "All for just $PRICE$ /year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "ताज़ा पूरा"}, "enableAutoTotpCopy": {"message": "TOTP को स्वचालित रूप से कॉपी करें"}, "disableAutoTotpCopyDesc": {"message": "यदि आपके लॉगिन में एक प्रमाणक कुंजी जुड़ी हुई है, तो जब भी आप लॉगिन को ऑटो-फिल करते हैं तो TOTP सत्यापन कोड स्वचालित रूप से आपके क्लिपबोर्ड पर कॉपी किया जाता है।"}, "enableAutoBiometricsPrompt": {"message": "लॉन्च पर बायोमेट्रिक्स के लिए पूछें"}, "premiumRequired": {"message": "Premium Required"}, "premiumRequiredDesc": {"message": "इस सुविधा का उपयोग करने के लिए प्रीमियम सदस्यता की आवश्यकता होती है।"}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "ईमेल $EMAIL$ को भेजा गया।", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "अपने कंप्यूटर के यूएसबी पोर्ट में अपनी सुरक्षा कुंजी डालें। अगर इसमें कोई बटन है तो उसे टच करें।\n"}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "वेबऑथन प्रमाणित करें"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "Login Unavailable"}, "noTwoStepProviders": {"message": "This account has two-step login enabled, however, none of the configured two-step providers are supported by this web browser."}, "noTwoStepProviders2": {"message": "कृपया एक समर्थित वेब ब्राउज़र (जैसे क्रोम) और/या अतिरिक्त प्रदाताओं का उपयोग करें जो वेब ब्राउज़र (जैसे एक प्रमाणक ऐप) में बेहतर समर्थित हैं।"}, "twoStepOptions": {"message": "Two-step Login Options"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "अपने दो कारक प्रदाताओं के सभी के लिए उपयोग खो दिया है? अपने खाते से सभी दो-कारक प्रदाताओं को अक्षम करने के लिए अपने रिकवरी कोड का उपयोग करें।"}, "recoveryCodeTitle": {"message": "Recovery Code"}, "authenticatorAppTitle": {"message": "Authenticator App"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP Security Key"}, "yubiKeyDesc": {"message": "अपने खाते तक पहुंचने के लिए YubiKey का उपयोग करें। YubiKey 4, 4 नैनो, 4C, और NEO उपकरणों के साथ काम करता है।"}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Verify with Duo Security for your organization using the Duo Mobile app, SMS, phone call, or U2F security key.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 वेबऑथन"}, "webAuthnDesc": {"message": "अपने खाते तक पहुंचने के लिए किसी भी WebAuthn सक्षम सुरक्षा कुंजी का उपयोग करें।"}, "emailTitle": {"message": "ईमेल"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "selfHostedEnvironment": {"message": "Self-hosted Environment"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "Custom Environment"}, "baseUrl": {"message": "सर्वर URL"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API Server URL"}, "webVaultUrl": {"message": "Web Vault Server URL"}, "identityUrl": {"message": "Identity Server URL"}, "notificationsUrl": {"message": "Notifications Server URL"}, "iconsUrl": {"message": "Icons Server URL"}, "environmentSaved": {"message": "पर्यावरण URL को बचाया गया है।"}, "showAutoFillMenuOnFormFields": {"message": "Show autofill menu on form fields", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "टकराव से बचने के लिए अपने ब्राउज़र की अंतर्निहित पासवर्ड प्रबंधक सेटिंग को बंद करें."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "ब्राउज़र सेटिंग संपादित करें."}, "autofillOverlayVisibilityOff": {"message": "Off", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "When autofill icon is selected", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Enable Auto-fill On Page Load."}, "enableAutoFillOnPageLoadDesc": {"message": "यदि लॉगिन फॉर्म का पता चलता है, तो वेब पेज लोड होने पर स्वचालित रूप से ऑटो-फिल करें।"}, "experimentalFeature": {"message": "Compromised or untrusted websites can exploit autofill on page load."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "Learn more about autofill"}, "defaultAutoFillOnPageLoad": {"message": "लॉगिन आइटम के लिए डिफ़ॉल्ट ऑटोफिल सेटिंग"}, "defaultAutoFillOnPageLoadDesc": {"message": "पेज लोड पर ऑटो-फिल को सक्षम करने के बाद, आप व्यक्तिगत लॉगिन आइटम के लिए सुविधा को सक्षम या अक्षम कर सकते हैं।यह लॉगिन आइटम के लिए डिफ़ॉल्ट सेटिंग है जो अलग से कॉन्फ़िगर नहीं हैं।"}, "itemAutoFillOnPageLoad": {"message": "पेज लोड पर ऑटो-भरें (यदि विकल्पों में सक्षम हैं)"}, "autoFillOnPageLoadUseDefault": {"message": "डिफ़ॉल्ट सेटिंग का उपयोग करें"}, "autoFillOnPageLoadYes": {"message": "पेज लोड पर ऑटो भरें"}, "autoFillOnPageLoadNo": {"message": "पेज लोड पर ऑटो-फिल न करें"}, "commandOpenPopup": {"message": "ओपन वॉल्ट पॉपअप"}, "commandOpenSidebar": {"message": "साइडबार में वॉल्ट खोले"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "Generate and copy a new random password to the clipboard."}, "commandLockVaultDesc": {"message": "वॉल्ट लॉक करें"}, "customFields": {"message": "Custom Fields"}, "copyValue": {"message": "Copy Value"}, "value": {"message": "मूल्य"}, "newCustomField": {"message": "New Custom Field"}, "dragToSort": {"message": "सॉर्ट करने के लिए ड्रैग करें"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "शब<PERSON>द"}, "cfTypeHidden": {"message": "छुपा हुआ"}, "cfTypeBoolean": {"message": "बूलियन"}, "cfTypeCheckbox": {"message": "Checkbox"}, "cfTypeLinked": {"message": "जुड़ा हुआ", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "लिंक्ड मान", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "अपने सत्यापन कोड के लिए अपने ईमेल की जांच करने के लिए पॉपअप विंडो के बाहर क्लिक करने से यह पॉपअप बंद हो जाएगा।क्या आप इस पॉपअप को एक नई विंडो में खोलना चाहते हैं ताकि यह बंद न हो?"}, "popupU2fCloseMessage": {"message": "यह ब्राउज़र इस पॉपअप विंडो में U2F अनुरोधों को संसाधित नहीं कर सकता है।क्या आप इस पॉपअप को एक नई विंडो में खोलना चाहते हैं ताकि आप U2F का उपयोग करके लॉग इन कर सकें?"}, "enableFavicon": {"message": "वेबसाइट आइकन दिखाएं"}, "faviconDesc": {"message": "प्रत्येक लॉगिन के आगे एक पहचानने योग्य छवि दिखाएं।"}, "faviconDescAlt": {"message": "Show a recognizable image next to each login. Applies to all logged in accounts."}, "enableBadgeCounter": {"message": "बैज काउंटर दिखाएं"}, "badgeCounterDesc": {"message": "इंगित करें कि आपके पास वर्तमान वेब पेज के लिए कितने लॉगिन हैं।"}, "cardholderName": {"message": "Cardholder Name"}, "number": {"message": "संख्या"}, "brand": {"message": "ब्रांड"}, "expirationMonth": {"message": "Expiration Month"}, "expirationYear": {"message": "Expiration Year"}, "expiration": {"message": "समय सीमा समाप्ति"}, "january": {"message": "<PERSON><PERSON><PERSON><PERSON>ी"}, "february": {"message": "फरवरी"}, "march": {"message": "मार<PERSON><PERSON>"}, "april": {"message": "अप्रैल"}, "may": {"message": "मई"}, "june": {"message": "जून"}, "july": {"message": "जुलाई"}, "august": {"message": "अगस्त"}, "september": {"message": "सितम्बर"}, "october": {"message": "अक्टूबर"}, "november": {"message": "नवं<PERSON>र"}, "december": {"message": "दि<PERSON><PERSON><PERSON>र"}, "securityCode": {"message": "Security Code"}, "ex": {"message": "ex."}, "title": {"message": "शीर्षक"}, "mr": {"message": "श्री"}, "mrs": {"message": "श्रीमती"}, "ms": {"message": "श्रीमती"}, "dr": {"message": "डॉ"}, "mx": {"message": "Mx"}, "firstName": {"message": "First Name"}, "middleName": {"message": "Middle Name"}, "lastName": {"message": "Last Name"}, "fullName": {"message": "पूरा नाम"}, "identityName": {"message": "पह<PERSON><PERSON>न का नाम"}, "company": {"message": "कंपनी"}, "ssn": {"message": "Social Security Number"}, "passportNumber": {"message": "Passport Number"}, "licenseNumber": {"message": "License Number"}, "email": {"message": "ईमेल"}, "phone": {"message": "फोन"}, "address": {"message": "पता"}, "address1": {"message": "पता 1"}, "address2": {"message": "पता 2"}, "address3": {"message": "पता 3"}, "cityTown": {"message": "City / Town"}, "stateProvince": {"message": "State / Province"}, "zipPostalCode": {"message": "Zip / Postal Code"}, "country": {"message": "देश"}, "type": {"message": "प्र<PERSON><PERSON>र"}, "typeLogin": {"message": "लॉग इन"}, "typeLogins": {"message": "लॉग इन"}, "typeSecureNote": {"message": "Secure Note"}, "typeCard": {"message": "कार्ड"}, "typeIdentity": {"message": "पह<PERSON><PERSON>न"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "नया $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "$TYPE$ संपादन", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "View $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "पासवर्ड इतिहास"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "वापस जाएं"}, "collections": {"message": "संग्रह"}, "nCollections": {"message": "$COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "Favorites"}, "popOutNewWindow": {"message": "एक नई विंडो के लिए पॉप आउट करें"}, "refresh": {"message": "रीफ्रेश करें"}, "cards": {"message": "कार्ड्स"}, "identities": {"message": "पह<PERSON><PERSON>न"}, "logins": {"message": "लॉग इन"}, "secureNotes": {"message": "Secure Notes"}, "sshKeys": {"message": "SSH कुँजी"}, "clear": {"message": "खाली करें", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "चेक करें कि पासवर्ड सामने आ गया है या नहीं।"}, "passwordExposed": {"message": "This password has been exposed $VALUE$ time(s) in data breaches. You should change it.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "यह पासवर्ड किसी भी ज्ञात डेटा उल्लंघनों में नहीं पाया गया था।इसका उपयोग करना सुरक्षित होना चाहिए।"}, "baseDomain": {"message": "बेस डोमेन", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Base domain (recommended)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "डोमेन नाम", "description": "Domain name. Ex. website.com"}, "host": {"message": "मेज<PERSON><PERSON>न", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "सटीक"}, "startsWith": {"message": "इससे शुरू होता है"}, "regEx": {"message": "नियमित अभिव्यक्ति", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Match Detection", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "डिफॉल्ट मैच डिटेक्शन", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Toggle Options"}, "toggleCurrentUris": {"message": "वर्तमान URI's को टॉगल करें", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "वर्तमान URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Organization", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "प्र<PERSON><PERSON>र"}, "allItems": {"message": "All Items"}, "noPasswordsInList": {"message": "सूची के लिए कोई पासवर्ड नहीं हैं।"}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "हटाएं"}, "default": {"message": "डिफॉल्ट"}, "dateUpdated": {"message": "अपडेट किया गया", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "बनाया था", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "Password Updated", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "क्या आप सुनिश्चित हैं कि आप \"कभी नहीं\" विकल्प का उपयोग करना चाहते हैं?\"कभी नहीं\" के लिए अपने लॉक विकल्प को सेट करना आपके डिवाइस पर आपकी वॉल्ट की एन्क्रिप्शन कुंजी को स्टोर करता है। यदि आप इस विकल्प का उपयोग करते हैं तो आपको यह सुनिश्चित करना चाहिए कि आप अपने डिवाइस को ठीक से सुरक्षित रखें।"}, "noOrganizationsList": {"message": "You do not belong to any organizations. Organizations allow you to securely share items with other users."}, "noCollectionsInList": {"message": "सूची में कोई संग्रह नहीं है।"}, "ownership": {"message": "मा<PERSON><PERSON><PERSON>ी"}, "whoOwnsThisItem": {"message": "इस आइटम का मालिक कौन है?"}, "strong": {"message": "मजबूत", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "अच्छा", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "कम<PERSON>ोर", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "Weak Master Password"}, "weakMasterPasswordDesc": {"message": "आपके द्वारा चुना गया मास्टर पासवर्ड कमजोर है। आपको अपने बिटवर्डन खाते की ठीक से सुरक्षा के लिए एक मजबूत मास्टर पासवर्ड (या पासवाफ्रेज़) का उपयोग करना चाहिए।क्या आप सुनिश्चित हैं कि आप इस मास्टर पासवर्ड का उपयोग करना चाहते हैं?"}, "pin": {"message": "पिन", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "पिन से अनलॉक करें "}, "setYourPinTitle": {"message": "पिन सेट करें"}, "setYourPinButton": {"message": "पिन सेट करें"}, "setYourPinCode": {"message": "बिटवर्डन को अनलॉक करने के लिए अपना पिन कोड सेट करें यदि आप कभी भी आवेदन से पूरी तरह लॉग आउट करते हैं तो आपकी पिन सेटिंग्स रीसेट हो जाएंगी।"}, "setYourPinCode1": {"message": "बिटवर्डन को अनलॉक करने के लिए आपके मास्टर पासवर्ड के बजाय आपके पिन का उपयोग किया जाएगा। यदि आप कभी भी बिटवर्डन से पूरी तरह लॉग आउट हो जाते हैं तो आपका पिन रीसेट हो जाएगा।"}, "pinRequired": {"message": "पिन-कोड आवश्यक है |"}, "invalidPin": {"message": "अमान्य पिन कोड।"}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Too many invalid PIN entry attempts. Logging out."}, "unlockWithBiometrics": {"message": "बायोमेट्रिक्स का उपयोग कर अनलॉक करें"}, "unlockWithMasterPassword": {"message": "मास्टर पासवर्ड से खोलें"}, "awaitDesktop": {"message": "डेस्कटॉप से पुष्टि का इंतजार"}, "awaitDesktopDesc": {"message": "ब्राउज़र के लिए बॉयोमीट्रिक्स सक्षम करने के लिए Bitwarden डेस्कटॉप आवेदन में बॉयोमीट्रिक्स का उपयोग कर पुष्टि करें।"}, "lockWithMasterPassOnRestart": {"message": "ब्राउज़र पुनः आरंभ करने पर मास्टर पासवर्ड के साथ लॉक करें"}, "lockWithMasterPassOnRestart1": {"message": "ब्राउज़र पुनरारंभ पर मास्टर पासवर्ड की आवश्यकता है"}, "selectOneCollection": {"message": "आपको कम से कम एक संग्रह का चयन करना होगा।"}, "cloneItem": {"message": "क्लोन आइटम"}, "clone": {"message": "क्लोन"}, "passwordGenerator": {"message": "Password generator"}, "usernameGenerator": {"message": "Username generator"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "Use this password"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "वॉल्ट मध्यांतर कार्रवाई"}, "vaultTimeoutAction1": {"message": "टाइमआउट कार्रवाई"}, "newCustomizationOptionsCalloutTitle": {"message": "New customization options"}, "newCustomizationOptionsCalloutContent": {"message": "Customize your vault experience with quick copy actions, compact mode, and more!"}, "newCustomizationOptionsCalloutLink": {"message": "View all Appearance settings"}, "lock": {"message": "लॉक", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "थ्रैश", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "थ्रैश में खोजें"}, "permanentlyDeleteItem": {"message": "स्थायी रूप से आइटम हटाएं"}, "permanentlyDeleteItemConfirmation": {"message": "क्या आप सुनिश्चित हैं कि आप इस आइटम को स्थायी रूप से हटाना चाहते हैं?"}, "permanentlyDeletedItem": {"message": "स्थायी रूप से आइटम हटाएं"}, "restoreItem": {"message": "आइटम बहाल करें"}, "restoredItem": {"message": "बहाल आइटम"}, "alreadyHaveAccount": {"message": "Already have an account?"}, "vaultTimeoutLogOutConfirmation": {"message": "लॉग आउट करने से वॉल्टमें प्रवेश संभव नहीं होगा और समय समाप्त होने के बाद ऑनलाइन प्रमाणीकरण की आश्यकता होगी। आप इस सेटिंग्स को प्रयोग करने के लिए विश्वस्त हैं?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "मध्यांतर कार्रवाई की पुष्टि"}, "autoFillAndSave": {"message": "ऑटो फिल और सेव"}, "fillAndSave": {"message": "Fill and save"}, "autoFillSuccessAndSavedUri": {"message": "ऑटो फिल आइटम और सेव URI"}, "autoFillSuccess": {"message": "ऑटो फिल आइटम"}, "insecurePageWarning": {"message": "Warning: This is an unsecured HTTP page, and any information you submit can potentially be seen and changed by others. This Login was originally saved on a secure (HTTPS) page."}, "insecurePageWarningFillPrompt": {"message": "Do you still wish to fill this login?"}, "autofillIframeWarning": {"message": "The form is hosted by a different domain than the URI of your saved login. <PERSON>ose OK to autofill anyway, or Cancel to stop."}, "autofillIframeWarningTip": {"message": "To prevent this warning in the future, save this URI, $HOSTNAME$, to your Bitwarden login item for this site.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "मास्टर पासवर्ड सेट करें"}, "currentMasterPass": {"message": "Current master password"}, "newMasterPass": {"message": "New master password"}, "confirmNewMasterPass": {"message": "Confirm new master password"}, "masterPasswordPolicyInEffect": {"message": "एक या एक से अधिक संगठन नीतियों को निम्नलिखित आवश्यकताओं को पूरा करने के लिए आपके मास्टर पासवर्ड की आवश्यकता होती है:"}, "policyInEffectMinComplexity": {"message": "$SCORE$ का न्यूनतम जटिलता स्कोर", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "$LENGTH$ की न्यूनतम लंबाई", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "एक या एक से अधिक ऊपरी अक्षर रखे"}, "policyInEffectLowercase": {"message": "एक या एक से अधिक लोअरकेस अक्षर रखे"}, "policyInEffectNumbers": {"message": "एक या अधिक संख्या रखे"}, "policyInEffectSpecial": {"message": "निम्नलिखित विशेष पात्रों में से एक या अधिक शामिल रखे $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "आपका नया मास्टर पासवर्ड पॉलिसी आवश्यकताओं को पूरा नहीं करता है।"}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Unsubscribe"}, "atAnyTime": {"message": "at any time."}, "byContinuingYouAgreeToThe": {"message": "By continuing, you agree to the"}, "and": {"message": "and"}, "acceptPolicies": {"message": "इस बॉक्स की जांच करके आप निम्नलिखित से सहमत हैं:"}, "acceptPoliciesRequired": {"message": "सेवा की शर्तों और गोपनीयता नीति को स्वीकार नहीं किया गया है।"}, "termsOfService": {"message": "सेवा की शर्तें"}, "privacyPolicy": {"message": "प्राइवेसी पोलिसी"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "आपका पासवर्ड संकेत आपके पासवर्ड के समान नहीं हो सकता है।"}, "ok": {"message": "ठीक है"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "डेस्कटॉप सिंक सत्यापन"}, "desktopIntegrationVerificationText": {"message": "कृपया सत्यापित करें कि डेस्कटॉप एप्लिकेशन इस फिंगरप्रिंट को दिखाता है:"}, "desktopIntegrationDisabledTitle": {"message": "ब्राउज़र एकीकरण सक्षम नहीं है"}, "desktopIntegrationDisabledDesc": {"message": "ब्राउज़र एकीकरण बिटवर्डन डेस्कटॉप एप्लिकेशन में सक्षम नहीं है।कृपया इसे डेस्कटॉप एप्लिकेशन के भीतर सेटिंग्स में सक्षम करें।"}, "startDesktopTitle": {"message": "बिटवर्डन डेस्कटॉप एप्लिकेशन शुरू करें"}, "startDesktopDesc": {"message": "इस फ़ंक्शन का उपयोग करने से पहले बिटवर्डन डेस्कटॉप एप्लिकेशन को शुरू करने की आवश्यकता है।"}, "errorEnableBiometricTitle": {"message": "बॉयोमीट्रिक्स सक्षम करने में असमर्थ"}, "errorEnableBiometricDesc": {"message": "डेस्कटॉप एप्लिकेशन द्वारा कार्रवाई रद्द कर दी गई थी"}, "nativeMessagingInvalidEncryptionDesc": {"message": "डेस्कटॉप एप्लिकेशन ने सुरक्षित संचार चैनल को अमान्य कर दिया। कृपया इस ऑपरेशन को फिर से प्रयास करें"}, "nativeMessagingInvalidEncryptionTitle": {"message": "डेस्कटॉप संचार बाधित"}, "nativeMessagingWrongUserDesc": {"message": "डेस्कटॉप एप्लिकेशन को एक अलग खाते में लॉग इन किया जाता है। कृपया सुनिश्चित करें कि दोनों आवेदन एक ही खाते में लॉग इन किए गए हैं।"}, "nativeMessagingWrongUserTitle": {"message": "खाता गलत मैच"}, "nativeMessagingWrongUserKeyTitle": {"message": "बेमेल बायोमेट्रिक कुंजी"}, "nativeMessagingWrongUserKeyDesc": {"message": "बायोमेट्रिक अनलॉक विफल रहा. बायोमेट्रिक गुप्त कुंजी वॉल्ट को अनलॉक करने में विफल रही. कृपया बायोमेट्रिक्स को फिर से सेट करने का प्रयास करें."}, "biometricsNotEnabledTitle": {"message": "बॉयोमीट्रिक्स सक्षम नहीं  है"}, "biometricsNotEnabledDesc": {"message": "ब्राउज़र बॉयोमीट्रिक्स पहले सेटिंग्स में सक्षम होने के लिए डेस्कटॉप बॉयोमीट्रिक की आवश्यकता है ।"}, "biometricsNotSupportedTitle": {"message": "बॉयोमीट्रिक्स सक्षम नहीं  है"}, "biometricsNotSupportedDesc": {"message": "ब्राउज़र बॉयोमीट्रिक्स इस डिवाइस पर समर्थित नहीं है।"}, "biometricsNotUnlockedTitle": {"message": "User locked or logged out"}, "biometricsNotUnlockedDesc": {"message": "Please unlock this user in the desktop application and try again."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "Biometrics failed"}, "biometricsFailedDesc": {"message": "Biometrics cannot be completed, consider using a master password or logging out. If this persists, please contact Bitwarden support."}, "nativeMessaginPermissionErrorTitle": {"message": "अनुमति नहीं दी गयी है"}, "nativeMessaginPermissionErrorDesc": {"message": "बिटवर्डन डेस्कटॉप एप्लिकेशन के साथ संवाद करने की अनुमति के बिना हम ब्राउज़र एक्सटेंशन में बॉयोमीट्रिक्स प्रदान नहीं कर सकते हैं।कृपया फिर से प्रयास करें।"}, "nativeMessaginPermissionSidebarTitle": {"message": "अनुमति अनुरोध त्रुटि"}, "nativeMessaginPermissionSidebarDesc": {"message": "यह क्रिया साइडबार में नहीं की जा सकती है, कृपया पॉपअप या पॉपआउट में कार्रवाई का फिर से प्रयास करें।"}, "personalOwnershipSubmitError": {"message": "एंटरप्राइज पॉलिसी के कारण, आप अपनी व्यक्तिगत वॉल्ट में वस्तुओं को सहेजने से प्रतिबंधित हैं।किसी संगठन के स्वामित्व विकल्प को बदलें और उपलब्ध संग्रहों में से चुनें।"}, "personalOwnershipPolicyInEffect": {"message": "एक संगठन नीति आपके स्वामित्व विकल्पों को प्रभावित कर रही है।"}, "personalOwnershipPolicyInEffectImports": {"message": "An organization policy has blocked importing items into your individual vault."}, "domainsTitle": {"message": "Domains", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "बहिष्कृत डोमेन"}, "excludedDomainsDesc": {"message": "बिटवर्डन इन डोमेन के लिए लॉगिन विवरण सहेजने के लिए नहीं कहेगा।परिवर्तनों को प्रभावी बनाने के लिए आपको पृष्ठ को ताज़ा करना होगा |"}, "excludedDomainsDescAlt": {"message": "Bitwarden will not ask to save login details for these domains for all logged in accounts. You must refresh the page for changes to take effect."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "जोखिमग्रस्त लॉगिन की सूची का चित्रण।"}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "उत्पन्न पासवर्ड प्रदर्शित करने वाले बिटवर्डन स्वतः भरण मेनू का चित्रण।"}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "उपयोगकर्ता को लॉगिन अपडेट करने के लिए प्रेरित करने वाली बिटवर्डन की अधिसूचना का चित्रण।"}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ is not a valid domain", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "भेजें", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "शब<PERSON>द"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "फ़ाइल"}, "allSends": {"message": "सभी Sends", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": " गतावधिक"}, "passwordProtected": {"message": "पासवर्ड सुरक्षित है"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "कॉपी Send लिंक", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "पासवर्ड हटाएं"}, "delete": {"message": "हटाएं"}, "removedPassword": {"message": "पासवर्ड हटाएं"}, "deletedSend": {"message": " Send हटाए गए", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Send लिंक", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "अक्षम"}, "removePasswordConfirmation": {"message": "क्या आप सयमुच पासवर्ड हटाना चाहते हैं?"}, "deleteSend": {"message": " Send हटाए", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "क्या आप वाकई इस Send को मिटाना चाहते हैं?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "क्या आप इस प्रेषित संदेश को स्थायी रूप से हटाना चाहते हैं?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "एडिट Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "हटाने की तारीख"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "समाप्ति तिथि"}, "oneDay": {"message": "1 दिन"}, "days": {"message": "$DAYS$ days", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "कस्टम"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "नया सेंड बनाएं", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "नया पासवर्ड"}, "sendDisabled": {"message": "सेंड अक्षम", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "एक उद्यम नीति के कारण, आप केवल मौजूदा सेंड को हटाने में सक्षम हैं।", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "नया सेंड बनाया गया", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "सेंड एडिट किया गया", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "फ़ाइल चुनने के लिए, साइडबार (यदि संभव हो) में एक्सटेंशन खोलें या इस बैनर पर क्लिक करके एक नई विंडो को पॉप आउट करें।"}, "sendFirefoxFileWarning": {"message": "फ़ायरफ़ॉक्स का उपयोग करके फ़ाइल चुनने के लिए, साइडबार में एक्सटेंशन खोलें या इस बैनर पर क्लिक करके एक नई विंडो को पॉप आउट करें।"}, "sendSafariFileWarning": {"message": "सफारी का उपयोग करके फ़ाइल चुनने के लिए, इस बैनर पर क्लिक करके एक नई विंडो को पॉप आउट करें।"}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "शुरू करने से पहले"}, "expirationDateIsInvalid": {"message": "प्रदान की गई समाप्ति तिथि मान्य नहीं है।"}, "deletionDateIsInvalid": {"message": "प्रदान की गई विलोपन तिथि मान्य नहीं है।"}, "expirationDateAndTimeRequired": {"message": "एक समाप्ति तिथि और समय की आवश्यकता है।"}, "deletionDateAndTimeRequired": {"message": "एक विलोपन तिथि और समय की आवश्यकता है।"}, "dateParsingError": {"message": "आपके विलोपन और समाप्ति तिथियों को सहेजने में एक त्रुटि थी।"}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "मास्टर पासवर्ड रि-प्रॉम्प्ट"}, "passwordConfirmation": {"message": "मास्टर पासवर्ड पुष्टि"}, "passwordConfirmationDesc": {"message": "यह क्रिया सुरक्षित है। जारी रखने के लिए, कृपया अपनी पहचान सत्यापित करने के लिए अपना मास्टर पासवर्ड फिर से दर्ज करें।"}, "emailVerificationRequired": {"message": "ईमेल सत्यापन आवश्यक है"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerificationRequiredDesc": {"message": "इस सुविधा का उपयोग करने के लिए आपको अपने ईमेल को सत्यापित करना होगा। आप वेब वॉल्ट में अपने ईमेल को सत्यापित कर सकते हैं।"}, "updatedMasterPassword": {"message": "अपडेट किया गया मास्टर पासवर्ड"}, "updateMasterPassword": {"message": "मास्टर पासवर्ड अपडेट करें"}, "updateMasterPasswordWarning": {"message": "आपका मास्टर पासवर्ड हाल ही में आपके संगठन के एक व्यवस्थापक द्वारा बदल दिया गया था। तिजोरी तक पहुँचने के लिए, आपको इसे अभी अपडेट करना होगा। ये कार्यवाही आपको अपने वर्तमान सत्र से लॉग आउट कर देगी, जिसके लिए आपको वापस लॉग इन करने की आवश्यकता होगी। अन्य उपकरणों पर सक्रिय सत्र एक घंटे तक सक्रिय रह सकते हैं।"}, "updateWeakMasterPasswordWarning": {"message": "Your master password does not meet one or more of your organization policies. In order to access the vault, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "tdeDisabledMasterPasswordRequired": {"message": "आपके संगठन ने विश्वसनीय डिवाइस एन्क्रिप्शन अक्षम कर दिया है. कृपया अपने वॉल्ट तक पहुँचने के लिए मास्टर पासवर्ड सेट करें."}, "resetPasswordPolicyAutoEnroll": {"message": "स्वचालित नामांकन"}, "resetPasswordAutoEnrollInviteWarning": {"message": "इस संगठन की एक उद्यम नीति है जो स्वचालित रूप से आपको पासवर्ड रीसेट में नामांकित करेगी। नामांकन संगठन प्रशासकों को आपका मास्टर पासवर्ड बदलने की अनुमति देगा।"}, "selectFolder": {"message": "फ़ोल्डर चुनें..."}, "noFoldersFound": {"message": "No folders found", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "कुल $TOTAL$ में से", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Verification required", "description": "Default title for the user verification dialog."}, "hours": {"message": "घंटे"}, "minutes": {"message": "मिनट"}, "vaultTimeoutPolicyAffectingOptions": {"message": "एंटरप्राइज़ नीति आवश्यकताएँ आपके टाइमआउट विकल्पों पर लागू की गई हैं"}, "vaultTimeoutPolicyInEffect": {"message": "आपकी संगठन नीतियां आपके तिजोरी टाइमआउट को प्रभावित कर रही हैं। अधिकतम अनुमत तिजोरी टाइमआउट $HOURS$ घंटे और $MINUTES$ मिनट है", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "अधिकतम $HOURS$ घंटे और $MINUTES$ मिनट)।", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "टाइमआउट आपके संगठन द्वारा निर्धारित प्रतिबंध से अधिक है: अधिकतम $HOURS$ घंटे और $MINUTES$ मिनट", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Your organization policies are affecting your vault timeout. Maximum allowed vault timeout is $HOURS$ hour(s) and $MINUTES$ minute(s). Your vault timeout action is set to $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Your organization policies have set your vault timeout action to $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "आपके तिजोरी टाइमआउट का समय आपके संगठन द्वारा निर्धारित प्रतिबंधों से अधिक है।"}, "vaultExportDisabled": {"message": "तिजोरी निर्यात अनुपलब्ध"}, "personalVaultExportPolicyInEffect": {"message": "One or more organization policies prevents you from exporting your individual vault."}, "copyCustomFieldNameInvalidElement": {"message": "Unable to identify a valid form element. Try inspecting the HTML instead."}, "copyCustomFieldNameNotUnique": {"message": "No unique identifier found."}, "convertOrganizationEncryptionDesc": {"message": "$ORGANIZATION$ is using SSO with a self-hosted key server. A master password is no longer required to log in for members of this organization.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "leaveOrganization": {"message": "Leave organization"}, "removeMasterPassword": {"message": "Remove master password"}, "removedMasterPassword": {"message": "Master password removed"}, "leaveOrganizationConfirmation": {"message": "क्या आप सुनिश्चित हैं कि आप इस संगठन को छोड़ना चाहते हैं?"}, "leftOrganization": {"message": "You have left the organization."}, "toggleCharacterCount": {"message": "Toggle character count"}, "sessionTimeout": {"message": "Your session has timed out. Please go back and try logging in again."}, "exportingPersonalVaultTitle": {"message": "Exporting individual vault"}, "exportingIndividualVaultDescription": {"message": "केवल $EMAIL$ से संबद्ध, व्यक्तिगत वॉल्ट वस्तुएँ निर्यात की जाएंगी. संगठन वॉल्ट वस्तुएँ शामिल नहीं की जाएंगी. केवल वॉल्ट वस्तुओं की जानकारी निर्यात की जाएगी और इसमें संबंधित अनुलग्नक शामिल नहीं होंगे.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "केवल $EMAIL$ से जुड़े अनुलग्नकों सहित व्यक्तिगत वॉल्ट आइटम ही निर्यात किए जाएंगे. संगठन वॉल्ट आइटम शामिल नहीं किए जाएंगे", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "एरर"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "उपयोगकर्ता नाम बनाएँ"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Plus addressed email", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Use your email provider's sub-addressing capabilities."}, "catchallEmail": {"message": "Catch-all email"}, "catchallEmailDesc": {"message": "Use your domain's configured catch-all inbox."}, "random": {"message": "Random"}, "randomWord": {"message": "Random word"}, "websiteName": {"message": "वेबसाइट का नाम"}, "service": {"message": "Service"}, "forwardedEmail": {"message": "Forwarded email alias"}, "forwardedEmailDesc": {"message": "Generate an email alias with an external forwarding service."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generated by <PERSON><PERSON><PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Invalid $SERVICENAME$ domain.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Invalid $SERVICENAME$ url.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Unknown $SERVICENAME$ error occurred.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Hostname", "description": "Part of a URL."}, "apiAccessToken": {"message": "API Access Token"}, "apiKey": {"message": "API Key"}, "ssoKeyConnectorError": {"message": "Key connector error: make sure key connector is available and working correctly."}, "premiumSubcriptionRequired": {"message": "Premium subscription required"}, "organizationIsDisabled": {"message": "Organization suspended."}, "disabledOrganizationFilterError": {"message": "Items in suspended Organizations cannot be accessed. Contact your Organization owner for assistance."}, "loggingInTo": {"message": "Logging in to $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Server version"}, "selfHostedServer": {"message": "self-hosted"}, "thirdParty": {"message": "Third-party"}, "thirdPartyServerMessage": {"message": "Connected to third-party server implementation, $SERVERNAME$. Please verify bugs using the official server, or report them to the third-party server.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "last seen on: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Log in with master password"}, "newAroundHere": {"message": "New around here?"}, "rememberEmail": {"message": "ईमेल याद रखें"}, "loginWithDevice": {"message": "Log in with device"}, "fingerprintPhraseHeader": {"message": "Fingerprint phrase"}, "fingerprintMatchInfo": {"message": "Please make sure your vault is unlocked and the Fingerprint phrase matches on the other device."}, "resendNotification": {"message": "Resend notification"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "A notification has been sent to your device."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "<PERSON><PERSON> initiated"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "Exposed Master Password"}, "exposedMasterPasswordDesc": {"message": "Password found in a data breach. Use a unique password to protect your account. Are you sure you want to use an exposed password?"}, "weakAndExposedMasterPassword": {"message": "Weak and Exposed Master Password"}, "weakAndBreachedMasterPasswordDesc": {"message": "Weak password identified and found in a data breach. Use a strong and unique password to protect your account. Are you sure you want to use this password?"}, "checkForBreaches": {"message": "Check known data breaches for this password"}, "important": {"message": "आवश्यक सूचनाः"}, "masterPasswordHint": {"message": "यदि आप इसे भूल जाते हैं तो आपका मास्टर पासवर्ड पुनर्प्राप्त नहीं किया जा सकता!"}, "characterMinimum": {"message": "$LENGTH$ वर्ण न्यूनतम", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Your organization policies have turned on autofill on page load."}, "howToAutofill": {"message": "ऑटो-फ़िल कैसे करें"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "Got it"}, "autofillSettings": {"message": "Autofill settings"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Manage shortcuts"}, "autofillShortcut": {"message": "Autofill keyboard shortcut"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Default autofill shortcut: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Opens in a new window"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Device approval required. Select an approval option below:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "Remember this device"}, "uncheckIfPublicDevice": {"message": "Uncheck if using a public device"}, "approveFromYourOtherDevice": {"message": "Approve from your other device"}, "requestAdminApproval": {"message": "Request admin approval"}, "ssoIdentifierRequired": {"message": "Organization SSO identifier is required."}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Follow the link in the email sent to"}, "andContinueCreatingYourAccount": {"message": "and continue creating your account."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "Access denied. You do not have permission to view this page."}, "general": {"message": "General"}, "display": {"message": "Display"}, "accountSuccessfullyCreated": {"message": "Account successfully created!"}, "adminApprovalRequested": {"message": "Admin approval requested"}, "adminApprovalRequestSentToAdmins": {"message": "Your request has been sent to your admin."}, "troubleLoggingIn": {"message": "Trouble logging in?"}, "loginApproved": {"message": "Login approved"}, "userEmailMissing": {"message": "User email missing"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "<PERSON><PERSON> trusted"}, "trustOrganization": {"message": "विश्वसनीय संगठन"}, "trust": {"message": "भरोसा करें"}, "doNotTrust": {"message": "भरोसा न करें"}, "organizationNotTrusted": {"message": "संगठन पर भरोसा नहीं है"}, "emergencyAccessTrustWarning": {"message": "अपने खाते की सुरक्षा के लिए, केवल तभी पुष्टि करें जब आपने इस उपयोगकर्ता को आपातकालीन पहुँच प्रदान की हो और उनका फिंगरप्रिंट उनके खाते में प्रदर्शित फिंगरप्रिंट से मेल खाता हो"}, "orgTrustWarning": {"message": "अपने खाते की सुरक्षा के लिए, केवल तभी आगे बढ़ें जब आप इस संगठन के सदस्य हों, खाता पुनर्प्राप्ति सक्षम हो, तथा नीचे प्रदर्शित फिंगरप्रिंट संगठन के फिंगरप्रिंट से मेल खाता हो।"}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "उपयोगकर्ता पर भरोसा रखें"}, "sendsNoItemsTitle": {"message": "No active Sends", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsNoItemsMessage": {"message": "Use Send to securely share encrypted information with anyone.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Input is required."}, "required": {"message": "required"}, "search": {"message": "Search"}, "inputMinLength": {"message": "Input must be at least $COUNT$ characters long.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Input must not exceed $COUNT$ characters in length.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "The following characters are not allowed: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Input value must be at least $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Input value must not exceed $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 or more emails are invalid"}, "inputTrimValidator": {"message": "Input must not contain only whitespace.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Input is not an email address."}, "fieldsNeedAttention": {"message": "$COUNT$ field(s) above need your attention.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Select --"}, "multiSelectPlaceholder": {"message": "-- Type to filter --"}, "multiSelectLoading": {"message": "Retrieving options..."}, "multiSelectNotFound": {"message": "No items found"}, "multiSelectClearAll": {"message": "Clear all"}, "plusNMore": {"message": "+ $QUANTITY$ more", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Submenu"}, "toggleCollapse": {"message": "Toggle collapse", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "डोमेन उपनाम"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Items with master password re-prompt cannot be autofilled on page load. Autofill on page load turned off.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Autofill on page load set to use default setting.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Turn off master password re-prompt to edit this field", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "सीधे सामग्री पर जाएं"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden autofill menu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Unlock your account to view matching logins", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Unlock account", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Fill credentials for", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Partial username", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "No items to show", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "New item", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Add new vault item", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "New identity", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Turn on"}, "ignore": {"message": "Ignore"}, "importData": {"message": "Import data", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Import error"}, "importErrorDesc": {"message": "There was a problem with the data you tried to import. Please resolve the errors listed below in your source file and try again."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Resolve the errors below and try again."}, "description": {"message": "Description"}, "importSuccess": {"message": "Data successfully imported"}, "importSuccessNumberOfItems": {"message": "A total of $AMOUNT$ items were imported.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Try again"}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "Set PIN"}, "verifyWithBiometrics": {"message": "Verify with biometrics"}, "awaitingConfirmation": {"message": "Awaiting confirmation"}, "couldNotCompleteBiometrics": {"message": "Could not complete biometrics."}, "needADifferentMethod": {"message": "Need a different method?"}, "useMasterPassword": {"message": "Use master password"}, "usePin": {"message": "Use PIN"}, "useBiometrics": {"message": "Use biometrics"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Resend code"}, "total": {"message": "Total"}, "importWarning": {"message": "You are importing data to $ORGANIZATION$. Your data may be shared with members of this organization. Do you want to proceed?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Duo two-step login is required for your account."}, "popoutExtension": {"message": "Popout extension"}, "launchDuo": {"message": "Launch Duo"}, "importFormatError": {"message": "Data is not formatted correctly. Please check your import file and try again."}, "importNothingError": {"message": "Nothing was imported."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Learn about your import options"}, "selectImportFolder": {"message": "Select a folder"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "confirmFilePassword": {"message": "Confirm file password"}, "exportSuccess": {"message": "वॉल्ट डेटा निर्यात किया गया"}, "typePasskey": {"message": "Passkey"}, "accessing": {"message": "ऐक्सेसिंग"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "Passkey will not be copied"}, "passkeyNotCopiedAlert": {"message": "The passkey will not be copied to the cloned item. Do you want to continue cloning this item?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verification required by the initiating site. This feature is not yet implemented for accounts without master password."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "A passkey already exists for this application."}, "noPasskeysFoundForThisApplication": {"message": "No passkeys found for this application."}, "noMatchingPasskeyLogin": {"message": "You do not have a matching login for this site."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "Confirm"}, "savePasskey": {"message": "Save passkey"}, "savePasskeyNewLogin": {"message": "Save passkey as new login"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "<PERSON><PERSON> Item"}, "overwritePasskey": {"message": "Overwrite passkey?"}, "overwritePasskeyAlert": {"message": "This item already contains a passkey. Are you sure you want to overwrite the current passkey?"}, "featureNotSupported": {"message": "Feature not yet supported"}, "yourPasskeyIsLocked": {"message": "Authentication required to use passkey. Verify your identity to continue."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "Incorrect username or password"}, "incorrectPassword": {"message": "Incorrect password"}, "incorrectCode": {"message": "Incorrect code"}, "incorrectPin": {"message": "Incorrect PIN"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "LastPass Email"}, "importingYourAccount": {"message": "Importing your account..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "LastPass master password"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "हमारी सहायता वेब पृष्ठ पर विस्तृत निर्देश देखें", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Import directly from LastPass"}, "importFromCSV": {"message": "Import from CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Collection"}, "lastPassYubikeyDesc": {"message": "Insert the YubiKey associated with your LastPass account into your computer's USB port, then touch its button."}, "switchAccount": {"message": "Switch account"}, "switchAccounts": {"message": "Switch accounts"}, "switchToAccount": {"message": "Switch to account"}, "activeAccount": {"message": "Active account"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Available accounts"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "server"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Just once"}, "alwaysForThisSite": {"message": "Always for this site"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ added to excluded domains.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Make <PERSON><PERSON><PERSON> your default password manager?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ignoring this option may cause conflicts between Bitwarden autofill suggestions and your browser's.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Make Bitwarden your default password manager", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Unable to set <PERSON><PERSON><PERSON> as the default password manager", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "You must grant browser privacy permissions to Bitwarden to set it as the default password manager.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Make default", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Credentials saved successfully!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Credentials updated successfully!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Error saving credentials. Check console for details.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Success"}, "removePasskey": {"message": "Remove passkey"}, "passkeyRemoved": {"message": "Passkey removed"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "accountSecurity": {"message": "Account security"}, "notifications": {"message": "Notifications"}, "appearance": {"message": "Appearance"}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "New"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "Owner"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Additional information"}, "itemHistory": {"message": "Item history"}, "lastEdited": {"message": "Last edited"}, "ownerYou": {"message": "Owner: You"}, "linked": {"message": "Linked"}, "copySuccessful": {"message": "Copy Successful"}, "upload": {"message": "Upload"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "deleteAttachmentName": {"message": "Delete attachment $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Download $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "फ़िल्टर"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "downloadAttachment": {"message": "Download - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Login credentials"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "एनिमेशन दिखाएं"}, "addAccount": {"message": "Add account"}, "loading": {"message": "Loading"}, "data": {"message": "Data"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Add field"}, "add": {"message": "Add"}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Edit field"}, "editFieldLabel": {"message": "Edit $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Delete $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ added", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Item Location"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "इस सेटिंग पर एंटरप्राइज़ नीति आवश्यकताएँ लागू की गई हैं"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "पुन: प्रयास करें"}, "vaultCustomTimeoutMinimum": {"message": "न्यूनतम कस्टम टाइमआउट 1 मिनट है."}, "additionalContentAvailable": {"message": "अतिरिक्त सामग्री उपलब्ध है"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "Compact mode"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBody": {"message": "Autofill items for the current page\nFavorite items for easy access\nSearch your vault for something else"}, "newLoginNudgeTitle": {"message": "स्वतः भरण से समय बचाएँ"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}}