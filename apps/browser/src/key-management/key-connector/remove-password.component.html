<header>
  <div class="left"></div>
  <div class="center">
    <span class="title">{{ "removeMasterPassword" | i18n }}</span>
  </div>
  <div class="right"></div>
</header>

<main tabindex="-1">
  <div class="box">
    <div class="box-content">
      <div class="box-content-row" appBoxRow>
        <p>{{ "convertOrganizationEncryptionDesc" | i18n: organization.name }}</p>
      </div>
      <div class="box-content-row">
        <button
          type="button"
          class="btn block primary"
          (click)="convert()"
          [disabled]="actionPromise"
        >
          <i
            class="bwi bwi-spinner bwi-spin"
            title="{{ 'loading' | i18n }}"
            aria-hidden="true"
            *ngIf="continuing"
          ></i>
          {{ "removeMasterPassword" | i18n }}
        </button>
      </div>
      <div class="box-content-row">
        <button
          type="button"
          class="btn btn-outline-secondary block"
          (click)="leave()"
          [disabled]="actionPromise"
        >
          <i
            class="bwi bwi-spinner bwi-spin"
            title="{{ 'loading' | i18n }}"
            aria-hidden="true"
            *ngIf="leaving"
          ></i>
          {{ "leaveOrganization" | i18n }}
        </button>
      </div>
    </div>
  </div>
</main>
