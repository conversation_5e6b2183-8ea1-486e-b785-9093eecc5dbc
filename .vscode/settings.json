{"cSpell.words": ["Csprng", "Decapsulation", "decryptable", "Popout", "Reprompt", "takeuntil"], "search.exclude": {"**/locales/[^e]*/messages.json": true, "**/locales/*[^n]/messages.json": true, "**/_locales/[^e]*/messages.json": true, "**/_locales/*[^n]/messages.json": true}, "rust-analyzer.linkedProjects": ["apps/desktop/desktop_native/Cargo.toml"], "typescript.tsdk": "node_modules/typescript/lib", "eslint.useFlatConfig": true}