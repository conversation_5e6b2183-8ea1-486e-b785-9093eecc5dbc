{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Jest All", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true, "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}, {"type": "node", "request": "launch", "name": "Jest Current File", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${fileBasenameNoExtension}", "--config", "jest.config.js"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true, "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}]}