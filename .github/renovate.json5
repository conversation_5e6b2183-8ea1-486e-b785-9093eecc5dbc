{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  extends: ["github>bitwarden/renovate-config"], // Extends our default configuration for pinned dependencies
  enabledManagers: ["cargo", "github-actions", "npm"],
  packageRules: [
    {
      // Group all build/test/lint workflows for GitHub Actions together for Platform.
      // Since they are code owners we don't need to assign a review team in Renovate.
      // Any changes here should also be reflected in CODEOWNERS.
      groupName: "github-action",
      matchManagers: ["github-actions"],
      matchFileNames: [
        "./github/workflows/automatic-issue-responses.yml",
        "./github/workflows/automatic-pull-request-responses.yml",
        "./github/workflows/build-browser.yml",
        "./github/workflows/build-cli.yml",
        "./github/workflows/build-desktop.yml",
        "./github/workflows/build-web.yml",
        "./github/workflows/chromatic.yml",
        "./github/workflows/crowdin-pull.yml",
        "./github/workflows/enforce-labels.yml",
        "./github/workflows/lint.yml",
        "./github/workflows/locales-lint.yml",
        "./github/workflows/repository-management.yml",
        "./github/workflows/scan.yml",
        "./github/workflows/stale-bot.yml",
        "./github/workflows/test.yml",
        "./github/workflows/version-auto-bump.yml",
      ],
      commitMessagePrefix: "[deps] Platform:",
    },
    {
      // Group all release-related workflows for GitHub Actions together for BRE.
      // Since they are code owners we don't need to assign a review team in Renovate.
      // Any changes here should also be reflected in CODEOWNERS.
      groupName: "github-action",
      matchManagers: ["github-actions"],
      matchFileNames: [
        "./github/workflows/brew-bump-desktop.yml",
        "./github/workflows/deploy-web.yml",
        "./github/workflows/publish-cli.yml",
        "./github/workflows/publish-desktop.yml",
        "./github/workflows/publish-web.yml",
        "./github/workflows/retrieve-current-desktop-rollout.yml",
        "./github/workflows/staged-rollout-desktop.yml",
        "./github/workflows/release-cli.yml",
        "./github/workflows/release-desktop-beta.yml",
        "./github/workflows/release-desktop.yml",
        "./github/workflows/release-web.yml",
      ],
      commitMessagePrefix: "[deps] BRE:",
      addLabels: ["hold"],
    },
    {
      // By default, we send patch updates to the Dependency Dashboard and do not generate a PR.
      // We want to generate PRs for a select number of dependencies to ensure we stay up to date on these.
      matchPackageNames: ["browserslist", "electron", "rxjs", "typescript", "webpack"],
      matchUpdateTypes: ["patch"],
      dependencyDashboardApproval: false,
    },
    {
      // Disable major and minor updates for TypeScript and Zone.js because they are managed by Angular.
      matchPackageNames: ["typescript", "zone.js"],
      matchUpdateTypes: ["major", "minor"],
      description: "Determined by Angular",
      enabled: false,
    },
    {
      // Disable major updates for core Angular dependencies because they are managed through ng update
      // when we decide to upgrade.
      matchSourceUrls: [
        "https://github.com/angular-eslint/angular-eslint",
        "https://github.com/angular/angular-cli",
        "https://github.com/angular/angular",
        "https://github.com/angular/components",
        "https://github.com/ng-select/ng-select",
      ],
      matchUpdateTypes: ["major"],
      description: "Manually updated using ng update",
      enabled: false,
    },
    {
      // Renovate should manage patch updates for TypeScript and Zone.js, despite ignoring major and minor.
      matchPackageNames: ["typescript", "zone.js"],
      matchUpdateTypes: "patch",
    },
    {
      // We want to update all the Jest-related packages together, to reduce PR noise.
      groupName: "jest",
      matchPackageNames: ["@types/jest", "jest", "ts-jest", "jest-preset-angular"],
    },
    {
      // We need to group all napi-related packages together to avoid build errors caused by version incompatibilities.
      groupName: "napi",
      matchPackageNames: ["napi", "napi-build", "napi-derive"],
    },
    {
      // We need to group all macOS/iOS binding-related packages together to avoid build errors caused by version incompatibilities.
      groupName: "macOS/iOS bindings",
      matchPackageNames: ["core-foundation", "security-framework", "security-framework-sys"],
    },
    {
      // We need to group all zbus-related packages together to avoid build errors caused by version incompatibilities.
      groupName: "zbus",
      matchPackageNames: ["zbus", "zbus_polkit"],
    },
    {
      matchPackageNames: [
        "base64-loader",
        "buffer",
        "bufferutil",
        "core-js",
        "css-loader",
        "html-loader",
        "mini-css-extract-plugin",
        "postcss",
        "postcss-loader",
        "process",
        "sass",
        "sass-loader",
        "style-loader",
        "ts-loader",
        "url",
        "util",
      ],
      description: "Admin Console owned dependencies",
      commitMessagePrefix: "[deps] AC:",
      reviewers: ["team:team-admin-console-dev"],
    },
    {
      matchPackageNames: ["qrious"],
      description: "Auth owned dependencies",
      commitMessagePrefix: "[deps] Auth:",
      reviewers: ["team:team-auth-dev"],
    },
    {
      matchPackageNames: [
        "@angular-eslint/schematics",
        "@typescript-eslint/rule-tester",
        "@typescript-eslint/utils",
        "angular-eslint",
        "eslint-config-prettier",
        "eslint-import-resolver-typescript",
        "eslint-plugin-import",
        "eslint-plugin-rxjs-angular",
        "eslint-plugin-rxjs",
        "eslint-plugin-storybook",
        "eslint-plugin-tailwindcss",
        "eslint",
        "husky",
        "lint-staged",
        "typescript-eslint",
      ],
      description: "Architecture owned dependencies",
      commitMessagePrefix: "[deps] Architecture:",
      reviewers: ["team:dept-architecture"],
    },
    {
      matchPackageNames: [
        "@angular-eslint/schematics",
        "@typescript-eslint/rule-tester",
        "@typescript-eslint/utils",
        "angular-eslint",
        "eslint-config-prettier",
        "eslint-import-resolver-typescript",
        "eslint-plugin-import",
        "eslint-plugin-rxjs-angular",
        "eslint-plugin-rxjs",
        "eslint-plugin-storybook",
        "eslint-plugin-tailwindcss",
        "eslint",
        "husky",
        "lint-staged",
        "typescript-eslint",
      ],
      groupName: "Linting minor-patch",
      matchUpdateTypes: ["minor", "patch"],
    },
    {
      matchPackageNames: [
        "@emotion/css",
        "@webcomponents/custom-elements",
        "bitwarden-russh",
        "bytes",
        "concurrently",
        "cross-env",
        "del",
        "ed25519",
        "lit",
        "@lit-labs/signals",
        "patch-package",
        "pkcs8",
        "prettier",
        "prettier-plugin-tailwindcss",
        "rimraf",
        "ssh-encoding",
        "ssh-key",
        "@storybook/web-components-webpack5",
        "tabbable",
        "tldts",
        "wait-on",
      ],
      description: "Autofill owned dependencies",
      commitMessagePrefix: "[deps] Autofill:",
      reviewers: ["team:team-autofill-dev"],
    },
    {
      matchPackageNames: ["braintree-web-drop-in"],
      description: "Billing owned dependencies",
      commitMessagePrefix: "[deps] Billing:",
      reviewers: ["team:team-billing-dev"],
    },
    {
      matchPackageNames: [
        "@babel/core",
        "@babel/preset-env",
        "@bitwarden/sdk-internal",
        "@electron/fuses",
        "@electron/notarize",
        "@electron/rebuild",
        "@ngtools/webpack",
        "@types/chrome",
        "@types/firefox-webext-browser",
        "@types/glob",
        "@types/jquery",
        "@types/lowdb",
        "@types/node",
        "@types/node-forge",
        "@types/node-ipc",
        "@yao-pkg/pkg",
        "anyhow",
        "arboard",
        "babel-loader",
        "base64",
        "bindgen",
        "browserslist",
        "byteorder",
        "bytes",
        "core-foundation",
        "copy-webpack-plugin",
        "dirs",
        "electron",
        "electron-builder",
        "electron-log",
        "electron-reload",
        "electron-store",
        "electron-updater",
        "embed_plist",
        "futures",
        "hex",
        "homedir",
        "html-webpack-injector",
        "html-webpack-plugin",
        "interprocess",
        "json5",
        "keytar",
        "libc",
        "log",
        "lowdb",
        "napi",
        "napi-build",
        "napi-derive",
        "node-forge",
        "node-ipc",
        "nx",
        "oo7",
        "oslog",
        "pin-project",
        "pkg",
        "rand",
        "rxjs",
        "scopeguard",
        "security-framework",
        "security-framework-sys",
        "serde",
        "serde_json",
        "simplelog",
        "sysinfo",
        "tsconfig-paths-webpack-plugin",
        "type-fest",
        "typenum",
        "typescript",
        "typescript-strict-plugin",
        "uniffi",
        "webpack",
        "webpack-cli",
        "webpack-dev-server",
        "webpack-node-externals",
        "widestring",
        "windows",
        "windows-future",
        "windows-registry",
        "zbus",
        "zbus_polkit",
      ],
      description: "Platform owned dependencies",
      commitMessagePrefix: "[deps] Platform:",
      reviewers: ["team:team-platform-dev"],
    },
    {
      matchPackageNames: [
        "@angular-devkit/build-angular",
        "@angular/animations",
        "@angular/cdk",
        "@angular/cli",
        "@angular/common",
        "@angular/compiler-cli",
        "@angular/compiler",
        "@angular/core",
        "@angular/forms",
        "@angular/platform-browser-dynamic",
        "@angular/platform-browser",
        "@angular/platform",
        "@angular/router",
        "@compodoc/compodoc",
        "@ng-select/ng-select",
        "@storybook/addon-a11y",
        "@storybook/addon-actions",
        "@storybook/addon-designs",
        "@storybook/addon-essentials",
        "@storybook/addon-interactions",
        "@storybook/addon-links",
        "@storybook/addon-themes",
        "@storybook/angular",
        "@storybook/manager-api",
        "@storybook/theming",
        "@types/react",
        "autoprefixer",
        "bootstrap",
        "chromatic",
        "jquery",
        "ngx-toastr",
        "popper.js",
        "react",
        "react-dom",
        "remark-gfm",
        "storybook",
        "tailwindcss",
        "zone.js",
      ],
      description: "UI Foundation owned dependencies",
      commitMessagePrefix: "[deps] UI Foundation:",
      reviewers: ["team:team-ui-foundation"],
    },
    {
      matchPackageNames: [
        "@types/jest",
        "jest-junit",
        "jest-mock-extended",
        "jest-preset-angular",
        "jest-diff",
        "ts-jest",
      ],
      description: "Secrets Manager owned dependencies",
      commitMessagePrefix: "[deps] SM:",
      reviewers: ["team:team-secrets-manager-dev"],
    },
    {
      matchPackageNames: [
        "@microsoft/signalr-protocol-msgpack",
        "@microsoft/signalr",
        "@types/jsdom",
        "@types/papaparse",
        "@types/zxcvbn",
        "jsdom",
        "jszip",
        "oidc-client-ts",
        "papaparse",
        "utf-8-validate",
        "zxcvbn",
      ],
      description: "Tools owned dependencies",
      commitMessagePrefix: "[deps] Tools:",
      reviewers: ["team:team-tools-dev"],
    },
    {
      matchPackageNames: [
        "@koa/multer",
        "@koa/router",
        "@types/inquirer",
        "@types/koa",
        "@types/koa__multer",
        "@types/koa__router",
        "@types/koa-bodyparser",
        "@types/koa-json",
        "@types/lunr",
        "@types/node-fetch",
        "@types/proper-lockfile",
        "@types/retry",
        "chalk",
        "commander",
        "form-data",
        "https-proxy-agent",
        "inquirer",
        "koa",
        "koa-bodyparser",
        "koa-json",
        "lunr",
        "multer",
        "node-fetch",
        "open",
        "proper-lockfile",
        "qrcode-parser",
      ],
      description: "Vault owned dependencies",
      commitMessagePrefix: "[deps] Vault:",
      reviewers: ["team:team-vault-dev"],
    },
    {
      matchPackageNames: [
        "@types/argon2-browser",
        "aes",
        "argon2",
        "argon2-browser",
        "big-integer",
        "cbc",
        "rsa",
        "russh-cryptovec",
        "sha2",
      ],
      description: "Key Management owned dependencies",
      commitMessagePrefix: "[deps] KM:",
      reviewers: ["team:team-key-management-dev"],
    },
  ],
  ignoreDeps: ["@types/koa-bodyparser", "bootstrap", "node-ipc", "node", "npm"],
}
