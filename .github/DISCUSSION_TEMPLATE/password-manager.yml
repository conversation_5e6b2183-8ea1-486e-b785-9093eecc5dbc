labels: ["discussions-new"]
body:
  - type: markdown
    attributes:
      value: |
        If you would like to contribute code to the Bitwarden codebase for consideration, please review [https://contributing.bitwarden.com/](https://contributing.bitwarden.com/) before posting. To keep discussion on topic, posts that do not include a proposal for a code contribution you wish to develop will be removed.
  - type: dropdown
    attributes:
      label: Select Topic Area
      description: "What would you like to discuss? :warning: For feature requests and product feedback, please visit https://community.bitwarden.com/"
      options:
        - "✅ Code Contribution Proposal"
        - "🚫 Product Feedback"
        - "🚫 Feature Request"
    validations:
      required: true
  - type: textarea
    attributes:
      label: Code Contribution Proposal
      description: "Please include a description of the code contribution you would like to contribute, including any relevant screenshots, and links to any existing [feature requests](https://community.bitwarden.com/c/feature-requests/5/none). This helps get feedback from the community and Bitwarden team members before you start writing code"
    validations:
      required: true
