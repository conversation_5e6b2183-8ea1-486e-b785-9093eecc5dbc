name: Web Bug Report
description: File a bug report
labels: [bug, web]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

        Please do not submit feature requests. The [Community Forums](https://community.bitwarden.com) has a section for submitting, voting for, and discussing product feature requests.
  - type: textarea
    id: reproduce
    attributes:
      label: Steps To Reproduce
      description: How can we reproduce the behavior.
      value: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. Click on '...'
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected Result
      description: A clear and concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    id: actual
    attributes:
      label: Actual Result
      description: A clear and concise description of what is happening.
    validations:
      required: true
  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots or Videos
      description: If applicable, add screenshots and/or a short video to help explain your problem.
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you seeing the problem on?
      multiple: true
      options:
        - Windows
        - macOS
        - Linux
        - Android
        - iOS
    validations:
      required: true
  - type: input
    id: os-version
    attributes:
      label: Operating System Version
      description: What version of the operating system(s) are you seeing the problem on?
  - type: dropdown
    id: browsers
    attributes:
      label: Web Browser
      description: What browser(s) are you seeing the problem on?
      multiple: true
      options:
        - Chrome
        - Safari
        - Microsoft Edge
        - Firefox
        - Opera
        - Brave
        - Vivaldi
    validations:
      required: true
  - type: input
    id: browser-version
    attributes:
      label: Browser Version
      description: What version of the browser(s) are you seeing the problem on?
  - type: input
    id: version
    attributes:
      label: Build Version
      description: What version of our software are you running? (Bottom of the page)
    validations:
      required: true
  - type: checkboxes
    id: issue-tracking-info
    attributes:
      label: Issue Tracking Info
      description: |
        Issue tracking information
      options:
        - label: I understand that work is tracked outside of Github. A PR will be linked to this issue should one be opened to address it, but Bitwarden doesn't use fields like "assigned", "milestone", or "project" to track progress.
