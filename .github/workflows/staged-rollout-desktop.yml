name: Staged Rollout Desktop
run-name: Staged Rollout Desktop - ${{ inputs.rollout_percentage }}%

on:
  workflow_dispatch:
    inputs:
      rollout_percentage:
        description: 'Staged Rollout Percentage'
        required: true
        default: '10'
        type: string

defaults:
  run:
    shell: bash

jobs:
  rollout:
    name: Update Rollout Percentage
    runs-on: ubuntu-22.04
    steps:
      - name: Login to Azure
        uses: Azure/login@e15b166166a8746d1a47596803bd8c1b595455cf # v1.6.0
        with:
          creds: ${{ secrets.AZURE_KV_CI_SERVICE_PRINCIPAL }}

      - name: Retrieve secrets
        id: retrieve-secrets
        uses: bitwarden/gh-actions/get-keyvault-secrets@main
        with:
          keyvault: "bitwarden-ci"
          secrets: "aws-electron-access-id,
            aws-electron-access-key,
            aws-electron-bucket-name"

      - name: Download channel update info files from S3
        env:
          AWS_ACCESS_KEY_ID: ${{ steps.retrieve-secrets.outputs.aws-electron-access-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.retrieve-secrets.outputs.aws-electron-access-key }}
          AWS_DEFAULT_REGION: 'us-west-2'
          AWS_S3_BUCKET_NAME: ${{ steps.retrieve-secrets.outputs.aws-electron-bucket-name }}
        run: |
          aws s3 cp $AWS_S3_BUCKET_NAME/desktop/latest.yml . \
          --quiet
          aws s3 cp $AWS_S3_BUCKET_NAME/desktop/latest-linux.yml . \
          --quiet
          aws s3 cp $AWS_S3_BUCKET_NAME/desktop/latest-mac.yml . \
          --quiet

      - name: Check new rollout percentage
        env:
          NEW_PCT: ${{ github.event.inputs.rollout_percentage }}
        run: |
          CURRENT_PCT=$(sed -r -n "s/stagingPercentage:\s([0-9]+)/\1/p" latest.yml)
          echo "Current percentage: ${CURRENT_PCT}"
          echo "New percentage: ${NEW_PCT}"
          echo
          if [ "$NEW_PCT" -le "$CURRENT_PCT" ]; then
            echo "New percentage (${NEW_PCT}) must be higher than current percentage (${CURRENT_PCT})!"
            echo
            echo "If you want to pull a staged release because it hasn’t gone well, you must increment the version \
                  number higher than your broken release. Because some of your users will be on the broken 1.0.1, \
                  releasing a new 1.0.1 would result in them staying on a broken version."
            exit 1
          fi

      - name: Set staged rollout percentage
        env:
          ROLLOUT_PCT: ${{ github.event.inputs.rollout_percentage }}
        run: |
          sed -i -r "/stagingPercentage/s/[0-9]+/${ROLLOUT_PCT}/" latest.yml
          sed -i -r "/stagingPercentage/s/[0-9]+/${ROLLOUT_PCT}/" latest-linux.yml
          sed -i -r "/stagingPercentage/s/[0-9]+/${ROLLOUT_PCT}/" latest-mac.yml

      - name: Publish channel update info files to S3
        env:
          AWS_ACCESS_KEY_ID: ${{ steps.retrieve-secrets.outputs.aws-electron-access-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.retrieve-secrets.outputs.aws-electron-access-key }}
          AWS_DEFAULT_REGION: 'us-west-2'
          AWS_S3_BUCKET_NAME: ${{ steps.retrieve-secrets.outputs.aws-electron-bucket-name }}
        run: |
          aws s3 cp latest.yml $AWS_S3_BUCKET_NAME/desktop/ \
          --acl "public-read"

          aws s3 cp latest-linux.yml $AWS_S3_BUCKET_NAME/desktop/ \
          --acl "public-read"

          aws s3 cp latest-mac.yml $AWS_S3_BUCKET_NAME/desktop/ \
          --acl "public-read"
