name: Chromatic

on:
  push:
    branches:
      - "main"
      - "rc"
      - "hotfix-rc"
  pull_request_target:
    types: [opened, synchronize]

jobs:
  check-run:
    name: Check PR run
    uses: bitwarden/gh-actions/.github/workflows/check-run.yml@main

  chromatic:
    name: Chromatic
    runs-on: ubuntu-22.04
    needs: check-run
    permissions:
      contents: read
      pull-requests: write

    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{  github.event.pull_request.head.sha }}
          fetch-depth: 0
        
      - name: Get changed files
        id: get-changed-files-for-chromatic
        uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        with:
          filters: |
            storyFiles: 
              - "apps/!(cli)/**"
              - "bitwarden_license/bit-web/src/app/**"
              - "libs/!(eslint)/**"
              - "package.json"
              - ".storybook/**"

      - name: Get Node version
        id: retrieve-node-version
        if: steps.get-changed-files-for-chromatic.outputs.storyFiles == 'true'
        run: |
          NODE_NVMRC=$(cat .nvmrc)
          NODE_VERSION=${NODE_NVMRC/v/''}
          echo "node_version=$NODE_VERSION" >> $GITHUB_OUTPUT

      - name: Set up Node
        uses: actions/setup-node@39370e3970a6d050c480ffad4ff0ed4d3fdee5af # v4.1.0
        with:
          node-version: ${{ steps.retrieve-node-version.outputs.node_version }}
        if: steps.get-changed-files-for-chromatic.outputs.storyFiles == 'true'

      - name: Cache NPM
        id: npm-cache
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: "~/.npm"
          key: ${{ runner.os }}-npm-chromatic-${{ hashFiles('**/package-lock.json') }}
        if: steps.get-changed-files-for-chromatic.outputs.storyFiles == 'true'

      - name: Install Node dependencies
        if: steps.get-changed-files-for-chromatic.outputs.storyFiles == 'true'
        run: npm ci

      # Manually build the Storybook to resolve a bug related to TurboSnap
      - name: Build Storybook
        if: steps.get-changed-files-for-chromatic.outputs.storyFiles == 'true'
        run: npm run build-storybook:ci

      - name: Publish to Chromatic
        uses: chromaui/action@8a12962215a66cd05b1ac5b0f1c08768d1aab155 # v11.25.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          storybookBuildDir: ./storybook-static
          exitOnceUploaded: true
          onlyChanged: true
          externals: "[\"libs/components/**/*.scss\", \"libs/components/**/*.css\", \"libs/components/tailwind.config*.js\"]"
          # Rather than use an `if` check on the whole publish step, we need to tell Chromatic to skip so that any Chromatic-spawned actions are properly skipped
          skip: ${{ steps.get-changed-files-for-chromatic.outputs.storyFiles == 'false' }}
