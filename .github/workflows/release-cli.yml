name: Release CLI
run-name: Release CLI ${{ inputs.release_type }}

on:
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release Options'
        required: true
        default: 'Initial Release'
        type: choice
        options:
          - Initial Release
          - Redeploy
          - Dry Run

defaults:
  run:
    working-directory: apps/cli

jobs:
  setup:
    name: Setup
    runs-on: ubuntu-22.04
    outputs:
      release_version: ${{ steps.version.outputs.version }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Branch check
        if: ${{ inputs.release_type != 'Dry Run' }}
        run: |
          if [[ "$GITHUB_REF" != "refs/heads/rc" ]] && [[ "$GITHUB_REF" != "refs/heads/hotfix-rc-cli" ]]; then
            echo "==================================="
            echo "[!] Can only release from the 'rc' or 'hotfix-rc-cli' branches"
            echo "==================================="
            exit 1
          fi

      - name: Check Release Version
        id: version
        uses: bitwarden/gh-actions/release-version-check@main
        with:
          release-type: ${{ inputs.release_type }}
          project-type: ts
          file: apps/cli/package.json
          monorepo: true
          monorepo-project: cli

  release:
    name: Release
    runs-on: ubuntu-22.04
    needs: setup
    steps:
      - name: Download all Release artifacts
        if: ${{ inputs.release_type != 'Dry Run' }}
        uses: bitwarden/gh-actions/download-artifacts@main
        with:
          workflow: build-cli.yml
          path: apps/cli
          workflow_conclusion: success
          branch: ${{ github.ref_name }}

      - name: Dry Run - Download all artifacts
        if: ${{ inputs.release_type == 'Dry Run' }}
        uses: bitwarden/gh-actions/download-artifacts@main
        with:
          workflow: build-cli.yml
          path: apps/cli
          workflow_conclusion: success
          branch: main

      - name: Create release
        if: ${{ inputs.release_type != 'Dry Run' }}
        uses: ncipollo/release-action@cdcc88a9acf3ca41c16c37bb7d21b9ad48560d87 # v1.15.0
        env:
          PKG_VERSION: ${{ needs.setup.outputs.release_version }}
        with:
          artifacts: "apps/cli/bw-oss-windows-${{ env.PKG_VERSION }}.zip,
                      apps/cli/bw-oss-windows-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bw-windows-${{ env.PKG_VERSION }}.zip,
                      apps/cli/bw-windows-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bw-oss-macos-${{ env.PKG_VERSION }}.zip,
                      apps/cli/bw-oss-macos-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bw-oss-macos-arm64-${{ env.PKG_VERSION }}.zip,
                      apps/cli/bw-oss-macos-arm64-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bw-macos-${{ env.PKG_VERSION }}.zip,
                      apps/cli/bw-macos-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bw-macos-arm64-${{ env.PKG_VERSION }}.zip,
                      apps/cli/bw-macos-arm64-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bw-oss-linux-${{ env.PKG_VERSION }}.zip,
                      apps/cli/bw-oss-linux-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bw-linux-${{ env.PKG_VERSION }}.zip,
                      apps/cli/bw-linux-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bitwarden-cli.${{ env.PKG_VERSION }}.nupkg,
                      apps/cli/bw_${{ env.PKG_VERSION }}_amd64.snap,
                      apps/cli/bw-snap-sha256-${{ env.PKG_VERSION }}.txt,
                      apps/cli/bitwarden-cli-${{ env.PKG_VERSION }}-npm-build.zip"
          commit: ${{ github.sha }}
          tag: cli-v${{ env.PKG_VERSION }}
          name: CLI v${{ env.PKG_VERSION }}
          body: "<insert release notes here>"
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: true
