name: Release Web
run-name: Release Web ${{ inputs.release_type }}

on:
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release Options'
        required: true
        default: 'Initial Release'
        type: choice
        options:
          - Initial Release
          - Redeploy
          - Dry Run

jobs:
  setup:
    name: Setup
    runs-on: ubuntu-22.04
    outputs:
      release_version: ${{ steps.version.outputs.version }}
      tag_version: ${{ steps.version.outputs.tag }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Branch check
        if: ${{ github.event.inputs.release_type != 'Dry Run' }}
        run: |
          if [[ "$GITHUB_REF" != "refs/heads/rc" ]] && [[ "$GITHUB_REF" != "refs/heads/hotfix-rc-web" ]]; then
            echo "==================================="
            echo "[!] Can only release from the 'rc' or 'hotfix-rc-web' branches"
            echo "==================================="
            exit 1
          fi

      - name: Check Release Version
        id: version
        uses: bitwarden/gh-actions/release-version-check@main
        with:
          release-type: ${{ github.event.inputs.release_type }}
          project-type: ts
          file: apps/web/package.json
          monorepo: true
          monorepo-project: web

  release:
    name: Create GitHub Release
    runs-on: ubuntu-22.04
    needs:
      - setup
    steps:
      - name: Download latest build artifacts
        if: ${{ github.event.inputs.release_type != 'Dry Run' }}
        uses: bitwarden/gh-actions/download-artifacts@main
        with:
          workflow: build-web.yml
          path: apps/web/artifacts
          workflow_conclusion: success
          branch: ${{ github.ref_name }}
          artifacts: "web-*-selfhosted-COMMERCIAL.zip,
            web-*-selfhosted-open-source.zip"

      - name: Dry Run - Download latest build artifacts
        if: ${{ github.event.inputs.release_type == 'Dry Run' }}
        uses: bitwarden/gh-actions/download-artifacts@main
        with:
          workflow: build-web.yml
          path: apps/web/artifacts
          workflow_conclusion: success
          branch: main
          artifacts: "web-*-selfhosted-COMMERCIAL.zip,
            web-*-selfhosted-open-source.zip"

      - name: Rename assets
        working-directory: apps/web/artifacts
        run: |
          mv web-*-selfhosted-COMMERCIAL.zip web-${{ needs.setup.outputs.release_version }}-selfhosted-COMMERCIAL.zip
          mv web-*-selfhosted-open-source.zip web-${{ needs.setup.outputs.release_version }}-selfhosted-open-source.zip

      - name: Create release
        if: ${{ github.event.inputs.release_type != 'Dry Run' }}
        uses: ncipollo/release-action@cdcc88a9acf3ca41c16c37bb7d21b9ad48560d87 # v1.15.0
        with:
          name: "Web v${{ needs.setup.outputs.release_version }}"
          commit: ${{ github.sha }}
          tag: web-v${{ needs.setup.outputs.release_version }}
          body: "<insert release notes here>"
          artifacts: "apps/web/artifacts/web-${{ needs.setup.outputs.release_version }}-selfhosted-COMMERCIAL.zip,
            apps/web/artifacts/web-${{ needs.setup.outputs.release_version }}-selfhosted-open-source.zip"
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: true
